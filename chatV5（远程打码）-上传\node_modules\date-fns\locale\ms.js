import { formatDistance } from "./ms/_lib/formatDistance.js";
import { formatLong } from "./ms/_lib/formatLong.js";
import { formatRelative } from "./ms/_lib/formatRelative.js";
import { localize } from "./ms/_lib/localize.js";
import { match } from "./ms/_lib/match.js";

/**
 * @category Locales
 * @summary Malay locale.
 * @language Malay
 * @iso-639-2 msa
 * <AUTHOR> [@Zyten](https://github.com/Zyten)
 */
export const ms = {
  code: "ms",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default ms;
