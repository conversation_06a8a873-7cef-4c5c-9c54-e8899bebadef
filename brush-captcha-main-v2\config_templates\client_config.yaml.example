# 客户端配置文件示例
# 将此文件保存为 client/config.yaml

# 并发数设置（可选，不填则自动根据系统资源计算）
# 建议根据服务器配置设置：
# - 1核1G: concurrency: 1
# - 2核2G: concurrency: 2  
# - 4核4G: concurrency: 4
# - 8核8G: concurrency: 6-8
concurrency: null

# Camoufox 参数配置
camoufox:
  # 当前设备支持的打码类型
  # HcaptchaCracker: 支持hCaptcha验证码
  # AntiTurnstileTaskProxyLess: 支持Turnstile验证码
  solver_type:
    - HcaptchaCracker
    - AntiTurnstileTaskProxyLess
  
  # 无头模式，建议保持true以节省资源
  headless: "true"

worker:
  # 当前设备名称，每台客户端服务器要设置不同的名称
  # 建议命名规则：client-01, client-02, client-03 等
  name: "client-01"
  
  # 后端API地址，替换为你的主服务器IP地址
  # 如果主服务器启用了SSL，使用wss://
  # 如果主服务器没有启用SSL，使用ws://
  # 注意：不能使用127.0.0.1或localhost，必须使用实际IP地址
  wss_url: "wss://你的主服务器IP:8080/ws/worker/"

# 其他配置示例：

# 如果只想支持Turnstile验证码：
# camoufox:
#   solver_type:
#     - AntiTurnstileTaskProxyLess
#   headless: "true"

# 如果只想支持hCaptcha验证码：
# camoufox:
#   solver_type:
#     - HcaptchaCracker
#   headless: "true"

# 如果主服务器没有启用SSL：
# worker:
#   name: "client-01"
#   wss_url: "ws://你的主服务器IP:8080/ws/worker/"

# 固定并发数示例：
# concurrency: 4
