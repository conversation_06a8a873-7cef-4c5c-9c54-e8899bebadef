# cardinal themes

These are the built in themes that come with cardinal.

You can create more themes by copying and then editing the [empty
theme](https://github.com/thlorenz/cardinal/blob/master/themes/empty.js) which is provided for that purpose.

The [hide semicolons theme](https://github.com/thlorenz/cardinal/blob/master/themes/hide-semicolons.js) has the added
benefit of making all semicolons invisible. This makes code more readable at times.

Find out how to change the theme used by cardinal [here](https://github.com/thlorenz/cardinal#theme).

# sharing themes

To add your theme to the cardinal built-in themes, follow the below steps:

1. fork the cardinal repository
2. add your theme to the themes folder and commit your changes
3. create a pull request

If you believe that your theme is better than the current default theme, let me know by creating an issue. 

This will allow others to cast their vote. If enough people agree, your theme will be promoted to be the default.

## Contributed Themes

### tomorrow night

[![tomorrow-night](https://github.com/thlorenz/cardinal/raw/master/assets/theme-tomorrow-night.png)](https://github.com/thlorenz/cardinal/blob/master/themes/tomorrow-night.js)

*by [firede](https://github.com/firede)*
