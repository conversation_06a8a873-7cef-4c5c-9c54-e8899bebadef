# Gemini API Keys 配置文件示例
# 每行一个API Key，用于hCaptcha验证码识别

# 从 Google AI Studio 获取：https://aistudio.google.com/
# 注意：这些是示例Key，请替换为你的真实API Key

AIzaSyBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
AIzaSyCxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
AIzaSyDxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
AIzaSyExxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
AIzaSyFxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# 获取Gemini API Key的步骤：
# 1. 访问 https://aistudio.google.com/
# 2. 登录Google账号
# 3. 点击 "Get API Key"
# 4. 创建新的API Key
# 5. 复制API Key到这个文件

# 注意事项：
# 1. 每个API Key都有使用限制，建议准备多个
# 2. 免费版本有请求频率限制
# 3. 如果需要高频使用，建议升级到付费版本
# 4. 保护好你的API Key，不要泄露给他人
