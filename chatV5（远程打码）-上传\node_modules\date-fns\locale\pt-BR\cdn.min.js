(()=>{var $;function U(C){return U=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},U(C)}function N(C,H){var G=Object.keys(C);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(C);H&&(J=J.filter(function(Y){return Object.getOwnPropertyDescriptor(C,Y).enumerable})),G.push.apply(G,J)}return G}function Q(C){for(var H=1;H<arguments.length;H++){var G=arguments[H]!=null?arguments[H]:{};H%2?N(Object(G),!0).forEach(function(J){z(C,J,G[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(C,Object.getOwnPropertyDescriptors(G)):N(Object(G)).forEach(function(J){Object.defineProperty(C,J,Object.getOwnPropertyDescriptor(G,J))})}return C}function z(C,H,G){if(H=A(H),H in C)Object.defineProperty(C,H,{value:G,enumerable:!0,configurable:!0,writable:!0});else C[H]=G;return C}function A(C){var H=E(C,"string");return U(H)=="symbol"?H:String(H)}function E(C,H){if(U(C)!="object"||!C)return C;var G=C[Symbol.toPrimitive];if(G!==void 0){var J=G.call(C,H||"default");if(U(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(C)}var W=Object.defineProperty,HC=function C(H,G){for(var J in G)W(H,J,{get:G[J],enumerable:!0,configurable:!0,set:function Y(X){return G[J]=function(){return X}}})},D={lessThanXSeconds:{one:"menos de um segundo",other:"menos de {{count}} segundos"},xSeconds:{one:"1 segundo",other:"{{count}} segundos"},halfAMinute:"meio minuto",lessThanXMinutes:{one:"menos de um minuto",other:"menos de {{count}} minutos"},xMinutes:{one:"1 minuto",other:"{{count}} minutos"},aboutXHours:{one:"cerca de 1 hora",other:"cerca de {{count}} horas"},xHours:{one:"1 hora",other:"{{count}} horas"},xDays:{one:"1 dia",other:"{{count}} dias"},aboutXWeeks:{one:"cerca de 1 semana",other:"cerca de {{count}} semanas"},xWeeks:{one:"1 semana",other:"{{count}} semanas"},aboutXMonths:{one:"cerca de 1 m\xEAs",other:"cerca de {{count}} meses"},xMonths:{one:"1 m\xEAs",other:"{{count}} meses"},aboutXYears:{one:"cerca de 1 ano",other:"cerca de {{count}} anos"},xYears:{one:"1 ano",other:"{{count}} anos"},overXYears:{one:"mais de 1 ano",other:"mais de {{count}} anos"},almostXYears:{one:"quase 1 ano",other:"quase {{count}} anos"}},S=function C(H,G,J){var Y,X=D[H];if(typeof X==="string")Y=X;else if(G===1)Y=X.one;else Y=X.other.replace("{{count}}",String(G));if(J!==null&&J!==void 0&&J.addSuffix)if(J.comparison&&J.comparison>0)return"em "+Y;else return"h\xE1 "+Y;return Y};function K(C){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},G=H.width?String(H.width):C.defaultWidth,J=C.formats[G]||C.formats[C.defaultWidth];return J}}var M={full:"EEEE, d 'de' MMMM 'de' y",long:"d 'de' MMMM 'de' y",medium:"d MMM y",short:"dd/MM/yyyy"},R={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},L={full:"{{date}} '\xE0s' {{time}}",long:"{{date}} '\xE0s' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},V={date:K({formats:M,defaultWidth:"full"}),time:K({formats:R,defaultWidth:"full"}),dateTime:K({formats:L,defaultWidth:"full"})},j={lastWeek:function C(H){var G=H.getDay(),J=G===0||G===6?"\xFAltimo":"\xFAltima";return"'"+J+"' eeee '\xE0s' p"},yesterday:"'ontem \xE0s' p",today:"'hoje \xE0s' p",tomorrow:"'amanh\xE3 \xE0s' p",nextWeek:"eeee '\xE0s' p",other:"P"},w=function C(H,G,J,Y){var X=j[H];if(typeof X==="function")return X(G);return X};function I(C){return function(H,G){var J=G!==null&&G!==void 0&&G.context?String(G.context):"standalone",Y;if(J==="formatting"&&C.formattingValues){var X=C.defaultFormattingWidth||C.defaultWidth,Z=G!==null&&G!==void 0&&G.width?String(G.width):X;Y=C.formattingValues[Z]||C.formattingValues[X]}else{var B=C.defaultWidth,q=G!==null&&G!==void 0&&G.width?String(G.width):C.defaultWidth;Y=C.values[q]||C.values[B]}var T=C.argumentCallback?C.argumentCallback(H):H;return Y[T]}}var _={narrow:["AC","DC"],abbreviated:["AC","DC"],wide:["antes de cristo","depois de cristo"]},f={narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["1\xBA trimestre","2\xBA trimestre","3\xBA trimestre","4\xBA trimestre"]},v={narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan","fev","mar","abr","mai","jun","jul","ago","set","out","nov","dez"],wide:["janeiro","fevereiro","mar\xE7o","abril","maio","junho","julho","agosto","setembro","outubro","novembro","dezembro"]},F={narrow:["D","S","T","Q","Q","S","S"],short:["dom","seg","ter","qua","qui","sex","sab"],abbreviated:["domingo","segunda","ter\xE7a","quarta","quinta","sexta","s\xE1bado"],wide:["domingo","segunda-feira","ter\xE7a-feira","quarta-feira","quinta-feira","sexta-feira","s\xE1bado"]},P={narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"manh\xE3",afternoon:"tarde",evening:"tarde",night:"noite"},abbreviated:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"manh\xE3",afternoon:"tarde",evening:"tarde",night:"noite"},wide:{am:"a.m.",pm:"p.m.",midnight:"meia-noite",noon:"meio-dia",morning:"manh\xE3",afternoon:"tarde",evening:"tarde",night:"noite"}},k={narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"da manh\xE3",afternoon:"da tarde",evening:"da tarde",night:"da noite"},abbreviated:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"da manh\xE3",afternoon:"da tarde",evening:"da tarde",night:"da noite"},wide:{am:"a.m.",pm:"p.m.",midnight:"meia-noite",noon:"meio-dia",morning:"da manh\xE3",afternoon:"da tarde",evening:"da tarde",night:"da noite"}},b=function C(H,G){var J=Number(H);if((G===null||G===void 0?void 0:G.unit)==="week")return J+"\xAA";return J+"\xBA"},h={ordinalNumber:b,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function C(H){return H-1}}),month:I({values:v,defaultWidth:"wide"}),day:I({values:F,defaultWidth:"wide"}),dayPeriod:I({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(C){return function(H){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=G.width,Y=J&&C.matchPatterns[J]||C.matchPatterns[C.defaultMatchWidth],X=H.match(Y);if(!X)return null;var Z=X[0],B=J&&C.parsePatterns[J]||C.parsePatterns[C.defaultParseWidth],q=Array.isArray(B)?c(B,function(x){return x.test(Z)}):m(B,function(x){return x.test(Z)}),T;T=C.valueCallback?C.valueCallback(q):q,T=G.valueCallback?G.valueCallback(T):T;var GC=H.slice(Z.length);return{value:T,rest:GC}}}function m(C,H){for(var G in C)if(Object.prototype.hasOwnProperty.call(C,G)&&H(C[G]))return G;return}function c(C,H){for(var G=0;G<C.length;G++)if(H(C[G]))return G;return}function y(C){return function(H){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=H.match(C.matchPattern);if(!J)return null;var Y=J[0],X=H.match(C.parsePattern);if(!X)return null;var Z=C.valueCallback?C.valueCallback(X[0]):X[0];Z=G.valueCallback?G.valueCallback(Z):Z;var B=H.slice(Y.length);return{value:Z,rest:B}}}var g=/^(\d+)[ºªo]?/i,d=/\d+/i,p={narrow:/^(ac|dc|a|d)/i,abbreviated:/^(a\.?\s?c\.?|d\.?\s?c\.?)/i,wide:/^(antes de cristo|depois de cristo)/i},u={any:[/^ac/i,/^dc/i],wide:[/^antes de cristo/i,/^depois de cristo/i]},l={narrow:/^[1234]/i,abbreviated:/^T[1234]/i,wide:/^[1234](º)? trimestre/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[jfmajsond]/i,abbreviated:/^(jan|fev|mar|abr|mai|jun|jul|ago|set|out|nov|dez)/i,wide:/^(janeiro|fevereiro|março|abril|maio|junho|julho|agosto|setembro|outubro|novembro|dezembro)/i},s={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^fev/i,/^mar/i,/^abr/i,/^mai/i,/^jun/i,/^jul/i,/^ago/i,/^set/i,/^out/i,/^nov/i,/^dez/i]},o={narrow:/^(dom|[23456]ª?|s[aá]b)/i,short:/^(dom|[23456]ª?|s[aá]b)/i,abbreviated:/^(dom|seg|ter|qua|qui|sex|s[aá]b)/i,wide:/^(domingo|(segunda|ter[cç]a|quarta|quinta|sexta)([- ]feira)?|s[aá]bado)/i},r={short:[/^d/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^s[aá]/i],narrow:[/^d/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^s[aá]/i],any:[/^d/i,/^seg/i,/^t/i,/^qua/i,/^qui/i,/^sex/i,/^s[aá]b/i]},a={narrow:/^(a|p|mn|md|(da) (manhã|tarde|noite))/i,any:/^([ap]\.?\s?m\.?|meia[-\s]noite|meio[-\s]dia|(da) (manhã|tarde|noite))/i},e={any:{am:/^a/i,pm:/^p/i,midnight:/^mn|^meia[-\s]noite/i,noon:/^md|^meio[-\s]dia/i,morning:/manhã/i,afternoon:/tarde/i,evening:/tarde/i,night:/noite/i}},t={ordinalNumber:y({matchPattern:g,parsePattern:d,valueCallback:function C(H){return parseInt(H,10)}}),era:O({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function C(H){return H+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},CC={code:"pt-BR",formatDistance:S,formatLong:V,formatRelative:w,localize:h,match:t,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{ptBR:CC})})})();

//# debugId=967C15314543570564756E2164756E21
