import asyncio
from task_handlers.HcaptchaCracker import run

async def main():
    proxy = {
        "server": "http://capsolver-zone-resi-region-hk:<EMAIL>:6001"

    }
    task = {
        "websiteURL": "https://accounts.hcaptcha.com/demo",
        "websiteKey": "********-0000-0000-0000-************",  # 替换为真实 sitekey
        "metadata": {
            "label": "bus"
        }
    }
    result = await run(task, proxy)
    print(result)

if __name__ == "__main__":
    asyncio.run(main())