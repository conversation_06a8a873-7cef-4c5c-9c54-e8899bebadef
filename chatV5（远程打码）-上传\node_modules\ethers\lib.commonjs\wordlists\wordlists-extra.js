"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LangZh = exports.LangPt = exports.LangIt = exports.LangKo = exports.LangJa = exports.LangFr = exports.LangEs = exports.LangCz = void 0;
var lang_cz_js_1 = require("./lang-cz.js");
Object.defineProperty(exports, "LangCz", { enumerable: true, get: function () { return lang_cz_js_1.LangCz; } });
var lang_es_js_1 = require("./lang-es.js");
Object.defineProperty(exports, "LangEs", { enumerable: true, get: function () { return lang_es_js_1.LangEs; } });
var lang_fr_js_1 = require("./lang-fr.js");
Object.defineProperty(exports, "LangFr", { enumerable: true, get: function () { return lang_fr_js_1.LangFr; } });
var lang_ja_js_1 = require("./lang-ja.js");
Object.defineProperty(exports, "LangJa", { enumerable: true, get: function () { return lang_ja_js_1.LangJa; } });
var lang_ko_js_1 = require("./lang-ko.js");
Object.defineProperty(exports, "LangKo", { enumerable: true, get: function () { return lang_ko_js_1.LangKo; } });
var lang_it_js_1 = require("./lang-it.js");
Object.defineProperty(exports, "LangIt", { enumerable: true, get: function () { return lang_it_js_1.LangIt; } });
var lang_pt_js_1 = require("./lang-pt.js");
Object.defineProperty(exports, "LangPt", { enumerable: true, get: function () { return lang_pt_js_1.LangPt; } });
var lang_zh_js_1 = require("./lang-zh.js");
Object.defineProperty(exports, "LangZh", { enumerable: true, get: function () { return lang_zh_js_1.LangZh; } });
//# sourceMappingURL=wordlists-extra.js.map