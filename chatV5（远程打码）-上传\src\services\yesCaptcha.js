const axios = require('axios');
const config = require('../../config');
const { log, logToFile } = require('../utils');

/**
 * YesCaptcha服务 - 用于解决RecaptchaV3验证
 * 文档: https://yescaptcha.atlassian.net/wiki/spaces/YESCAPTCHA/overview
 */
class YesCaptchaService {
  constructor() {
    this.clientKey = config.YESCAPTCHA_CLIENT_KEY || '';
    this.websiteURL = config.YESCAPTCHA_WEBSITE_URL || 'https://klokapp.ai/';
    this.websiteKey = config.YESCAPTCHA_WEBSITE_KEY || '6LcZrRMrAAAAAKllb4TLb1CWH2LR7iNOKmT7rt3L';
    this.pageAction = config.YESCAPTCHA_PAGE_ACTION || 'WALLET_CONNECT';
    this.taskType = config.YESCAPTCHA_TYPE || 'RecaptchaV3EnterpriseTaskM1';
    this.baseUrl = 'https://api.yescaptcha.com';
  }

  /**
   * 创建验证码任务并获取任务ID
   * @returns {Promise<string>} 任务ID
   */
  async createTask() {
    try {
      log(`[YesCaptcha] 创建${this.taskType}任务...`, 'info');
      logToFile(`创建YesCaptcha任务`, {
        taskType: this.taskType,
        websiteURL: this.websiteURL,
        websiteKey: this.websiteKey,
        pageAction: this.pageAction
      });

      // 根据YesCaptcha开发文档，softId必须是数字类型，不能是字符串
      const response = await axios.post(`${this.baseUrl}/createTask`, {
        clientKey: this.clientKey,
        task: {
          type: this.taskType,
          websiteURL: this.websiteURL,
          websiteKey: this.websiteKey,
          pageAction: this.pageAction,
          isEnterprise: true
        },
        "softID": 61036 // 使用数字类型的softId，而非字符串
      });
      
      // 记录请求信息到日志
      logToFile('YesCaptcha请求详情', {
        clientKey: this.clientKey.substring(0, 10) + '...',
        softId: 61036,
        taskType: this.taskType
      });

      if (response.data && response.data.taskId) {
        log(`[YesCaptcha] 任务创建成功，ID: ${response.data.taskId}`, 'success');
        return response.data.taskId;
      } else {
        throw new Error(`创建任务失败: ${JSON.stringify(response.data)}`);
      }
    } catch (error) {
      log(`[YesCaptcha] 创建任务失败: ${error.message}`, 'error');
      logToFile(`YesCaptcha创建任务失败`, { error: error.message });
      throw error;
    }
  }

  /**
   * 获取任务结果
   * @param {string} taskId 任务ID
   * @returns {Promise<string>} 验证码token
   */
  async getTaskResult(taskId) {
    try {
      log(`[YesCaptcha] 获取任务结果: ${taskId}`, 'info');
      
      // 最多尝试30次，每次间隔2秒
      let attempts = 0;
      const maxAttempts = 30;
      const interval = 2000;

      while (attempts < maxAttempts) {
        attempts++;
        
        const response = await axios.post(`${this.baseUrl}/getTaskResult`, {
          clientKey: this.clientKey,
          taskId: taskId
        });

        if (response.data && response.data.status === 'ready') {
          const token = response.data.solution.gRecaptchaResponse;
          log(`[YesCaptcha] 成功获取token (尝试${attempts}次)`, 'success');
          logToFile(`YesCaptcha获取token成功`, { 
            attempts,
            tokenLength: token.length,
            tokenPreview: token.substring(0, 20) + '...'
          });
          return token;
        } else if (response.data && response.data.status === 'processing') {
          log(`[YesCaptcha] 任务处理中，等待中... (${attempts}/${maxAttempts})`, 'info');
          await new Promise(resolve => setTimeout(resolve, interval));
        } else {
          throw new Error(`获取结果失败: ${JSON.stringify(response.data)}`);
        }
      }

      throw new Error(`获取任务结果超时，尝试次数: ${maxAttempts}`);
    } catch (error) {
      log(`[YesCaptcha] 获取任务结果失败: ${error.message}`, 'error');
      logToFile(`YesCaptcha获取任务结果失败`, { error: error.message });
      throw error;
    }
  }

  /**
   * 获取RecaptchaV3 Token
   * @returns {Promise<string>} 验证码token
   */
  async getRecaptchaToken() {
    try {
      if (!this.clientKey) {
        throw new Error('缺少YesCaptcha客户端密钥，请在config.js中配置YESCAPTCHA_CLIENT_KEY');
      }

      const taskId = await this.createTask();
      const token = await this.getTaskResult(taskId);
      return token;
    } catch (error) {
      log(`[YesCaptcha] 获取RecaptchaV3 Token失败: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * 获取账户余额
   * @returns {Promise<number>} 账户余额
   */
  async getBalance() {
    try {
      const response = await axios.post(`${this.baseUrl}/getBalance`, {
        clientKey: this.clientKey
      });

      if (response.data && response.data.balance !== undefined) {
        return response.data.balance;
      } else {
        throw new Error(`获取余额失败: ${JSON.stringify(response.data)}`);
      }
    } catch (error) {
      log(`[YesCaptcha] 获取账户余额失败: ${error.message}`, 'error');
      return -1;
    }
  }
}

// 导出单例
const yesCaptchaService = new YesCaptchaService();
module.exports = yesCaptchaService;
