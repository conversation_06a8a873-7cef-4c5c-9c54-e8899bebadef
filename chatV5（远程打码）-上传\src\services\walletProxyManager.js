const fs = require('fs');
const path = require('path');
const config = require('../../config');
const { log, logToFile } = require('../utils');

/**
 * 钱包代理绑定管理服务
 * 负责管理钱包和代理的绑定关系
 */
class WalletProxyManager {
  constructor() {
    this.bindingFilePath = path.resolve(process.cwd(), config.WALLET_PROXY_BINDING_FILE || './proxybind.json');
    this.failureLogPath = path.resolve(process.cwd(), './proxy-failures.json');
    this.bindings = {};
    this.loadBindings();
  }

  /**
   * 加载绑定关系
   */
  loadBindings() {
    try {
      if (fs.existsSync(this.bindingFilePath)) {
        const data = fs.readFileSync(this.bindingFilePath, 'utf8');
        this.bindings = JSON.parse(data);
        const bindingCount = Object.keys(this.bindings).length;
        log(`[钱包代理绑定] 已加载${bindingCount}个钱包代理绑定关系`, 'info');
        logToFile('钱包代理绑定关系加载成功', { count: bindingCount });
      } else {
        log('[钱包代理绑定] 绑定文件不存在，将创建新文件', 'info');
        this.saveBindings();
      }
    } catch (error) {
      log(`[钱包代理绑定] 加载绑定关系失败: ${error.message}`, 'error');
      logToFile('加载钱包代理绑定关系失败', { error: error.message });
      this.bindings = {};
      this.saveBindings();
    }
  }

  /**
   * 保存绑定关系
   */
  saveBindings() {
    try {
      fs.writeFileSync(this.bindingFilePath, JSON.stringify(this.bindings, null, 2), 'utf8');
      log('[钱包代理绑定] 绑定关系已保存', 'success');
      logToFile('钱包代理绑定关系已保存', { count: Object.keys(this.bindings).length });
    } catch (error) {
      log(`[钱包代理绑定] 保存绑定关系失败: ${error.message}`, 'error');
      logToFile('保存钱包代理绑定关系失败', { error: error.message });
    }
  }

  /**
   * 绑定钱包和代理
   * @param {string} walletAddress 钱包地址
   * @param {string} proxyUrl 代理URL
   */
  bindWalletToProxy(walletAddress, proxyUrl) {
    if (!walletAddress || !proxyUrl) {
      log('[钱包代理绑定] 钱包地址和代理URL不能为空', 'error');
      return false;
    }

    // 检查是否已存在绑定关系
    if (this.bindings[walletAddress]) {
      // 如果已存在，不再保留sessionToken
      const existingBinding = this.bindings[walletAddress];
      if (typeof existingBinding === 'object') {
        // 移除sessionToken字段，只保留其他信息
        const { sessionToken, ...rest } = existingBinding;
        this.bindings[walletAddress] = {
          ...rest,
          proxyUrl: proxyUrl,
          lastUpdated: new Date().toISOString()
        };
      } else {
        // 兼容旧版本的绑定格式
        this.bindings[walletAddress] = {
          proxyUrl: proxyUrl,
          failureCount: 0,
          lastUpdated: new Date().toISOString()
        };
      }
    } else {
      // 创建新的绑定关系
      this.bindings[walletAddress] = {
        proxyUrl: proxyUrl,
        failureCount: 0,
        lastUpdated: new Date().toISOString()
      };
    }
    
    this.saveBindings();
    log(`[钱包代理绑定] 钱包${walletAddress}已绑定到代理${proxyUrl}`, 'success');
    return true;
  }

  /**
   * 获取钱包的绑定信息对象
   * @param {string} walletAddress 钱包地址
   * @returns {Object|null} 绑定信息对象或null
   */
  getWalletBinding(walletAddress) {
    if (!config.ENABLE_WALLET_PROXY_BINDING) {
      return null;
    }
    
    if (!walletAddress) {
      return null;
    }

    const binding = this.bindings[walletAddress];
    if (!binding) return null;
    
    // 兼容旧版本的绑定格式
    if (typeof binding === 'string') {
      return { proxyUrl: binding };
    }
    
    return binding;
  }

  /**
   * 获取钱包绑定的代理
   * @param {string} walletAddress 钱包地址
   * @returns {string|null} 代理URL或null
   */
  getProxyForWallet(walletAddress) {
    if (!config.ENABLE_WALLET_PROXY_BINDING) {
      return null;
    }
    
    if (!walletAddress) {
      return null;
    }

    const binding = this.bindings[walletAddress];
    if (!binding) return null;
    
    // 兼容旧版本的绑定格式
    if (typeof binding === 'string') {
      return binding;
    }
    
    return binding.proxyUrl || null;
  }

  /**
   * 解除钱包和代理的绑定
   * @param {string} walletAddress 钱包地址
   */
  unbindWallet(walletAddress) {
    if (!walletAddress || !this.bindings[walletAddress]) {
      return false;
    }

    delete this.bindings[walletAddress];
    this.saveBindings();
    log(`[钱包代理绑定] 已解除钱包${walletAddress}的代理绑定`, 'info');
    return true;
  }

  /**
   * 自动为未绑定代理的钱包分配代理
   * @param {Array<string>} walletAddresses 钱包地址列表
   * @param {Array<string>} proxyUrls 代理URL列表
   */
  autoBindWallets(walletAddresses, proxyUrls) {
    if (!config.ENABLE_WALLET_PROXY_BINDING) {
      log('[钱包代理绑定] 钱包代理绑定功能未启用', 'info');
      return;
    }

    if (!walletAddresses || !walletAddresses.length || !proxyUrls || !proxyUrls.length) {
      log('[钱包代理绑定] 钱包地址或代理URL列表为空', 'warning');
      return;
    }

    // 确保数量相等，取较小值
    const count = Math.min(walletAddresses.length, proxyUrls.length);
    log(`[钱包代理绑定] 将一一对应绑定 ${count} 个钱包和代理`, 'info');
    
    let bindCount = 0;
    for (let i = 0; i < count; i++) {
      const walletAddress = walletAddresses[i];
      const proxyUrl = proxyUrls[i];
      
      this.bindWalletToProxy(walletAddress, proxyUrl);
      bindCount++;
    }

    if (bindCount > 0) {
      log(`[钱包代理绑定] 已为${bindCount}个钱包分配代理`, 'success');
      this.saveBindings();
    } else {
      log('[钱包代理绑定] 没有钱包需要绑定代理', 'info');
    }
  }

  /**
   * 获取所有绑定关系
   * @returns {Object} 绑定关系对象
   */
  getAllBindings() {
    return { ...this.bindings };
  }

  /**
   * 清除所有绑定关系
   */
  clearAllBindings() {
    this.bindings = {};
    this.saveBindings();
    log('[钱包代理绑定] 已清除所有钱包代理绑定关系', 'info');
    return true;
  }

  /**
   * 更新钱包的会话Token
   * @param {string} walletAddress 钱包地址
   * @param {string} sessionToken 会话Token
   * @returns {boolean} 是否更新成功
   */
  updateWalletToken(walletAddress, sessionToken) {
    if (!walletAddress || !sessionToken) {
      log('[钱包代理绑定] 钱包地址和会话Token不能为空', 'error');
      return false;
    }

    if (!this.bindings[walletAddress]) {
      log(`[钱包代理绑定] 钱包${walletAddress}未绑定代理`, 'warning');
      return false;
    }

    // 不再保存token，只记录日志
    log(`[钱包代理绑定] 钱包${walletAddress}获取到新token，但不再保存`, 'info');
    return true;
  }

  /**
   * 获取钱包的会话Token
   * @param {string} walletAddress 钱包地址
   * @returns {null} 始终返回null，不再使用token
   */
  getWalletToken(walletAddress) {
    // 不再返回token，始终返回null
    log(`[钱包代理绑定] 请求钱包${walletAddress}的token，但不再提供`, 'info');
    return null;
  }

  /**
   * 获取钱包的完整绑定信息
   * @param {string} walletAddress 钱包地址
   * @returns {Object|null} 绑定信息或null，不包含sessionToken
   */
  getWalletBinding(walletAddress) {
    if (!walletAddress || !this.bindings[walletAddress]) {
      return null;
    }

    const binding = this.bindings[walletAddress];
    if (typeof binding === 'string') {
      // 兼容旧版本绑定格式
      return {
        proxyUrl: binding,
        failureCount: 0,
        lastUpdated: null
      };
    }

    // 返回绑定信息，但移除sessionToken字段
    const { sessionToken, ...rest } = binding;
    return { ...rest };
  }

  /**
   * 记录代理失败
   * @param {string} walletAddress 钱包地址
   * @param {string} errorMessage 错误信息
   */
  recordProxyFailure(walletAddress, errorMessage) {
    if (!walletAddress) return;

    // 获取绑定信息
    const binding = this.bindings[walletAddress];
    if (!binding) return;

    // 更新失败计数
    if (typeof binding === 'string') {
      // 兼容旧版本绑定格式
      const proxyUrl = binding;
      this.bindings[walletAddress] = {
        proxyUrl: proxyUrl,
        failureCount: 1,
        lastFailure: new Date().toISOString(),
        lastFailureMessage: errorMessage
      };
    } else {
      binding.failureCount = (binding.failureCount || 0) + 1;
      binding.lastFailure = new Date().toISOString();
      binding.lastFailureMessage = errorMessage;
    }

    this.saveBindings();
    
    // 记录到失败日志文件
    this.logProxyFailure(walletAddress, errorMessage);
    
    log(`[钱包代理绑定] 钱包${walletAddress}的代理失败次数+1，当前失败次数: ${this.bindings[walletAddress].failureCount}`, 'warning');
  }

  /**
   * 将代理失败记录到日志文件
   * @param {string} walletAddress 钱包地址
   * @param {string} errorMessage 错误信息
   */
  logProxyFailure(walletAddress, errorMessage) {
    try {
      // 读取现有失败日志
      let failureLog = {};
      if (fs.existsSync(this.failureLogPath)) {
        const data = fs.readFileSync(this.failureLogPath, 'utf8');
        try {
          failureLog = JSON.parse(data);
        } catch (e) {
          failureLog = {};
        }
      }

      // 获取代理URL
      const binding = this.bindings[walletAddress];
      const proxyUrl = typeof binding === 'string' ? binding : binding.proxyUrl;

      // 添加新的失败记录
      const timestamp = new Date().toISOString();
      if (!failureLog[walletAddress]) {
        failureLog[walletAddress] = [];
      }

      failureLog[walletAddress].push({
        timestamp,
        proxyUrl,
        errorMessage,
        totalFailures: this.bindings[walletAddress].failureCount
      });

      // 保存失败日志
      fs.writeFileSync(this.failureLogPath, JSON.stringify(failureLog, null, 2), 'utf8');
    } catch (error) {
      log(`[钱包代理绑定] 记录代理失败日志失败: ${error.message}`, 'error');
    }
  }

  /**
   * 重置代理失败计数
   * @param {string} walletAddress 钱包地址
   */
  resetProxyFailureCount(walletAddress) {
    if (!walletAddress || !this.bindings[walletAddress]) return;

    const binding = this.bindings[walletAddress];
    if (typeof binding === 'object') {
      binding.failureCount = 0;
      binding.lastFailure = null;
      binding.lastFailureMessage = null;
      this.saveBindings();
      log(`[钱包代理绑定] 已重置钱包${walletAddress}的代理失败计数`, 'info');
    }
  }
}

// 导出单例
const walletProxyManager = new WalletProxyManager();
module.exports = walletProxyManager;
