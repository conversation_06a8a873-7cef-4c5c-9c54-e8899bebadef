"use strict";
/**
 *  Some common constants useful for Ethereum.
 *
 *  @_section: api/constants: Constants  [about-constants]
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessagePrefix = exports.EtherSymbol = exports.MaxInt256 = exports.MinInt256 = exports.MaxUint256 = exports.WeiPerEther = exports.N = exports.ZeroHash = exports.ZeroAddress = void 0;
var addresses_js_1 = require("./addresses.js");
Object.defineProperty(exports, "ZeroAddress", { enumerable: true, get: function () { return addresses_js_1.ZeroAddress; } });
var hashes_js_1 = require("./hashes.js");
Object.defineProperty(exports, "ZeroHash", { enumerable: true, get: function () { return hashes_js_1.ZeroHash; } });
var numbers_js_1 = require("./numbers.js");
Object.defineProperty(exports, "N", { enumerable: true, get: function () { return numbers_js_1.N; } });
Object.defineProperty(exports, "WeiPerEther", { enumerable: true, get: function () { return numbers_js_1.<PERSON><PERSON>erEther; } });
Object.defineProperty(exports, "MaxUint256", { enumerable: true, get: function () { return numbers_js_1.MaxUint256; } });
Object.defineProperty(exports, "MinInt256", { enumerable: true, get: function () { return numbers_js_1.MinInt256; } });
Object.defineProperty(exports, "MaxInt256", { enumerable: true, get: function () { return numbers_js_1.MaxInt256; } });
var strings_js_1 = require("./strings.js");
Object.defineProperty(exports, "EtherSymbol", { enumerable: true, get: function () { return strings_js_1.EtherSymbol; } });
Object.defineProperty(exports, "MessagePrefix", { enumerable: true, get: function () { return strings_js_1.MessagePrefix; } });
//# sourceMappingURL=index.js.map