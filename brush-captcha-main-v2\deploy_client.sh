#!/bin/bash

# 客户端部署脚本
# 使用方法: bash deploy_client.sh [客户端名称]

echo "🚀 客户端部署脚本"
echo "=================="

# 主服务器IP
SERVER_IP="*************"

# 获取客户端名称
if [ -n "$1" ]; then
    CLIENT_NAME="$1"
else
    read -p "请输入客户端名称 (例如: client-01): " CLIENT_NAME
    CLIENT_NAME=${CLIENT_NAME:-"client-$(date +%s)"}
fi

echo "📋 部署信息:"
echo "   主服务器: $SERVER_IP"
echo "   客户端名称: $CLIENT_NAME"
echo ""

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先运行: bash install_docker.sh"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose未安装，请先运行: bash install_docker.sh"
    exit 1
fi

# 克隆或更新项目
echo "📥 准备项目文件..."
if [ -d "brush-captcha" ]; then
    echo "⚠️  项目目录已存在，正在更新..."
    cd brush-captcha
    git pull
else
    echo "📥 克隆项目..."
    git clone https://github.com/Brush-Bot/brush-captcha.git
    cd brush-captcha
fi

# 进入客户端目录
cd client

# 检测主服务器连接方式
echo "🔍 检测主服务器连接方式..."
if curl -k -s --connect-timeout 5 "https://$SERVER_IP:8080" > /dev/null 2>&1; then
    WSS_URL="wss://$SERVER_IP:8080/ws/worker/"
    echo "✅ 检测到HTTPS，使用WSS连接"
elif curl -s --connect-timeout 5 "http://$SERVER_IP:8080" > /dev/null 2>&1; then
    WSS_URL="ws://$SERVER_IP:8080/ws/worker/"
    echo "✅ 检测到HTTP，使用WS连接"
else
    echo "⚠️  无法连接到主服务器，使用默认WS连接"
    WSS_URL="ws://$SERVER_IP:8080/ws/worker/"
fi

# 创建配置文件
echo "📝 创建配置文件..."
cat > config.yaml << EOF
# 并发数设置（自动根据系统资源计算）
concurrency: null

# Camoufox 参数配置
camoufox:
  # 支持的打码类型
  solver_type:
    - HcaptchaCracker
    - AntiTurnstileTaskProxyLess
  # 无头模式
  headless: "true"

worker:
  # 客户端名称
  name: "$CLIENT_NAME"
  # 主服务器地址
  wss_url: "$WSS_URL"
EOF

echo "✅ 配置文件已创建"

# 显示配置内容
echo ""
echo "📋 配置文件内容:"
echo "=================="
cat config.yaml
echo "=================="
echo ""

# 启动客户端
echo "🚀 启动客户端..."
docker-compose down 2>/dev/null || true
docker-compose up -d

# 等待容器启动
echo "⏳ 等待容器启动..."
sleep 10

# 检查容器状态
echo "📊 检查容器状态..."
if docker ps | grep -q "capsolver-client\|client"; then
    echo "✅ 客户端容器启动成功！"
    
    # 显示容器信息
    echo ""
    echo "📋 容器信息:"
    docker ps | grep -E "capsolver-client|client"
    
    # 显示日志
    echo ""
    echo "📄 最近日志:"
    echo "============"
    CONTAINER_NAME=$(docker ps --format "{{.Names}}" | grep -E "capsolver-client|client" | head -1)
    if [ -n "$CONTAINER_NAME" ]; then
        docker logs --tail 20 "$CONTAINER_NAME"
    fi
    echo "============"
    
else
    echo "❌ 客户端容器启动失败！"
    echo ""
    echo "📄 错误日志:"
    echo "============"
    docker-compose logs
    echo "============"
    exit 1
fi

# 显示完成信息
echo ""
echo "🎉 客户端部署完成！"
echo "=================="
echo "📋 部署信息:"
echo "   客户端名称: $CLIENT_NAME"
echo "   连接地址: $WSS_URL"
echo "   容器状态: 运行中"
echo ""
echo "🔧 管理命令:"
echo "   查看状态: docker ps"
echo "   查看日志: docker logs $CONTAINER_NAME"
echo "   重启服务: docker-compose restart"
echo "   停止服务: docker-compose down"
echo ""
echo "🌐 管理后台: http://$SERVER_IP:8080"
echo "👤 用户名: ddk"
echo "🔑 密码: Ddk19940316."
echo ""
echo "💡 提示: 在管理后台的'节点状态'部分可以看到客户端连接状态"
