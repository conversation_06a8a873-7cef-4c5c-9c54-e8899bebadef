module.exports = {
  THREADS: 20,
  BASE_URL: "https://api1-pp.klokapp.ai",

  // API Keys
  GROQ_API_KEY_PATH: "./groq-api.key",  // 保留路径但不使用
  GEMINI_API_KEY_PATH: "./gemini-api.key",

  // Models
  GROQ_MODEL: "llama-3.1-8b",  // 保留但不使用
  GEMINI_MODEL: "gemini-1.5-pro",  // 使用测试成功的模型

  // API Usage Strategy
  API_STRATEGY: {
    // 每个API的权重，权重越大使用频率越高
    GROQ_WEIGHT: 0,  // 完全禁用Groq API
    GEMINI_WEIGHT: 100,  // 只使用Gemini API
    // 是否启用自动切换
    AUTO_SWITCH: false, // 禁用API切换
    // 每个API的每日请求限制
    DAILY_LIMITS: {
      GROQ: 0,  // 禁用Groq API
      GEMINI: Number.MAX_SAFE_INTEGER  // 移除Gemini API限制
    }
  },

  DEFAULT_HEADERS: {
    "content-type": "application/json",
    Origin: "https://klokapp.ai",
    Referer: "https://klokapp.ai/"
  },

  REFERRAL_CODE: {
    referral_code: ""
  },

  // 请求间隔配置
  MIN_CHAT_DELAY: 5000,      // 最小聊天延迟(5秒)
  MAX_CHAT_DELAY: 10000,     // 最大聊天延迟(10秒)
  
  // 每个钱包每次任务最多执行的问题数
  MAX_QUESTIONS_PER_WALLET: 50,  // 增加到更高的值，充分利用API配额
  
  // API请求配置
  REQUEST_TIMEOUT: 30000,     // API请求超时时间(30秒)
  REQUEST_RETRY_COUNT: 3,     // 请求失败时的重试次数
  REQUEST_RETRY_DELAY: 2000,  // 重试延迟(2秒)
  
  // Gemini API配置
  GEMINI_REQUEST_TIMEOUT: 30000, // Gemini API超时时间(30秒)
  GEMINI_RETRY_COUNT: 2,         // 失败时重试次数
  GEMINI_RETRY_DELAY: 2000,      // 重试延迟(2秒)

  // 使用预设问题库 - 作为备用方案
  USE_PREDEFINED_QUESTIONS: true, // 当API不可用时使用预设问题库
  
  // 网络请求方式配置
  // 接口转发设置
  USE_API_FORWARDING: false,  // 禁用接口转发
  API_FORWARDING_BASE_PATH: "https://jiushi21.win/v1",
  
  // 代理设置 (必需)
  USE_PROXY: true,  // 启用代理（测试成功）
  PROXY_URL: 'http://127.0.0.1:7890', // 代理服务器地址

  // YesCaptcha配置
  YESCAPTCHA_CLIENT_KEY: '8272c13b7d47b23b419a48f03b704e5db66c57b161036',  // 需要在运行时设置
  YESCAPTCHA_WEBSITE_URL: 'https://klokapp.ai/',
  YESCAPTCHA_WEBSITE_KEY: '0x4AAAAAABdQypM3HkDQTuaO', // Cloudflare Turnstile sitekey
  YESCAPTCHA_PAGE_ACTION: 'WALLET_CONNECT',
  YESCAPTCHA_TYPE: 'TurnstileTaskProxyless', // 使用Turnstile任务类型
  
  // 验证码服务选择（'local'、'2captcha'或'remote'）
  CAPTCHA_SERVICE: 'remote',

  // 本地Turnstile-Solver配置
  LOCAL_TURNSTILE_SOLVER_URL: 'http://127.0.0.1:5000',
  LOCAL_TURNSTILE_WEBSITE_URL: 'https://klokapp.ai/',
  LOCAL_TURNSTILE_WEBSITE_KEY: '0x4AAAAAABdQypM3HkDQTuaO',

  // 远程打码服务配置
  REMOTE_CAPTCHA_URL: 'http://your-vps-ip:8000',  // 替换为你的VPS地址
  REMOTE_CAPTCHA_CLIENT_KEY: 'your_client_key',   // 替换为你的客户端密钥
  REMOTE_CAPTCHA_MAX_RETRIES: 5,                  // 最大重试次数
  REMOTE_CAPTCHA_RETRY_DELAY: 3000,               // 重试延迟(毫秒)
  REMOTE_CAPTCHA_TASK_TIMEOUT: 300000,            // 任务超时时间(毫秒，5分钟)
  
  // 2Captcha配置
  TWOCAPTCHA_API_KEY: '********************************', // 需要在运行时设置您的2Captcha API密钥
  TWOCAPTCHA_WEBSITE_URL: 'https://klokapp.ai/',
  TWOCAPTCHA_WEBSITE_KEY: '0x4AAAAAABdQypM3HkDQTuaO', // 需要设置网站上的Cloudflare Turnstile sitekey
  
  // 钱包代理绑定设置
  WALLET_PROXY_BINDING_FILE: './proxybind.json',
  ENABLE_WALLET_PROXY_BINDING: true,
  
  // 钱包队列管理设置
  SHUFFLE_WALLETS_DAILY: true,  // 每天0点随机打乱钱包队列
  
  // 问题文件设置
  QUESTIONS_FILE: './question.txt',
};
