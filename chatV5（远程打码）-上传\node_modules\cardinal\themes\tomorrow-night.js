var colors = require('ansicolors')

// Change the below definitions in order to tweak the color theme.
module.exports = {

    'Boolean': {
      'true'   :  undefined
    , 'false'  :  undefined
    , _default :  colors.yellow
    }

  , 'Identifier': {
      'undefined' :  colors.yellow
    , 'self'      :  colors.yellow
    , 'type'      :  colors.yellow
    , 'value'     :  colors.yellow
    , 'console'   :  undefined
    , 'log'       :  colors.blue
    , 'warn'      :  colors.blue
    , 'error'     :  colors.blue
    , 'join'      :  colors.blue
    , _default    :  function(s, info) {
        var prevToken = info.tokens[info.tokenIndex - 1]
        var nextToken = info.tokens[info.tokenIndex + 1]

        return (nextToken
            && nextToken.type === 'Punctuator'
            && nextToken.value === '('
            && prevToken
            && prevToken.type === 'Keyword'
            && prevToken.value === 'function'
          ) ? colors.blue(s) : colors.white(s)
      }
    }

  , 'Null': {
      _default: colors.yellow
    }

  , 'Numeric': {
      _default: colors.yellow
    }

  , 'String': {
      _default: function(s, info) {
        var nextToken = info.tokens[info.tokenIndex + 1]

        // show keys of object literals and json in different color
        return (nextToken && nextToken.type === 'Punctuator' && nextToken.value === ':')
          ? colors.green(s)
          : colors.brightGreen(s)
      }
    }

  , 'Keyword': {
      'break'       :  colors.magenta

    , 'case'        :  colors.magenta
    , 'catch'       :  colors.magenta
    , 'class'       :  undefined
    , 'const'       :  undefined
    , 'continue'    :  colors.magenta

    , 'debugger'    :  colors.magenta
    , 'default'     :  colors.magenta
    , 'delete'      :  colors.red
    , 'do'          :  colors.magenta

    , 'else'        :  colors.magenta
    , 'enum'        :  undefined
    , 'export'      :  undefined
    , 'extends'     :  undefined

    , 'finally'     :  colors.magenta
    , 'for'         :  colors.magenta
    , 'function'    :  colors.magenta

    , 'if'          :  colors.magenta
    , 'implements'  :  undefined
    , 'import'      :  undefined
    , 'in'          :  colors.cyan
    , 'instanceof'  :  colors.cyan
    , 'let'         :  undefined
    , 'new'         :  colors.cyan
    , 'package'     :  undefined
    , 'private'     :  undefined
    , 'protected'   :  undefined
    , 'public'      :  undefined
    , 'return'      :  colors.magenta
    , 'static'      :  undefined
    , 'super'       :  undefined
    , 'switch'      :  colors.magenta

    , 'this'        :  colors.red
    , 'throw'       :  colors.magenta
    , 'try'         :  colors.magenta
    , 'typeof'      :  colors.cyan

    , 'var'         :  colors.magenta
    , 'void'        :  colors.magenta

    , 'while'       :  colors.magenta
    , 'with'        :  colors.cyan
    , 'yield'       :  undefined
    , _default      :  colors.white
  }
  , 'Punctuator': {
      ';': colors.white
    , '.': colors.white
    , ',': colors.white

    , '{': colors.white
    , '}': colors.white
    , '(': colors.white
    , ')': colors.white
    , '[': colors.white
    , ']': colors.white

    , '<': undefined
    , '>': undefined
    , '+': undefined
    , '-': undefined
    , '*': undefined
    , '%': undefined
    , '&': undefined
    , '|': colors.white
    , '^': undefined
    , '!': undefined
    , '~': undefined
    , '?': colors.white
    , ':': colors.white
    , '=': undefined

    , '<=': undefined
    , '>=': undefined
    , '==': undefined
    , '!=': undefined
    , '++': undefined
    , '--': undefined
    , '<<': undefined
    , '>>': undefined
    , '&&': undefined
    , '||': undefined
    , '+=': undefined
    , '-=': undefined
    , '*=': undefined
    , '%=': undefined
    , '&=': undefined
    , '|=': undefined
    , '^=': undefined
    , '/=': undefined
    , '=>': undefined
    , '**': undefined

    , '===': undefined
    , '!==': undefined
    , '>>>': undefined
    , '<<=': undefined
    , '>>=': undefined
    , '...': undefined
    , '**=': undefined

    , '>>>=': undefined

    , _default: colors.cyan
  }

    // line comment
  , Line: {
     _default: colors.brightBlack
    }

    /* block comment */
  , Block: {
     _default: colors.brightBlack
    }

  // JSX
  , JSXAttribute: {
      _default: colors.magenta
    }
  , JSXClosingElement: {
      _default: colors.magenta
    }
  , JSXElement: {
      _default: colors.magenta
    }
  , JSXEmptyExpression: {
      _default: colors.magenta
    }
  , JSXExpressionContainer: {
      _default: colors.magenta
    }
  , JSXIdentifier: {
        className: colors.blue
      , _default: colors.magenta
    }
  , JSXMemberExpression: {
      _default: colors.magenta
    }
  , JSXNamespacedName: {
      _default: colors.magenta
    }
  , JSXOpeningElement: {
      _default: colors.magenta
    }
  , JSXSpreadAttribute: {
      _default: colors.magenta
    }
  , JSXText: {
      _default: colors.brightGreen
    }

  , _default: undefined
}
