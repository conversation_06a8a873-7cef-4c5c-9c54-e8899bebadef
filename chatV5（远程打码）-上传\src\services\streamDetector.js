/**
 * 流式响应检测器
 * 用于检测流式响应是否已完成
 */
const { log, logToFile } = require('../utils/simple-logger');

class StreamDetector {
  constructor() {
    // 使用Map存储不同上下文的流式响应状态
    this.streamStates = new Map();
    this.defaultTimeout = 60000; // 默认超时时间：60秒
  }

  /**
   * 重置流式响应状态
   * @param {string} contextKey 上下文键，通常是钱包地址+进程ID
   */
  resetStreamState(contextKey) {
    this.streamStates.set(contextKey, {
      isComplete: false,
      startTime: Date.now(),
      lastUpdateTime: Date.now(),
      chunks: 0,
      hasError: false,
      errorMessage: null
    });
    
    log(`[流式响应] 已重置状态 (${contextKey})`, "info");
  }

  /**
   * 标记流式响应完成
   * @param {string} contextKey 上下文键
   * @param {boolean} hasError 是否有错误
   * @param {string} errorMessage 错误信息
   */
  markStreamComplete(contextKey, hasError = false, errorMessage = null) {
    const state = this.streamStates.get(contextKey);
    
    if (!state) {
      this.resetStreamState(contextKey);
      this.markStreamComplete(contextKey, hasError, errorMessage);
      return;
    }
    
    state.isComplete = true;
    state.lastUpdateTime = Date.now();
    state.hasError = hasError;
    state.errorMessage = errorMessage;
    
    const duration = state.lastUpdateTime - state.startTime;
    log(`[流式响应] 已完成 (${contextKey}), 耗时: ${duration}ms, 块数: ${state.chunks}`, "success");
    
    this.streamStates.set(contextKey, state);
  }

  /**
   * 更新流式响应状态
   * @param {string} contextKey 上下文键
   * @param {string} chunk 响应块
   */
  updateStreamState(contextKey, chunk) {
    let state = this.streamStates.get(contextKey);
    
    if (!state) {
      this.resetStreamState(contextKey);
      state = this.streamStates.get(contextKey);
    }
    
    state.lastUpdateTime = Date.now();
    state.chunks++;
    
    // 检查是否包含完成标记
    if (this.checkCompletionMarkers(chunk)) {
      state.isComplete = true;
      log(`[流式响应] 检测到完成标记 (${contextKey})`, "info");
    }
    
    this.streamStates.set(contextKey, state);
  }

  /**
   * 检查响应块是否包含完成标记
   * @param {string} chunk 响应块
   * @returns {boolean} 是否包含完成标记
   */
  checkCompletionMarkers(chunk) {
    if (!chunk) return false;
    
    // 检查常见的完成标记
    if (chunk.includes('[DONE]')) return true;
    
    // 检查JSON完成标记
    if (chunk.includes('"finish_reason":"stop"')) return true;
    if (chunk.includes('"finish_reason": "stop"')) return true;
    
    // 检查OpenAI格式的完成标记
    if (chunk.includes('"finish_reason":') && (chunk.includes('"stop"') || chunk.includes('"length"'))) return true;
    
    // 检查完整JSON对象结束
    const jsonEndPattern = /\}\s*$/;
    if (jsonEndPattern.test(chunk.trim())) return true;
    
    return false;
  }

  /**
   * 检查流式响应是否已完成
   * @param {string} contextKey 上下文键
   * @returns {boolean} 是否已完成
   */
  isStreamComplete(contextKey) {
    const state = this.streamStates.get(contextKey);
    
    if (!state) return true; // 如果没有状态，认为已完成
    
    return state.isComplete;
  }

  /**
   * 等待流式响应完成
   * @param {string} contextKey 上下文键
   * @param {number} timeout 超时时间（毫秒）
   * @returns {Promise<boolean>} 是否成功完成
   */
  async waitForStreamCompletion(contextKey, timeout = this.defaultTimeout) {
    return new Promise((resolve) => {
      const checkInterval = 500; // 每500毫秒检查一次
      const startTime = Date.now();
      
      const check = () => {
        // 检查是否已完成
        if (this.isStreamComplete(contextKey)) {
          const state = this.streamStates.get(contextKey);
          const duration = Date.now() - startTime;
          log(`[流式响应] 等待完成 (${contextKey}), 耗时: ${duration}ms`, "success");
          
          resolve(true);
          return;
        }
        
        // 检查是否超时
        if (Date.now() - startTime > timeout) {
          log(`[流式响应] 等待超时 (${contextKey}), 超时: ${timeout}ms`, "warning");
          this.markStreamComplete(contextKey, true, "等待超时");
          resolve(false);
          return;
        }
        
        // 继续等待
        setTimeout(check, checkInterval);
      };
      
      // 开始检查
      check();
    });
  }

  /**
   * 清理过期的流式响应状态
   * @param {number} maxAge 最大年龄（毫秒）
   */
  cleanupExpiredStates(maxAge = 3600000) { // 默认1小时
    const now = Date.now();
    
    for (const [key, state] of this.streamStates.entries()) {
      if (now - state.lastUpdateTime > maxAge) {
        this.streamStates.delete(key);
        log(`[流式响应] 清理过期状态 (${key})`, "info");
      }
    }
  }
}

// 创建单例
const streamDetector = new StreamDetector();

// 定期清理过期状态
setInterval(() => {
  streamDetector.cleanupExpiredStates();
}, 3600000); // 每小时清理一次

module.exports = streamDetector;
