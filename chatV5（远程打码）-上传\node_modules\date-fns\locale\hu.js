import { formatDistance } from "./hu/_lib/formatDistance.js";
import { formatLong } from "./hu/_lib/formatLong.js";
import { formatRelative } from "./hu/_lib/formatRelative.js";
import { localize } from "./hu/_lib/localize.js";
import { match } from "./hu/_lib/match.js";

/**
 * @category Locales
 * @summary Hungarian locale.
 * @language Hungarian
 * @iso-639-2 hun
 * <AUTHOR> [@pshpak](https://github.com/pshpak)
 * <AUTHOR> [@eduardopsll](https://github.com/eduardopsll)
 * <AUTHOR> [@twodcube](https://github.com/twodcube)
 */
export const hu = {
  code: "hu",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default hu;
