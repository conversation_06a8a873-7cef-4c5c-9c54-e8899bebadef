{"version": 3, "file": "batches.d.ts", "sourceRoot": "", "sources": ["../src/resources/batches.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAC1C,OAAO,KAAK,IAAI,MAAM,SAAS,CAAC;AAEhC,qBAAa,OAAQ,SAAQ,WAAW;IACtC;;OAEG;IACH,MAAM,CAAC,IAAI,EAAE,iBAAiB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC;IAIpG;;OAEG;IACH,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC;IAIhG;;OAEG;IACH,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC;CAGxE;AAED,MAAM,WAAW,mBAAmB;IAClC,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,iBAAiB,EAAE,MAAM,CAAC;IAE1B;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,aAAa,EAAE,MAAM,CAAC;IAEtB;;OAEG;IACH,MAAM,EAAE,OAAO,CAAC;IAEhB;;OAEG;IACH,MAAM,EACF,YAAY,GACZ,QAAQ,GACR,aAAa,GACb,YAAY,GACZ,WAAW,GACX,SAAS,GACT,YAAY,GACZ,WAAW,CAAC;IAEhB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IAEvB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IAEvB,MAAM,CAAC,EAAE,mBAAmB,CAAC,MAAM,CAAC;IAEpC;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IAEvB;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;;OAGG;IACH,QAAQ,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAE1B;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;OAEG;IACH,cAAc,CAAC,EAAE,mBAAmB,CAAC,aAAa,CAAC;CACpD;AAED,yBAAiB,mBAAmB,CAAC;IACnC,UAAiB,MAAM;QACrB,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAE1B;;WAEG;QACH,MAAM,CAAC,EAAE,MAAM,CAAC;KACjB;IAED,UAAiB,MAAM,CAAC;QACtB,UAAiB,IAAI;YACnB;;eAEG;YACH,IAAI,CAAC,EAAE,MAAM,CAAC;YAEd;;eAEG;YACH,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAErB;;eAEG;YACH,OAAO,CAAC,EAAE,MAAM,CAAC;YAEjB;;eAEG;YACH,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;SACvB;KACF;IAED;;OAEG;IACH,UAAiB,aAAa;QAC5B;;WAEG;QACH,SAAS,EAAE,MAAM,CAAC;QAElB;;WAEG;QACH,MAAM,EAAE,MAAM,CAAC;QAEf;;WAEG;QACH,KAAK,EAAE,MAAM,CAAC;KACf;CACF;AAED,MAAM,WAAW,qBAAqB;IACpC,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,iBAAiB,EAAE,MAAM,CAAC;IAE1B;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,aAAa,EAAE,MAAM,CAAC;IAEtB;;OAEG;IACH,MAAM,EAAE,OAAO,CAAC;IAEhB;;OAEG;IACH,MAAM,EACF,YAAY,GACZ,QAAQ,GACR,aAAa,GACb,YAAY,GACZ,WAAW,GACX,SAAS,GACT,YAAY,GACZ,WAAW,CAAC;IAEhB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IAEvB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IAEvB,MAAM,CAAC,EAAE,qBAAqB,CAAC,MAAM,CAAC;IAEtC;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IAEvB;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;;OAGG;IACH,QAAQ,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAE1B;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;OAEG;IACH,cAAc,CAAC,EAAE,qBAAqB,CAAC,aAAa,CAAC;CACtD;AAED,yBAAiB,qBAAqB,CAAC;IACrC,UAAiB,MAAM;QACrB,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAE1B;;WAEG;QACH,MAAM,CAAC,EAAE,MAAM,CAAC;KACjB;IAED,UAAiB,MAAM,CAAC;QACtB,UAAiB,IAAI;YACnB;;eAEG;YACH,IAAI,CAAC,EAAE,MAAM,CAAC;YAEd;;eAEG;YACH,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAErB;;eAEG;YACH,OAAO,CAAC,EAAE,MAAM,CAAC;YAEjB;;eAEG;YACH,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;SACvB;KACF;IAED;;OAEG;IACH,UAAiB,aAAa;QAC5B;;WAEG;QACH,SAAS,EAAE,MAAM,CAAC;QAElB;;WAEG;QACH,MAAM,EAAE,MAAM,CAAC;QAEf;;WAEG;QACH,KAAK,EAAE,MAAM,CAAC;KACf;CACF;AAED,MAAM,WAAW,iBAAiB;IAChC,IAAI,EAAE,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAEpC,MAAM,EAAE,MAAM,CAAC;CAChB;AAED,yBAAiB,iBAAiB,CAAC;IACjC,UAAiB,IAAI;QACnB,EAAE,EAAE,MAAM,CAAC;QAEX;;WAEG;QACH,iBAAiB,EAAE,MAAM,CAAC;QAE1B;;WAEG;QACH,UAAU,EAAE,MAAM,CAAC;QAEnB;;WAEG;QACH,QAAQ,EAAE,MAAM,CAAC;QAEjB;;WAEG;QACH,aAAa,EAAE,MAAM,CAAC;QAEtB;;WAEG;QACH,MAAM,EAAE,OAAO,CAAC;QAEhB;;WAEG;QACH,MAAM,EACF,YAAY,GACZ,QAAQ,GACR,aAAa,GACb,YAAY,GACZ,WAAW,GACX,SAAS,GACT,YAAY,GACZ,WAAW,CAAC;QAEhB;;WAEG;QACH,YAAY,CAAC,EAAE,MAAM,CAAC;QAEtB;;WAEG;QACH,aAAa,CAAC,EAAE,MAAM,CAAC;QAEvB;;WAEG;QACH,YAAY,CAAC,EAAE,MAAM,CAAC;QAEtB;;WAEG;QACH,aAAa,CAAC,EAAE,MAAM,CAAC;QAEvB,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC;QAErB;;WAEG;QACH,UAAU,CAAC,EAAE,MAAM,CAAC;QAEpB;;WAEG;QACH,UAAU,CAAC,EAAE,MAAM,CAAC;QAEpB;;WAEG;QACH,SAAS,CAAC,EAAE,MAAM,CAAC;QAEnB;;WAEG;QACH,aAAa,CAAC,EAAE,MAAM,CAAC;QAEvB;;WAEG;QACH,cAAc,CAAC,EAAE,MAAM,CAAC;QAExB;;;WAGG;QACH,QAAQ,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;QAE1B;;WAEG;QACH,cAAc,CAAC,EAAE,MAAM,CAAC;QAExB;;WAEG;QACH,cAAc,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC;KACrC;IAED,UAAiB,IAAI,CAAC;QACpB,UAAiB,MAAM;YACrB,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAE1B;;eAEG;YACH,MAAM,CAAC,EAAE,MAAM,CAAC;SACjB;QAED,UAAiB,MAAM,CAAC;YACtB,UAAiB,IAAI;gBACnB;;mBAEG;gBACH,IAAI,CAAC,EAAE,MAAM,CAAC;gBAEd;;mBAEG;gBACH,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;gBAErB;;mBAEG;gBACH,OAAO,CAAC,EAAE,MAAM,CAAC;gBAEjB;;mBAEG;gBACH,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;aACvB;SACF;QAED;;WAEG;QACH,UAAiB,aAAa;YAC5B;;eAEG;YACH,SAAS,EAAE,MAAM,CAAC;YAElB;;eAEG;YACH,MAAM,EAAE,MAAM,CAAC;YAEf;;eAEG;YACH,KAAK,EAAE,MAAM,CAAC;SACf;KACF;CACF;AAED,MAAM,WAAW,iBAAiB;IAChC;;;OAGG;IACH,iBAAiB,EAAE,KAAK,CAAC;IAEzB;;;OAGG;IACH,QAAQ,EAAE,sBAAsB,CAAC;IAEjC;;;;;;;OAOG;IACH,aAAa,EAAE,MAAM,CAAC;IAEtB;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC;CAC1C;AAED,MAAM,CAAC,OAAO,WAAW,OAAO,CAAC;IAC/B,OAAO,EACL,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,qBAAqB,IAAI,qBAAqB,EACnD,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,iBAAiB,IAAI,iBAAiB,GAC5C,CAAC;CACH"}