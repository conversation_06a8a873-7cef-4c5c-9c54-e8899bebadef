#!/bin/bash

echo "🔐 生成SSL证书..."

# 获取服务器公网IP
SERVER_IP=$(curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || echo "localhost")
echo "📡 检测到服务器IP: $SERVER_IP"

# 创建tmp目录
mkdir -p tmp

# 创建SSL配置文件
cat > tmp/ssl.conf << EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = CN
ST = Beijing
L = Beijing
O = MyCompany
OU = Dev
CN = $SERVER_IP

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
IP.1 = $SERVER_IP
DNS.1 = localhost
DNS.2 = $SERVER_IP
EOF

# 生成SSL证书
echo "🔑 正在生成SSL证书和私钥..."
openssl req -x509 -newkey rsa:2048 -nodes \
    -keyout tmp/server.key -out tmp/server.crt -days 365 \
    -config tmp/ssl.conf -extensions v3_req

if [ $? -eq 0 ]; then
    echo "✅ SSL证书生成成功！"
    echo "📁 证书文件位置："
    echo "   - 证书: tmp/server.crt"
    echo "   - 私钥: tmp/server.key"
    echo ""
    echo "🔍 证书信息："
    openssl x509 -in tmp/server.crt -text -noout | grep -A 1 "Subject:"
    openssl x509 -in tmp/server.crt -text -noout | grep -A 5 "Subject Alternative Name"
else
    echo "❌ SSL证书生成失败！"
    exit 1
fi

# 清理临时文件
rm tmp/ssl.conf

echo ""
echo "🚀 现在可以运行安装脚本："
echo "bash install_server_and_frontend.sh"
