# 📊 Klok CSV积分追踪系统

## 🚀 快速开始

### 启动程序
```bash
node main.js
```
或
```bash
npm start
```

### 查看表格
- **文件名**: `klok-scores.csv`
- **打开方式**: Excel、WPS、Google Sheets等
- **更新频率**: 每30秒自动更新

### 查看统计
```bash
npm run csv-view
```

## 📋 表格格式

### 基本结构
```
序号 | 私钥 | 地址 | 20250614 | 20250615 | 20250616 | ...
  1  | 0x... | 0x... |   125    |   150    |   175    | ...
  2  | 0x... | 0x... |   100    | 100(无变化) |   120    | ...
```

### 列说明
- **序号**: 钱包编号 (1, 2, 3...)
- **私钥**: 完整的钱包私钥
- **地址**: 完整的钱包地址
- **日期列**: 每天一列，格式为 YYYY/MM/DD

### 状态说明
- **数字**: 当天的总积分 (如: 125, 150)
- **执行中**: 任务正在运行
- **错误**: 任务执行失败
- **无数据**: 该日期无记录
- **数字(无变化)**: 积分与前一天相同，需要关注

## 🔴 积分变化检测

系统会自动检测积分是否有变化：
- ✅ **有变化**: 正常显示积分数字
- 🔴 **无变化**: 显示"积分(无变化)"，提醒需要关注

### 示例
```csv
序号,私钥,地址,2025/06/13,2025/06/14
1,"0x123...","0x123...",100,125        # 正常增长
2,"0x456...","0x456...",150,150(无变化) # 需要关注
```

## 📈 Excel使用技巧

### 1. 打开CSV文件
- 直接双击CSV文件
- 或在Excel中选择"打开" → 选择CSV文件

### 2. 数据筛选
```
步骤:
1. 选中表头行
2. 数据 → 筛选
3. 点击列标题的下拉箭头
4. 选择要筛选的内容
```

### 3. 突出显示问题
```
条件格式设置:
1. 选中日期列
2. 开始 → 条件格式 → 突出显示单元格规则
3. 文本包含 → 输入"无变化" → 选择红色背景
4. 文本包含 → 输入"错误" → 选择橙色背景
```

### 4. 制作图表
```
步骤:
1. 选中钱包地址和积分数据
2. 插入 → 图表 → 折线图
3. 可以看到积分变化趋势
```

## 🔧 功能特点

- ✅ **实时更新** - 程序运行时自动记录积分
- ✅ **自动扩展** - 新增钱包和日期自动添加
- ✅ **智能标记** - 积分无变化时标记提醒
- ✅ **Excel友好** - 标准CSV格式，支持中文

## 💡 使用建议

### 日常监控
1. 打开 `klok-scores.csv` 查看最新积分
2. 关注标记为"(无变化)"和"错误"的钱包
3. 使用 `npm run csv-view` 快速查看统计

### Excel技巧
- 使用条件格式突出显示问题钱包
- 制作折线图查看积分趋势
- 使用筛选功能查看特定状态

### 故障排除
- **CSV乱码**: 用记事本打开，另存为UTF-8编码
- **数据不更新**: 确认主程序正在运行
- **Excel格式问题**: 使用"数据" → "从文本"导入
