'use strict'

/* eslint-disable no-path-concat */

var test = require('tape')
var path = require('path')
var customTheme = require('./fixtures/custom')
var cardinal = require('..')

var file = path.join(__dirname, 'fixtures/foo.js')
var fileWithErrors = path.join(__dirname, 'fixtures/foo-with-errors.js')

test('supplying custom theme', function(t) {
  cardinal.highlightFile(file, { theme: customTheme }, function(err, highlighted) {
    t.equals(null, err, 'no error')
    t.equals(highlighted, '\u001b[94mfunction\u001b[39m \u001b[96mfoo\u001b[39m\u001b[90m(\u001b[39m\u001b[90m)\u001b[39m \u001b[33m{\u001b[39m \n  \u001b[32mvar\u001b[39m \u001b[96ma\u001b[39m \u001b[93m=\u001b[39m \u001b[34m3\u001b[39m\u001b[90m;\u001b[39m \u001b[31mreturn\u001b[39m \u001b[96ma\u001b[39m \u001b[93m>\u001b[39m \u001b[34m2\u001b[39m \u001b[93m?\u001b[39m \u001b[31mtrue\u001b[39m \u001b[93m:\u001b[39m \u001b[91mfalse\u001b[39m\u001b[90m;\u001b[39m \n\u001b[33m}\u001b[39m\n')
    t.end()
  })
})

test('not supplying custom theme', function(t) {
  cardinal.highlightFile(file, function(err, highlighted) {
    t.equals(null, err, 'no error')
    t.equals(highlighted, '\u001b[94mfunction\u001b[39m \u001b[37mfoo\u001b[39m\u001b[90m(\u001b[39m\u001b[90m)\u001b[39m \u001b[33m{\u001b[39m \n  \u001b[32mvar\u001b[39m \u001b[37ma\u001b[39m \u001b[93m=\u001b[39m \u001b[34m3\u001b[39m\u001b[90m;\u001b[39m \u001b[31mreturn\u001b[39m \u001b[37ma\u001b[39m \u001b[93m>\u001b[39m \u001b[34m2\u001b[39m \u001b[93m?\u001b[39m \u001b[91mtrue\u001b[39m \u001b[93m:\u001b[39m \u001b[91mfalse\u001b[39m\u001b[90m;\u001b[39m \n\u001b[33m}\u001b[39m\n')
    t.end()
  })
})

test('syntactically invalid code', function(t) {
  cardinal.highlightFile(fileWithErrors, function(err, highlighted) {
    t.equals(null, err, 'no error')
    t.equals(highlighted, '\u001b[94mfunction\u001b[39m \u001b[90m(\u001b[39m\u001b[90m)\u001b[39m \u001b[33m{\u001b[39m \n  \u001b[32mvar\u001b[39m \u001b[37ma\u001b[39m \u001b[93m=\u001b[39m \u001b[34m3\u001b[39m\u001b[90m;\u001b[39m \u001b[31mreturn\u001b[39m \u001b[37ma\u001b[39m \u001b[93m>\u001b[39m \u001b[34m2\u001b[39m \u001b[93m?\u001b[39m \u001b[91mtrue\u001b[39m \u001b[93m:\u001b[39m \u001b[91mfalse\u001b[39m\u001b[90m;\u001b[39m \n\u001b[33m}\u001b[39m\u001b[90m;\u001b[39m\n')
    t.end()
  })
})

test('non existing file', function(t) {
  cardinal.highlightFile('./not/existing', function(err, highlighted) {
    t.ok((/ENOENT. .*not.existing/).test(err.message))
    t.end()
  })
})
