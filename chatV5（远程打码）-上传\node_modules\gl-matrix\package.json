{"name": "gl-matrix", "description": "Javascript Matrix and Vector library for High Performance WebGL apps", "version": "2.8.1", "main": "dist/gl-matrix.js", "module": "lib/gl-matrix.js", "homepage": "http://glmatrix.net", "license": "MIT", "bugs": {"url": "https://github.com/toji/gl-matrix/issues"}, "repository": {"type": "git", "url": "https://github.com/toji/gl-matrix.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "mocha --require babel-register --recursive spec", "old_test": "jasmine-node spec", "doc": "jsdoc -c jsdoc.config.json", "update-license-version": "node utils/update-license-version.js", "build": "webpack --config utils/webpack.config.js", "build-min": "webpack --config utils/webpack.config.min.js", "build-esm": "BABEL_ENV=esm babel src -d lib", "build-all": "npm run update-license-version&&npm run build&&npm run build-min&&npm run build-esm"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-loader": "^7.1.4", "babel-preset-env": "^1.7.0", "babel-register": "^6.26.0", "jsdoc": "^3.5.5", "mocha": "^5.1.1", "node-libs-browser": "^2.1.0", "uglifyjs-webpack-plugin": "^1.2.5", "webpack": "^4.8.3", "webpack-cli": "^2.1.3"}, "dependencies": {}, "sideEffects": false}