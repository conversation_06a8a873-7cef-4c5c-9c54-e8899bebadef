{"name": "<PERSON><PERSON>-blessed-contrib", "version": "1.0.0", "description": "Drawing in terminal with unicode braille characters", "main": "index.js", "scripts": {"test": "echo \"OK 1337/1337 passed! (100%)\""}, "author": "<PERSON><PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "**************:madbence/node-drawille.git"}, "license": "MIT", "bugs": {"url": "https://github.com/madbence/node-drawille/issues"}, "devDependencies": {"bresenham": "0.0.3", "gl-matrix": "^2.1.0"}}