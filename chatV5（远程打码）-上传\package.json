{"name": "klok-bot", "version": "1.0.0", "description": "Automation script for KlokAI chat", "main": "main.js", "scripts": {"start": "node main.js", "csv-view": "node csv-viewer.js"}, "repository": {"type": "git", "url": "https://github.com/rpchubs/Klok-BOT.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"axios": "^1.8.4", "blessed": "^0.1.81", "blessed-contrib": "^4.11.0", "boxen": "^8.0.1", "crypto": "^1.0.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "ethers": "^6.13.5", "groq-sdk": "^0.15.0", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.1", "p-limit": "^2.3.0"}}