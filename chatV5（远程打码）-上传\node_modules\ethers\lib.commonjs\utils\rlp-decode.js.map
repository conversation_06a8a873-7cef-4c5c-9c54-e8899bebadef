{"version": 3, "file": "rlp-decode.js", "sourceRoot": "", "sources": ["../../src.ts/utils/rlp-decode.ts"], "names": [], "mappings": ";AAAA,gDAAgD;;;AAEhD,uCAAoC;AACpC,2CAAqD;AACrD,uCAAqC;AAKrC,SAAS,WAAW,CAAC,KAAa;IAC9B,IAAI,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAChC,OAAO,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;QAAE,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC;KAAE;IACpD,OAAO,IAAI,GAAG,MAAM,CAAC;AACzB,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAgB,EAAE,MAAc,EAAE,MAAc;IACvE,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;QAC7B,MAAM,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KAC9C;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAOD,SAAS,eAAe,CAAC,IAAgB,EAAE,MAAc,EAAE,WAAmB,EAAE,MAAc;IAC1F,MAAM,MAAM,GAAe,EAAE,CAAC;IAE9B,OAAO,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,EAAE;QACtC,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAE3C,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAE5B,WAAW,IAAI,OAAO,CAAC,QAAQ,CAAC;QAChC,IAAA,kBAAM,EAAC,WAAW,IAAI,MAAM,GAAG,CAAC,GAAG,MAAM,EAAE,sBAAsB,EAAE,gBAAgB,EAAE;YACjF,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM;SAC/B,CAAC,CAAC;KACN;IAED,OAAO,EAAC,QAAQ,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAC,CAAC;AACpD,CAAC;AAED,+CAA+C;AAC/C,SAAS,OAAO,CAAC,IAAgB,EAAE,MAAc;IAC7C,IAAA,kBAAM,EAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,gBAAgB,EAAE,gBAAgB,EAAE;QAC1D,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC;KACrC,CAAC,CAAC;IAEH,MAAM,WAAW,GAAG,CAAC,MAAc,EAAE,EAAE;QACnC,IAAA,kBAAM,EAAC,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE,8BAA8B,EAAE,gBAAgB,EAAE;YAC5E,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM;SAC5C,CAAC,CAAC;IACP,CAAC,CAAC;IAEF,iCAAiC;IACjC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;QACtB,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QACzC,WAAW,CAAC,MAAM,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;QAEvC,MAAM,MAAM,GAAG,iBAAiB,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,EAAE,YAAY,CAAC,CAAC;QACjE,WAAW,CAAC,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,MAAM,CAAC,CAAC;QAEhD,OAAO,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC,GAAG,YAAY,EAAE,YAAY,GAAG,MAAM,CAAC,CAAC;KAE1F;SAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QACnC,WAAW,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;QAEjC,OAAO,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;KAE5D;SAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;QAC7B,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QACzC,WAAW,CAAC,MAAM,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;QAEvC,MAAM,MAAM,GAAG,iBAAiB,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,EAAE,YAAY,CAAC,CAAC;QACjE,WAAW,CAAC,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,MAAM,CAAC,CAAC;QAEhD,MAAM,MAAM,GAAG,IAAA,iBAAO,EAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,YAAY,EAAE,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC;QAClG,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC,GAAG,YAAY,GAAG,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAA;KAEnE;SAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QACnC,WAAW,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;QAEjC,MAAM,MAAM,GAAG,IAAA,iBAAO,EAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACpE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAA;KACpD;IAED,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;AAC9D,CAAC;AAED;;GAEG;AACH,SAAgB,SAAS,CAAC,KAAgB;IACtC,MAAM,IAAI,GAAG,IAAA,kBAAQ,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACrC,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACjC,IAAA,0BAAc,EAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,MAAM,EAAE,mCAAmC,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACrG,OAAO,OAAO,CAAC,MAAM,CAAC;AAC1B,CAAC;AALD,8BAKC"}