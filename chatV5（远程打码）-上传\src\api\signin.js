const fs = require("fs");
const axios = require("axios");
const { Wallet } = require("ethers");
const crypto = require("crypto");
const path = require("path");
const config = require("../../config");
const { log } = require("../utils");
const pLimit = require("p-limit");
const yesCaptchaService = require("../services/yesCaptcha");
const walletProxyManager = require("../services/walletProxyManager");
const { HttpsProxyAgent } = require("https-proxy-agent");
const { HttpProxyAgent } = require("http-proxy-agent");

async function signMessage(wallet) {
    const nonce = generateNonce();
    const timestamp = new Date().toISOString();
    const message = `klokapp.ai wants you to sign in with your Ethereum account:
${wallet.address}


URI: https://klokapp.ai/
Version: 1
Chain ID: 1
Nonce: ${nonce}
Issued At: ${timestamp}`;

    return {
        signature: await wallet.signMessage(message),
        message: message,
        nonce: nonce,
        timestamp: timestamp
    };
}

function generateNonce() {
    return Buffer.from(crypto.randomBytes(48)).toString("hex");
}

const MAX_RETRIES = 1; 
const RETRY_DELAY = 3000;

async function authenticate(wallet) {
    let attempt = 0;
    while (attempt < MAX_RETRIES) {
        try {
            const signResult = await signMessage(wallet);
            
            // 获取reCAPTCHA token
            log(`[INFO] 正在获取reCAPTCHA token...`, "info");
            const recaptchaToken = await yesCaptchaService.getRecaptchaToken();
            
            const payload = {
                signedMessage: signResult.signature,
                message: signResult.message,
                referral_code: config.REFERRAL_CODE.referral_code,
                recaptcha_token: recaptchaToken
            };

            log(`[INFO] 正在验证钱包 ${wallet.address}...`, "info");

            // 获取钱包绑定的代理
            const binding = walletProxyManager.getWalletBinding(wallet.address);
            const proxyUrl = binding ? binding.proxyUrl : null;
            
            // 配置请求选项
            const requestOptions = {
                headers: config.DEFAULT_HEADERS,
                timeout: 60000
            };
            
            // 如果有绑定代理，使用代理
            if (proxyUrl) {
                log(`[INFO] 使用钱包 ${wallet.address} 绑定的代理: ${proxyUrl}`, "info");
                if (config.BASE_URL.startsWith("https")) {
                    requestOptions.httpsAgent = new HttpsProxyAgent(proxyUrl);
                } else {
                    requestOptions.httpAgent = new HttpProxyAgent(proxyUrl);
                }
            }

            const response = await axios.post(`${config.BASE_URL}/verify`, payload, requestOptions);

            const { session_token } = response.data;
            log(`[SUCCESS] 已获取 ${wallet.address} 的会话令牌`, "success");

            // 更新钱包-代理-Token绑定关系
            if (config.ENABLE_WALLET_PROXY_BINDING) {
                walletProxyManager.updateWalletToken(wallet.address, session_token);
                log(`[INFO] 已更新钱包 ${wallet.address} 的Token绑定关系`, "info");
            }

            const tokenPath = path.join(process.cwd(), "session-token.key");
            fs.appendFileSync(tokenPath, `${session_token}\n`);
            return session_token;
        } catch (error) {
            attempt++;
            log(`[ERROR] 验证尝试 ${attempt} 失败，钱包地址: ${wallet.address}: ${error.message}`, "error");
            if (error.response) {
                log(`[ERROR] 状态码: ${error.response.status}, 数据:`, error.response.data, "error");
            }

            if (attempt < MAX_RETRIES) {
                log(`[INFO] 将在 ${RETRY_DELAY / 1000} 秒后重试验证钱包 ${wallet.address}...`, "info");
                await delay(RETRY_DELAY);
            }
        }
    }
    log(`[ERROR] 所有验证尝试均失败，钱包地址: ${wallet.address}`, "error");
    return null;
}

function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

module.exports = {
    authenticate,
    signMessage,
    generateNonce,
    authenticateAllWallets: async (privateKeys) => {
        const tokens = [];
        const limit = pLimit(config.THREADS || 10); 

        if (!privateKeys || privateKeys.length === 0) {
            log("[ERROR] 未提供私钥", "error");
            return tokens;
        }

        // 导入验证Token的函数
        const { verifyToken } = require('./auth');

        const promises = privateKeys.map(key =>
            limit(async () => {
                try {
                    const wallet = new Wallet(key.trim());
                    
                    // 检查是否已有有效的Token
                    if (config.ENABLE_WALLET_PROXY_BINDING) {
                        const binding = walletProxyManager.getWalletBinding(wallet.address);
                        if (binding && binding.sessionToken) {
                            // 验证Token是否有效
                            log(`[INFO] 钱包 ${wallet.address} 已有绑定Token，正在验证是否有效...`, "info");
                            
                            try {
                                const isValid = await verifyToken(binding.sessionToken);
                                if (isValid) {
                                    log(`[SUCCESS] 钱包 ${wallet.address} 的绑定Token有效，直接使用`, "success");
                                    tokens.push(binding.sessionToken);
                                    
                                    // 将有效的Token写入session-token.key文件
                                    const tokenPath = path.join(process.cwd(), "session-token.key");
                                    fs.appendFileSync(tokenPath, `${binding.sessionToken}\n`);
                                    
                                    return;
                                } else {
                                    log(`[WARNING] 钱包 ${wallet.address} 的绑定Token已过期，需要重新获取`, "warning");
                                }
                            } catch (verifyError) {
                                log(`[WARNING] 验证钱包 ${wallet.address} 的Token失败: ${verifyError.message}`, "warning");
                            }
                        }
                    }
                    
                    // 获取新Token
                    log(`[INFO] 为钱包 ${wallet.address} 获取新Token`, "info");
                    const token = await authenticate(wallet);
                    if (token) tokens.push(token);
                } catch (error) {
                    log(`[ERROR] 无效的私钥或认证失败: ${error.message}`, "error");
                    // 记录代理失败
                    try {
                        const wallet = new Wallet(key.trim());
                        if (config.ENABLE_WALLET_PROXY_BINDING) {
                            walletProxyManager.recordProxyFailure(wallet.address, error.message);
                        }
                    } catch (walletError) {
                        log(`[ERROR] 无法解析钱包地址: ${walletError.message}`, "error");
                    }
                }
            })
        );

        await Promise.all(promises);
        return tokens;
    }
};