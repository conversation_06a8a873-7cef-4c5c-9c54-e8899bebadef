{"version": 3, "file": "signer-noncemanager.d.ts", "sourceRoot": "", "sources": ["../../src.ts/providers/signer-noncemanager.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AAEtD,OAAO,KAAK,EAAE,eAAe,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAExE,OAAO,KAAK,EACR,QAAQ,EAAE,QAAQ,EAAE,kBAAkB,EAAE,mBAAmB,EAC9D,MAAM,eAAe,CAAC;AACvB,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AAG1C;;;;GAIG;AACH,qBAAa,YAAa,SAAQ,cAAc;;IAC5C;;OAEG;IACH,MAAM,EAAG,MAAM,CAAC;IAKhB;;OAEG;gBACS,MAAM,EAAE,MAAM;IAQpB,UAAU,IAAI,OAAO,CAAC,MAAM,CAAC;IAInC,OAAO,CAAC,QAAQ,EAAE,IAAI,GAAG,QAAQ,GAAG,YAAY;IAI1C,QAAQ,CAAC,QAAQ,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC;IAapD;;;OAGG;IACH,SAAS,IAAI,IAAI;IAIjB;;;OAGG;IACH,KAAK,IAAI,IAAI;IAKP,eAAe,CAAC,EAAE,EAAE,kBAAkB,GAAG,OAAO,CAAC,mBAAmB,CAAC;IAY3E,eAAe,CAAC,EAAE,EAAE,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC;IAIxD,WAAW,CAAC,OAAO,EAAE,MAAM,GAAG,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;IAI1D,aAAa,CAAC,MAAM,EAAE,eAAe,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,cAAc,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;CAGpI"}