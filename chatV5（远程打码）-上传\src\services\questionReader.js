const fs = require('fs');
const path = require('path');
const config = require('../../config');
const { log, logToFile } = require('../utils');

/**
 * 问题读取服务
 * 从question.txt文件中读取问题，而不使用Gemini API
 */
class QuestionReader {
  constructor() {
    this.questionsFilePath = path.resolve(process.cwd(), config.QUESTIONS_FILE || './question.txt');
    this.questions = [];
    this.loadQuestions();
  }

  /**
   * 加载问题
   */
  loadQuestions() {
    try {
      if (fs.existsSync(this.questionsFilePath)) {
        const data = fs.readFileSync(this.questionsFilePath, 'utf8');
        this.questions = data.split(/\r?\n/)
          .map(line => line.trim())
          .filter(line => line.length > 0);
        
        log(`[问题读取] 已加载${this.questions.length}个问题`, 'info');
        logToFile('问题加载成功', { count: this.questions.length });
      } else {
        log('[问题读取] 问题文件不存在，请创建question.txt文件', 'error');
        logToFile('问题文件不存在', { path: this.questionsFilePath });
        this.questions = [];
      }
    } catch (error) {
      log(`[问题读取] 加载问题失败: ${error.message}`, 'error');
      logToFile('加载问题失败', { error: error.message });
      this.questions = [];
    }
  }

  /**
   * 获取随机问题
   * @returns {string} 随机问题
   */
  getRandomQuestion() {
    if (this.questions.length === 0) {
      this.loadQuestions();
      if (this.questions.length === 0) {
        return "你好，今天天气怎么样？"; // 默认问题
      }
    }
    
    const randomIndex = Math.floor(Math.random() * this.questions.length);
    return this.questions[randomIndex];
  }
  
  /**
   * 获取问题库中的问题数量
   * @returns {number} 问题数量
   */
  getQuestionCount() {
    return this.questions.length;
  }

  /**
   * 获取指定索引的问题
   * @param {number} index 问题索引
   * @returns {string} 指定索引的问题
   */
  getQuestionByIndex(index) {
    if (this.questions.length === 0) {
      this.loadQuestions();
      if (this.questions.length === 0) {
        return "你好，今天天气怎么样？"; // 默认问题
      }
    }
    
    if (index < 0 || index >= this.questions.length) {
      log(`[问题读取] 问题索引${index}超出范围`, 'warning');
      return this.getRandomQuestion();
    }
    
    return this.questions[index];
  }

  /**
   * 获取所有问题
   * @returns {Array<string>} 所有问题
   */
  getAllQuestions() {
    if (this.questions.length === 0) {
      this.loadQuestions();
    }
    return [...this.questions];
  }

  /**
   * 重新加载问题
   */
  reloadQuestions() {
    this.loadQuestions();
    return this.questions.length;
  }
}

// 导出单例
const questionReader = new QuestionReader();
module.exports = questionReader;
