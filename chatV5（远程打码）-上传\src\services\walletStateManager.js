const fs = require('fs');
const path = require('path');
const { log, logToFile } = require('../utils/simple-logger');

class WalletStateManager {
  constructor() {
    this.stateFilePath = path.join(process.cwd(), 'wallet-queue-state.json');
    this.walletStates = {};
    this.loadState();
  }

  // 加载钱包状态
  loadState() {
    try {
      if (fs.existsSync(this.stateFilePath)) {
        const data = fs.readFileSync(this.stateFilePath, 'utf8');
        this.walletStates = JSON.parse(data);
        log(`已从 ${this.stateFilePath} 加载钱包状态`, 'info');
      } else {
        log(`钱包状态文件不存在，将创建新文件`, 'info');
        this.walletStates = {};
        this.saveState();
      }
    } catch (error) {
      log(`加载钱包状态失败: ${error.message}`, 'error');
      logToFile('加载钱包状态失败', { error: error.message, stack: error.stack });
      this.walletStates = {};
    }
  }

  // 保存钱包状态
  saveState() {
    try {
      fs.writeFileSync(this.stateFilePath, JSON.stringify(this.walletStates, null, 2), 'utf8');
    } catch (error) {
      log(`保存钱包状态失败: ${error.message}`, 'error');
      logToFile('保存钱包状态失败', { error: error.message, stack: error.stack });
    }
  }

  // 获取钱包状态
  getWalletState(walletAddress) {
    return this.walletStates[walletAddress] || null;
  }

  // 获取所有钱包状态
  getAllWalletStates() {
    return this.walletStates;
  }

  // 更新钱包状态
  updateWalletState(walletAddress, state) {
    this.walletStates[walletAddress] = {
      ...this.getWalletState(walletAddress),
      ...state,
      lastUpdated: new Date().toISOString()
    };
    this.saveState();
  }

  // 设置钱包下次执行时间
  setNextExecutionTime(walletAddress, completionTime = null) {
    const now = completionTime ? new Date(completionTime) : new Date();
    
    // 生成24.5到26.5小时的随机延迟（毫秒）
    const minDelay = 24.5 * 60 * 60 * 1000; // 24.5小时
    const maxDelay = 26.5 * 60 * 60 * 1000; // 26.5小时
    // 正确计算随机延迟，确保在minDelay和maxDelay之间
    const randomDelay = Math.floor(Math.random() * (maxDelay - minDelay)) + minDelay;
    
    // 计算下次执行时间
    const nextExecutionTime = new Date(now.getTime() + randomDelay);
    
    // 更新钱包状态
    this.updateWalletState(walletAddress, {
      lastExecutionTime: now.toISOString(),
      nextExecutionTime: nextExecutionTime.toISOString(),
      randomDelay: randomDelay, // 保存随机延迟（毫秒）
      delayHours: (randomDelay / (60 * 60 * 1000)).toFixed(2) // 保存延迟小时数，便于查看
    });
    
    return nextExecutionTime;
  }

  // 初始化新钱包
  initializeWallet(walletAddress, initialDelay = null) {
    // 如果钱包已存在，不做任何操作
    if (this.walletStates[walletAddress]) {
      return;
    }
    
    const now = new Date();
    let nextExecutionTime;
    
    if (initialDelay === null) {
      // 计算当前钱包总数，用于均匀分布
      const totalWallets = Object.keys(this.walletStates).length;
      
      if (totalWallets === 0) {
        // 第一个钱包立即执行
        nextExecutionTime = new Date(now.getTime() + 60 * 1000); // 1分钟后执行
      } else {
        // 将钱包均匀分布在24小时内
        // 添加小的随机性以避免完全均匀
        const baseDelay = (24 * 60 * 60 * 1000) / (totalWallets + 1);
        const randomFactor = Math.random() * baseDelay * 0.5; // 添加最多50%的随机性
        const calculatedDelay = baseDelay * (totalWallets + 1) + randomFactor;
        
        // 确保延迟不超过24小时
        const finalDelay = Math.min(calculatedDelay, 24 * 60 * 60 * 1000);
        nextExecutionTime = new Date(now.getTime() + finalDelay);
        
        log(`钱包 ${walletAddress.substring(0, 8)}... 初始化延迟: ${(finalDelay / (60 * 60 * 1000)).toFixed(2)}小时`, "info");
      }
    } else {
      // 使用指定的初始延迟
      nextExecutionTime = new Date(now.getTime() + initialDelay);
    }
    
    this.updateWalletState(walletAddress, {
      initialized: true,
      completedTasks: 0,
      lastExecutionTime: null,
      nextExecutionTime: nextExecutionTime.toISOString(),
      initialDelay: initialDelay || 'calculated'
    });
  }

  // 获取下一个可执行的钱包
  getNextExecutableWallet() {
    const now = new Date();
    let nextWallet = null;
    let earliestTime = null;
    
    // 遍历所有钱包，找到下一个可执行的钱包
    for (const [address, state] of Object.entries(this.walletStates)) {
      if (!state.nextExecutionTime) continue;
      
      const nextExecTime = new Date(state.nextExecutionTime);
      
      // 如果钱包可以执行（当前时间已经超过了下次执行时间）
      if (nextExecTime <= now) {
        // 如果还没有找到可执行的钱包，或者这个钱包的执行时间更早
        if (nextWallet === null || nextExecTime < earliestTime) {
          nextWallet = address;
          earliestTime = nextExecTime;
        }
      }
    }
    
    return nextWallet;
  }

  // 获取下一个将要可执行的钱包及其等待时间（毫秒）
  getNextWalletWaitTime() {
    const now = new Date().getTime();
    let nextWallet = null;
    let minWaitTime = Number.MAX_SAFE_INTEGER;
    
    // 遍历所有钱包，找到最早将要可执行的钱包
    for (const [address, state] of Object.entries(this.walletStates)) {
      if (!state.nextExecutionTime) continue;
      
      const nextExecTime = new Date(state.nextExecutionTime).getTime();
      const waitTime = nextExecTime - now;
      
      // 如果这个钱包的等待时间比当前最小等待时间更短
      if (waitTime > 0 && waitTime < minWaitTime) {
        nextWallet = address;
        minWaitTime = waitTime;
      }
    }
    
    return { nextWallet, waitTime: minWaitTime };
  }

  // 记录任务完成
  recordTaskCompletion(walletAddress) {
    const state = this.getWalletState(walletAddress) || {};
    const completedTasks = (state.completedTasks || 0) + 1;
    
    this.updateWalletState(walletAddress, {
      completedTasks: completedTasks,
      lastCompletionTime: new Date().toISOString()
    });
    
    // 设置下次执行时间
    this.setNextExecutionTime(walletAddress);
    
    return completedTasks;
  }

  // 获取所有可执行的钱包
  getExecutableWallets() {
    const now = new Date();
    const executableWallets = [];
    
    for (const [address, state] of Object.entries(this.walletStates)) {
      if (!state.nextExecutionTime) continue;
      
      const nextExecTime = new Date(state.nextExecutionTime);
      if (nextExecTime <= now) {
        executableWallets.push({
          address,
          nextExecutionTime: state.nextExecutionTime,
          lastExecutionTime: state.lastExecutionTime,
          completedTasks: state.completedTasks || 0
        });
      }
    }
    
    // 按下次执行时间排序（最早的在前）
    executableWallets.sort((a, b) => {
      return new Date(a.nextExecutionTime) - new Date(b.nextExecutionTime);
    });
    
    return executableWallets;
  }

  // 获取钱包统计信息
  getWalletStats() {
    const now = new Date();
    const stats = {
      totalWallets: Object.keys(this.walletStates).length,
      executableNow: 0,
      pendingExecution: 0,
      totalCompletedTasks: 0,
      nextExecutionIn: null
    };
    
    let earliestPendingTime = Number.MAX_SAFE_INTEGER;
    
    for (const [address, state] of Object.entries(this.walletStates)) {
      // 统计已完成任务总数
      stats.totalCompletedTasks += (state.completedTasks || 0);
      
      if (!state.nextExecutionTime) continue;
      
      const nextExecTime = new Date(state.nextExecutionTime);
      const waitTime = nextExecTime.getTime() - now.getTime();
      
      if (waitTime <= 0) {
        // 当前可执行
        stats.executableNow++;
      } else {
        // 等待执行
        stats.pendingExecution++;
        
        // 更新最早的等待时间
        if (waitTime < earliestPendingTime) {
          earliestPendingTime = waitTime;
          stats.nextExecutionIn = waitTime;
        }
      }
    }
    
    return stats;
  }
}

// 创建单例实例
const walletStateManager = new WalletStateManager();

module.exports = walletStateManager;
