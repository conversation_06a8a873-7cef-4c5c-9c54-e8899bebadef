"use strict";
exports.eachWeekendOfYear = eachWeekendOfYear;
var _index = require("./eachWeekendOfInterval.cjs");
var _index2 = require("./endOfYear.cjs");
var _index3 = require("./startOfYear.cjs");

/**
 * The {@link eachWeekendOfYear} function options.
 */

/**
 * @name eachWeekendOfYear
 * @category Year Helpers
 * @summary List all the Saturdays and Sundays in the year.
 *
 * @description
 * Get all the Saturdays and Sundays in the year.
 *
 * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).
 * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.
 *
 * @param date - The given year
 * @param options - An object with options
 *
 * @returns An array containing all the Saturdays and Sundays
 *
 * @example
 * // Lists all Saturdays and Sundays in the year
 * const result = eachWeekendOfYear(new Date(2020, 1, 1))
 * //=> [
 * //   Sat Jan 03 2020 00:00:00,
 * //   Sun Jan 04 2020 00:00:00,
 * //   ...
 * //   Sun Dec 27 2020 00:00:00
 * // ]
 * ]
 */
function eachWeekendOfYear(date, options) {
  const start = (0, _index3.startOfYear)(date, options);
  const end = (0, _index2.endOfYear)(date, options);
  return (0, _index.eachWeekendOfInterval)({ start, end }, options);
}
