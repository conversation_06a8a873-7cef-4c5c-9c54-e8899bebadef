{"name": "sparkline", "description": "A JavaScript implementation of 'spark'.", "version": "0.1.2", "homepage": "https://github.com/shiwano/sparkline", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/shiwano/sparkline.git"}, "bugs": {"url": "https://github.com/shiwano/sparkline/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/shiwano/sparkline/blob/master/LICENSE-MIT"}], "main": "lib/sparkline", "bin": {"sparkline": "bin/sparkline"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "grunt nodeunit"}, "devDependencies": {"grunt-contrib-jshint": "~0.6.4", "grunt-contrib-nodeunit": "~0.2.1", "grunt-contrib-watch": "~0.5.3", "grunt": "~0.4.1"}, "keywords": ["spark", "graph"], "dependencies": {"nopt": "~2.1.2", "here": "0.0.2"}}