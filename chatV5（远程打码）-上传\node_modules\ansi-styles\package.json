{"name": "ansi-styles", "version": "2.2.1", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": "chalk/ansi-styles", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": ["Sindre Sorhus <<EMAIL>> (sindresorhus.com)", "<PERSON> <<EMAIL>> (jbnicolai.com)"], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"mocha": "*"}}