{"version": 3, "file": "provider-websocket.d.ts", "sourceRoot": "", "sources": ["../../src.ts/providers/provider-websocket.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AAEtD,OAAO,KAAK,EAAE,yBAAyB,EAAC,MAAM,uBAAuB,CAAC;AACtE,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE/C;;GAEG;AACH,MAAM,WAAW,aAAa;IAC1B,MAAM,EAAE,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;IAC9C,SAAS,EAAE,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;IACjD,OAAO,EAAE,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;IAE/C,UAAU,EAAE,MAAM,CAAC;IAEnB,IAAI,CAAC,OAAO,EAAE,GAAG,GAAG,IAAI,CAAC;IACzB,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CAC/C;AAED;;;GAGG;AACH,MAAM,MAAM,gBAAgB,GAAG,MAAM,aAAa,CAAC;AAEnD;;;;;;;;;GASG;AACH,qBAAa,iBAAkB,SAAQ,cAAc;;IAIjD,IAAI,SAAS,IAAI,aAAa,CAG7B;gBAEW,GAAG,EAAE,MAAM,GAAG,aAAa,GAAG,gBAAgB,EAAE,OAAO,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,EAAE,yBAAyB;IA2C/G,MAAM,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAItC,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;CAOjC"}