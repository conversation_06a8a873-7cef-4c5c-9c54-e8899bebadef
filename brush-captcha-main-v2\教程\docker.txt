第1步：完全卸载Docker

# 停止所有Docker容器
docker stop $(docker ps -aq) 2>/dev/null || true

# 删除所有Docker容器
docker rm $(docker ps -aq) 2>/dev/null || true

# 删除所有Docker镜像
docker rmi $(docker images -q) 2>/dev/null || true

# 停止Docker服务
systemctl stop docker 2>/dev/null || true

# 卸载所有Docker相关包
apt remove -y docker docker-engine docker.io containerd runc docker-compose docker-compose-plugin docker-ce docker-ce-cli

# 删除Docker数据目录
rm -rf /var/lib/docker
rm -rf /var/lib/containerd
rm -rf /etc/docker
rm -rf ~/.docker

# 删除Docker仓库配置
rm -f /etc/apt/sources.list.d/docker.list
rm -f /etc/apt/keyrings/docker.gpg

# 清理系统
apt autoremove -y
apt autoclean
apt update


第2步：重新安装Docker

# 更新系统
apt update && apt upgrade -y

# 安装Docker（官方脚本）
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装Python依赖
apt install -y python3-distutils python3-setuptools

# 启动Docker服务
systemctl start docker
systemctl enable docker


# 安装docker-compose
apt install -y docker-compose

# 如果docker-compose有问题，下载最新版本
if ! docker-compose --version 2>/dev/null; then
    curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
fi

# 验证安装
docker --version
docker-compose --version




重新部署：
cd ~/brush-captcha-main/client
docker compose down
docker compose up -d --build