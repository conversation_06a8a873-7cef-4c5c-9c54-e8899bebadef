(()=>{var A;function C(G){return C=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},C(G)}function x(G,J){var X=Object.keys(G);if(Object.getOwnPropertySymbols){var Y=Object.getOwnPropertySymbols(G);J&&(Y=Y.filter(function(Z){return Object.getOwnPropertyDescriptor(G,Z).enumerable})),X.push.apply(X,Y)}return X}function Q(G){for(var J=1;J<arguments.length;J++){var X=arguments[J]!=null?arguments[J]:{};J%2?x(Object(X),!0).forEach(function(Y){K(G,Y,X[Y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(X)):x(Object(X)).forEach(function(Y){Object.defineProperty(G,Y,Object.getOwnPropertyDescriptor(X,Y))})}return G}function K(G,J,X){if(J=N(J),J in G)Object.defineProperty(G,J,{value:X,enumerable:!0,configurable:!0,writable:!0});else G[J]=X;return G}function N(G){var J=W(G,"string");return C(J)=="symbol"?J:String(J)}function W(G,J){if(C(G)!="object"||!G)return G;var X=G[Symbol.toPrimitive];if(X!==void 0){var Y=X.call(G,J||"default");if(C(Y)!="object")return Y;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(G)}var z=Object.defineProperty,XG=function G(J,X){for(var Y in X)z(J,Y,{get:X[Y],enumerable:!0,configurable:!0,set:function Z(B){return X[Y]=function(){return B}}})},D={lessThanXSeconds:{one:"\u5C11\u65BC 1 \u79D2",other:"\u5C11\u65BC {{count}} \u79D2"},xSeconds:{one:"1 \u79D2",other:"{{count}} \u79D2"},halfAMinute:"\u534A\u5206\u9418",lessThanXMinutes:{one:"\u5C11\u65BC 1 \u5206\u9418",other:"\u5C11\u65BC {{count}} \u5206\u9418"},xMinutes:{one:"1 \u5206\u9418",other:"{{count}} \u5206\u9418"},xHours:{one:"1 \u5C0F\u6642",other:"{{count}} \u5C0F\u6642"},aboutXHours:{one:"\u5927\u7D04 1 \u5C0F\u6642",other:"\u5927\u7D04 {{count}} \u5C0F\u6642"},xDays:{one:"1 \u5929",other:"{{count}} \u5929"},aboutXWeeks:{one:"\u5927\u7D04 1 \u500B\u661F\u671F",other:"\u5927\u7D04 {{count}} \u500B\u661F\u671F"},xWeeks:{one:"1 \u500B\u661F\u671F",other:"{{count}} \u500B\u661F\u671F"},aboutXMonths:{one:"\u5927\u7D04 1 \u500B\u6708",other:"\u5927\u7D04 {{count}} \u500B\u6708"},xMonths:{one:"1 \u500B\u6708",other:"{{count}} \u500B\u6708"},aboutXYears:{one:"\u5927\u7D04 1 \u5E74",other:"\u5927\u7D04 {{count}} \u5E74"},xYears:{one:"1 \u5E74",other:"{{count}} \u5E74"},overXYears:{one:"\u8D85\u904E 1 \u5E74",other:"\u8D85\u904E {{count}} \u5E74"},almostXYears:{one:"\u5C07\u8FD1 1 \u5E74",other:"\u5C07\u8FD1 {{count}} \u5E74"}},S=function G(J,X,Y){var Z,B=D[J];if(typeof B==="string")Z=B;else if(X===1)Z=B.one;else Z=B.other.replace("{{count}}",String(X));if(Y!==null&&Y!==void 0&&Y.addSuffix)if(Y.comparison&&Y.comparison>0)return Z+"\u5167";else return Z+"\u524D";return Z};function $(G){return function(){var J=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},X=J.width?String(J.width):G.defaultWidth,Y=G.formats[X]||G.formats[G.defaultWidth];return Y}}var M={full:"y'\u5E74'M'\u6708'd'\u65E5' EEEE",long:"y'\u5E74'M'\u6708'd'\u65E5'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},R={full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},L={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},V={date:$({formats:M,defaultWidth:"full"}),time:$({formats:R,defaultWidth:"full"}),dateTime:$({formats:L,defaultWidth:"full"})},j={lastWeek:"'\u4E0A\u500B'eeee p",yesterday:"'\u6628\u5929' p",today:"'\u4ECA\u5929' p",tomorrow:"'\u660E\u5929' p",nextWeek:"'\u4E0B\u500B'eeee p",other:"P"},w=function G(J,X,Y,Z){return j[J]};function I(G){return function(J,X){var Y=X!==null&&X!==void 0&&X.context?String(X.context):"standalone",Z;if(Y==="formatting"&&G.formattingValues){var B=G.defaultFormattingWidth||G.defaultWidth,H=X!==null&&X!==void 0&&X.width?String(X.width):B;Z=G.formattingValues[H]||G.formattingValues[B]}else{var T=G.defaultWidth,q=X!==null&&X!==void 0&&X.width?String(X.width):G.defaultWidth;Z=G.values[q]||G.values[T]}var U=G.argumentCallback?G.argumentCallback(J):J;return Z[U]}}var _={narrow:["\u524D","\u516C\u5143"],abbreviated:["\u524D","\u516C\u5143"],wide:["\u516C\u5143\u524D","\u516C\u5143"]},f={narrow:["1","2","3","4"],abbreviated:["\u7B2C\u4E00\u5B63","\u7B2C\u4E8C\u5B63","\u7B2C\u4E09\u5B63","\u7B2C\u56DB\u5B63"],wide:["\u7B2C\u4E00\u5B63\u5EA6","\u7B2C\u4E8C\u5B63\u5EA6","\u7B2C\u4E09\u5B63\u5EA6","\u7B2C\u56DB\u5B63\u5EA6"]},v={narrow:["\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D","\u4E03","\u516B","\u4E5D","\u5341","\u5341\u4E00","\u5341\u4E8C"],abbreviated:["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708"],wide:["\u4E00\u6708","\u4E8C\u6708","\u4E09\u6708","\u56DB\u6708","\u4E94\u6708","\u516D\u6708","\u4E03\u6708","\u516B\u6708","\u4E5D\u6708","\u5341\u6708","\u5341\u4E00\u6708","\u5341\u4E8C\u6708"]},P={narrow:["\u65E5","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D"],short:["\u65E5","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D"],abbreviated:["\u9031\u65E5","\u9031\u4E00","\u9031\u4E8C","\u9031\u4E09","\u9031\u56DB","\u9031\u4E94","\u9031\u516D"],wide:["\u661F\u671F\u65E5","\u661F\u671F\u4E00","\u661F\u671F\u4E8C","\u661F\u671F\u4E09","\u661F\u671F\u56DB","\u661F\u671F\u4E94","\u661F\u671F\u516D"]},F={narrow:{am:"\u4E0A",pm:"\u4E0B",midnight:"\u5348\u591C",noon:"\u664C",morning:"\u65E9",afternoon:"\u5348",evening:"\u665A",night:"\u591C"},abbreviated:{am:"\u4E0A\u5348",pm:"\u4E0B\u5348",midnight:"\u5348\u591C",noon:"\u4E2D\u5348",morning:"\u4E0A\u5348",afternoon:"\u4E0B\u5348",evening:"\u665A\u4E0A",night:"\u591C\u665A"},wide:{am:"\u4E0A\u5348",pm:"\u4E0B\u5348",midnight:"\u5348\u591C",noon:"\u4E2D\u5348",morning:"\u4E0A\u5348",afternoon:"\u4E0B\u5348",evening:"\u665A\u4E0A",night:"\u591C\u665A"}},k={narrow:{am:"\u4E0A",pm:"\u4E0B",midnight:"\u5348\u591C",noon:"\u664C",morning:"\u65E9",afternoon:"\u5348",evening:"\u665A",night:"\u591C"},abbreviated:{am:"\u4E0A\u5348",pm:"\u4E0B\u5348",midnight:"\u5348\u591C",noon:"\u4E2D\u5348",morning:"\u4E0A\u5348",afternoon:"\u4E0B\u5348",evening:"\u665A\u4E0A",night:"\u591C\u665A"},wide:{am:"\u4E0A\u5348",pm:"\u4E0B\u5348",midnight:"\u5348\u591C",noon:"\u4E2D\u5348",morning:"\u4E0A\u5348",afternoon:"\u4E0B\u5348",evening:"\u665A\u4E0A",night:"\u591C\u665A"}},h=function G(J,X){var Y=Number(J);switch(X===null||X===void 0?void 0:X.unit){case"date":return Y+"\u65E5";case"hour":return Y+"\u6642";case"minute":return Y+"\u5206";case"second":return Y+"\u79D2";default:return"\u7B2C "+Y}},b={ordinalNumber:h,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function G(J){return J-1}}),month:I({values:v,defaultWidth:"wide"}),day:I({values:P,defaultWidth:"wide"}),dayPeriod:I({values:F,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(G){return function(J){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Y=X.width,Z=Y&&G.matchPatterns[Y]||G.matchPatterns[G.defaultMatchWidth],B=J.match(Z);if(!B)return null;var H=B[0],T=Y&&G.parsePatterns[Y]||G.parsePatterns[G.defaultParseWidth],q=Array.isArray(T)?c(T,function(E){return E.test(H)}):m(T,function(E){return E.test(H)}),U;U=G.valueCallback?G.valueCallback(q):q,U=X.valueCallback?X.valueCallback(U):U;var JG=J.slice(H.length);return{value:U,rest:JG}}}function m(G,J){for(var X in G)if(Object.prototype.hasOwnProperty.call(G,X)&&J(G[X]))return X;return}function c(G,J){for(var X=0;X<G.length;X++)if(J(G[X]))return X;return}function y(G){return function(J){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Y=J.match(G.matchPattern);if(!Y)return null;var Z=Y[0],B=J.match(G.parsePattern);if(!B)return null;var H=G.valueCallback?G.valueCallback(B[0]):B[0];H=X.valueCallback?X.valueCallback(H):H;var T=J.slice(Z.length);return{value:H,rest:T}}}var d=/^(第\s*)?\d+(日|時|分|秒)?/i,g=/\d+/i,p={narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},u={any:[/^(前)/i,/^(公元)/i]},l={narrow:/^[1234]/i,abbreviated:/^第[一二三四]季/i,wide:/^第[一二三四]季度/i},i={any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},n={narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},s={narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},o={narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^週[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},r={any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},a={any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨)/i},e={any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},t={ordinalNumber:y({matchPattern:d,parsePattern:g,valueCallback:function G(J){return parseInt(J,10)}}),era:O({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function G(J){return J+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},GG={code:"zh-HK",formatDistance:S,formatLong:V,formatRelative:w,localize:b,match:t,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},(A=window.dateFns)===null||A===void 0?void 0:A.locale),{},{zhHK:GG})})})();

//# debugId=1F1B710BD58C8EC164756E2164756E21
