"use strict";
exports.et = void 0;
var _index = require("./et/_lib/formatDistance.cjs");
var _index2 = require("./et/_lib/formatLong.cjs");
var _index3 = require("./et/_lib/formatRelative.cjs");
var _index4 = require("./et/_lib/localize.cjs");
var _index5 = require("./et/_lib/match.cjs");

/**
 * @category Locales
 * @summary Estonian locale.
 * @language Estonian
 * @iso-639-2 est
 * <AUTHOR> [@HansenPriit](https://github.com/priithansen)
 */
const et = (exports.et = {
  code: "et",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
