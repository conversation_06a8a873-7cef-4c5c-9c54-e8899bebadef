<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>JSDoc: Module: quat</title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">
</head>

<body>

<div id="main">

    <h1 class="page-title">Module: quat</h1>

    




<section>

<header>
    
        
            
        
    
</header>

<article>
    <div class="container-overview">
    
        
            <div class="description">Quaternion</div>
        

        
            















<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line6">line 6</a>
    </li></ul></dd>
    

    

    

    
</dl>


















        
    
    </div>

    

    

    

    

    

    

    
        <h3 class="subsection-title">Members</h3>

        
            
<h4 class="name" id=".equals"><span class="type-signature">(static, constant) </span>equals<span class="type-signature"></span></h4>




<div class="description">
    Returns whether or not the quaternions have approximately the same elements in the same position.
</div>







<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line535">line 535</a>
    </li></ul></dd>
    

    

    

    
</dl>






        
            
<h4 class="name" id=".exactEquals"><span class="type-signature">(static, constant) </span>exactEquals<span class="type-signature"></span></h4>




<div class="description">
    Returns whether or not the quaternions have exactly the same elements in the same position (when compared with ===)
</div>







<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line526">line 526</a>
    </li></ul></dd>
    

    

    

    
</dl>






        
            
<h4 class="name" id=".length"><span class="type-signature">(static, constant) </span>length<span class="type-signature"></span></h4>




<div class="description">
    Calculates the length of a quat
</div>







<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line486">line 486</a>
    </li></ul></dd>
    

    

    

    
</dl>






        
            
<h4 class="name" id=".rotationTo"><span class="type-signature">(static, constant) </span>rotationTo<span class="type-signature"></span></h4>




<div class="description">
    Sets a quaternion to represent the shortest rotation from one
vector to another.

Both vectors are assumed to be unit length.
</div>







<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line548">line 548</a>
    </li></ul></dd>
    

    

    

    
</dl>






        
            
<h4 class="name" id=".setAxes"><span class="type-signature">(static, constant) </span>setAxes<span class="type-signature"></span></h4>




<div class="description">
    Sets the specified quaternion with values corresponding to the given
axes. Each axis is a vec3 and is expected to be unit length and
perpendicular to all other specified axes.
</div>







<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line613">line 613</a>
    </li></ul></dd>
    

    

    

    
</dl>






        
            
<h4 class="name" id=".sqlerp"><span class="type-signature">(static, constant) </span>sqlerp<span class="type-signature"></span></h4>




<div class="description">
    Performs a spherical linear interpolation with two control points
</div>







<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line590">line 590</a>
    </li></ul></dd>
    

    

    

    
</dl>






        
    

    
        <h3 class="subsection-title">Methods</h3>

        
            

    

    
    <h4 class="name" id=".add"><span class="type-signature">(static) </span>add<span class="signature">(out, a, b)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Adds two quat's
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the receiving quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line439">line 439</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".calculateW"><span class="type-signature">(static) </span>calculateW<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Calculates the W component of a quat from the X, Y, and Z components.
Assumes that quaternion is 1 unit in length.
Any existing W component will be ignored.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the receiving quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">quat to calculate W component of</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line180">line 180</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".clone"><span class="type-signature">(static) </span>clone<span class="signature">(a)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Creates a new quat initialized with values from an existing quaternion
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">quaternion to clone</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line393">line 393</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    a new quaternion
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".conjugate"><span class="type-signature">(static) </span>conjugate<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Calculates the conjugate of a quat
If the quaternion is normalized, this function is faster than quat.inverse and produces the same result.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the receiving quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">quat to calculate conjugate of</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line291">line 291</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".copy"><span class="type-signature">(static) </span>copy<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Copy the values from one quat to another
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the receiving quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the source quaternion</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line415">line 415</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".create"><span class="type-signature">(static) </span>create<span class="signature">()</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Creates a new identity quat
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line16">line 16</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    a new quaternion
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".dot"><span class="type-signature">(static) </span>dot<span class="signature">(a, b)</span><span class="type-signature"> &rarr; {Number}</span></h4>
    

    



<div class="description">
    Calculates the dot product of two quat's
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line466">line 466</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    dot product of a and b
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromEuler"><span class="type-signature">(static) </span>fromEuler<span class="signature">(out, Angle, Angle, Angle)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Creates a quaternion from the given euler angle x, y, z.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the receiving quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>Angle</code></td>
            

            <td class="type">
            
                
<span class="param-type">x</span>


            
            </td>

            

            

            <td class="description last">to rotate around X axis in degrees.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>Angle</code></td>
            

            <td class="type">
            
                
<span class="param-type">y</span>


            
            </td>

            

            

            <td class="description last">to rotate around Y axis in degrees.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>Angle</code></td>
            

            <td class="type">
            
                
<span class="param-type">z</span>


            
            </td>

            

            

            <td class="description last">to rotate around Z axis in degrees.</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line355">line 355</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromMat3"><span class="type-signature">(static) </span>fromMat3<span class="signature">(out, m)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Creates a quaternion from the given 3x3 rotation matrix.

NOTE: The resultant quaternion is not normalized, so you should be sure
to renormalize the quaternion yourself where necessary.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the receiving quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat3</span>


            
            </td>

            

            

            <td class="description last">rotation matrix</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line310">line 310</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromValues"><span class="type-signature">(static) </span>fromValues<span class="signature">(x, y, z, w)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Creates a new quat initialized with the given values
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>x</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">X component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>y</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Y component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>z</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Z component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>w</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">W component</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line405">line 405</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    a new quaternion
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".getAxisAngle"><span class="type-signature">(static) </span>getAxisAngle<span class="signature">(out_axis, q)</span><span class="type-signature"> &rarr; {Number}</span></h4>
    

    



<div class="description">
    Gets the rotation axis and angle for a given
 quaternion. If a quaternion is created with
 setAxisAngle, this method will return the same
 values as providied in the original parameter list
 OR functionally equivalent values.
Example: The quaternion formed by axis [0, 0, 1] and
 angle -90 is the same as the quaternion formed by
 [0, 0, 1] and 270. This method favors the latter.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out_axis</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">Vector receiving the axis of rotation</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>q</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">Quaternion to be decomposed</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line73">line 73</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    Angle, in radians, of the rotation
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".identity"><span class="type-signature">(static) </span>identity<span class="signature">(out)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Set a quat to the identity quaternion
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the receiving quaternion</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line33">line 33</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".invert"><span class="type-signature">(static) </span>invert<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Calculates the inverse of a quat
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the receiving quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">quat to calculate inverse of</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line269">line 269</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".len"><span class="type-signature">(static) </span>len<span class="signature">()</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    Alias for quat.length
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line492">line 492</a>
    </li></ul></dd>
    

    

    

    
</dl>


















        
            

    

    
    <h4 class="name" id=".lerp"><span class="type-signature">(static) </span>lerp<span class="signature">(out, a, b, t)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Performs a linear interpolation between two quat's
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the receiving quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>t</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">interpolation amount, in the range [0-1], between the two inputs</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line478">line 478</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".mul"><span class="type-signature">(static) </span>mul<span class="signature">()</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    Alias for quat.multiply
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line445">line 445</a>
    </li></ul></dd>
    

    

    

    
</dl>


















        
            

    

    
    <h4 class="name" id=".multiply"><span class="type-signature">(static) </span>multiply<span class="signature">(out, a, b)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Multiplies two quat's
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the receiving quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line97">line 97</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".normalize"><span class="type-signature">(static) </span>normalize<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Normalize a quat
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the receiving quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">quaternion to normalize</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line517">line 517</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".random"><span class="type-signature">(static) </span>random<span class="signature">(out)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Generates a random quaternion
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the receiving quaternion</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line245">line 245</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".rotateX"><span class="type-signature">(static) </span>rotateX<span class="signature">(out, a, rad)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Rotates a quaternion by the given angle about the X axis
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">quat receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">quat to rotate</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rad</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last">angle (in radians) to rotate</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line116">line 116</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".rotateY"><span class="type-signature">(static) </span>rotateY<span class="signature">(out, a, rad)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Rotates a quaternion by the given angle about the Y axis
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">quat receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">quat to rotate</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rad</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last">angle (in radians) to rotate</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line137">line 137</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".rotateZ"><span class="type-signature">(static) </span>rotateZ<span class="signature">(out, a, rad)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Rotates a quaternion by the given angle about the Z axis
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">quat receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">quat to rotate</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rad</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last">angle (in radians) to rotate</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line158">line 158</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".scale"><span class="type-signature">(static) </span>scale<span class="signature">(out, a, b)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Scales a quat by a scalar number
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the receiving vector</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the vector to scale</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">amount to scale the vector by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line456">line 456</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".set"><span class="type-signature">(static) </span>set<span class="signature">(out, x, y, z, w)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Set the components of a quat to the given values
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the receiving quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>x</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">X component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>y</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Y component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>z</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Z component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>w</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">W component</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line428">line 428</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".setAxisAngle"><span class="type-signature">(static) </span>setAxisAngle<span class="signature">(out, axis, rad)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Sets a quat from the given angle and rotation axis,
then returns it.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the receiving quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>axis</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">the axis around which to rotate</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rad</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">the angle in radians</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line50">line 50</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".slerp"><span class="type-signature">(static) </span>slerp<span class="signature">(out, a, b, t)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Performs a spherical linear interpolation between two quat
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the receiving quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>t</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">interpolation amount, in the range [0-1], between the two inputs</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line199">line 199</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".sqrLen"><span class="type-signature">(static) </span>sqrLen<span class="signature">()</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    Alias for quat.squaredLength
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line507">line 507</a>
    </li></ul></dd>
    

    

    

    
</dl>


















        
            

    

    
    <h4 class="name" id=".squaredLength"><span class="type-signature">(static) </span>squaredLength<span class="signature">(a)</span><span class="type-signature"> &rarr; {Number}</span></h4>
    

    



<div class="description">
    Calculates the squared length of a quat
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">vector to calculate squared length of</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line501">line 501</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    squared length of a
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".str"><span class="type-signature">(static) </span>str<span class="signature">(a)</span><span class="type-signature"> &rarr; {String}</span></h4>
    

    



<div class="description">
    Returns a string representation of a quatenion
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">vector to represent as a string</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat.js.html">quat.js</a>, <a href="quat.js.html#line382">line 382</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    string representation of the vector
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>


    </dd>
</dl>

    





        
    

    

    
</article>

</section>




</div>

<nav>
    <h2><a href="index.html">Home</a></h2><h3>Modules</h3><ul><li><a href="module-glMatrix.html">glMatrix</a></li><li><a href="module-mat2.html">mat2</a></li><li><a href="module-mat2d.html">mat2d</a></li><li><a href="module-mat3.html">mat3</a></li><li><a href="module-mat4.html">mat4</a></li><li><a href="module-quat.html">quat</a></li><li><a href="module-quat2.html">quat2</a></li><li><a href="module-vec2.html">vec2</a></li><li><a href="module-vec3.html">vec3</a></li><li><a href="module-vec4.html">vec4</a></li></ul>
</nav>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.5.5</a> on Fri Jul 13 2018 11:51:34 GMT+0200 (W. Europe Daylight Time)
</footer>

<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>
</body>
</html>