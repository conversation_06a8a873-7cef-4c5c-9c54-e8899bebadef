// ColorCodes explained: http://www.termsys.demon.co.uk/vtansi.htm
'use strict';

var colorNums = {
      white         :  37
    , black         :  30
    , blue          :  34
    , cyan          :  36
    , green         :  32
    , magenta       :  35
    , red           :  31
    , yellow        :  33
    , brightBlack   :  90
    , brightRed     :  91
    , brightGreen   :  92
    , brightYellow  :  93
    , brightBlue    :  94
    , brightMagenta :  95
    , brightCyan    :  96
    , brightWhite   :  97
    }
  , backgroundColorNums = {
      bgBlack         :  40
    , bgRed           :  41
    , bgGreen         :  42
    , bgYellow        :  43
    , bgBlue          :  44
    , bgMagenta       :  45
    , bgCyan          :  46
    , bgWhite         :  47
    , bgBrightBlack   :  100
    , bgBrightRed     :  101
    , bgBrightGreen   :  102
    , bgBrightYellow  :  103
    , bgBrightBlue    :  104
    , bgBrightMagenta :  105
    , bgBrightCyan    :  106
    , bgBrightWhite   :  107
    } 
  , open   =  {}
  , close  =  {}
  , colors =  {}
  ;

Object.keys(colorNums).forEach(function (k) {
  var o =  open[k]  =  '\u001b[' + colorNums[k] + 'm';
  var c =  close[k] =  '\u001b[39m';

  colors[k] = function (s) { 
    return o + s + c;
  };
});

Object.keys(backgroundColorNums).forEach(function (k) {
  var o =  open[k]  =  '\u001b[' + backgroundColorNums[k] + 'm';
  var c =  close[k] =  '\u001b[49m';

  colors[k] = function (s) { 
    return o + s + c;
  };
});

module.exports =  colors;
colors.open    =  open;
colors.close   =  close;
