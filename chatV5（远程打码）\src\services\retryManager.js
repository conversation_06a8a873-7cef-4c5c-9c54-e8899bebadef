const { log, logToFile } = require('../utils/simple-logger');

/**
 * 统一重试管理器
 * 提供多层次、智能化的重试机制
 */
class RetryManager {
  constructor() {
    this.retryStats = new Map(); // 记录重试统计
  }

  /**
   * 执行带重试的操作
   * @param {string} operationName 操作名称
   * @param {Function} operation 要执行的操作
   * @param {Object} options 重试选项
   * @returns {Promise<any>} 操作结果
   */
  async executeWithRetry(operationName, operation, options = {}) {
    const config = {
      maxRetries: options.maxRetries || 5,
      baseDelay: options.baseDelay || 3000,
      maxDelay: options.maxDelay || 30000,
      backoffMultiplier: options.backoffMultiplier || 1.5,
      totalTimeout: options.totalTimeout || 300000, // 5分钟
      retryCondition: options.retryCondition || this.defaultRetryCondition,
      onRetry: options.onRetry || null,
      context: options.context || {}
    };

    const startTime = Date.now();
    let attempt = 0;
    let lastError = null;
    let consecutiveNetworkErrors = 0;
    const maxConsecutiveNetworkErrors = 3;

    // 初始化统计
    if (!this.retryStats.has(operationName)) {
      this.retryStats.set(operationName, {
        totalAttempts: 0,
        totalSuccesses: 0,
        totalFailures: 0,
        averageAttempts: 0
      });
    }

    while (attempt < config.maxRetries) {
      attempt++;
      const attemptStartTime = Date.now();
      
      // 检查总超时
      if (Date.now() - startTime > config.totalTimeout) {
        log(`[重试管理器] ${operationName} 总超时 (${config.totalTimeout/1000}秒)`, 'error');
        break;
      }

      try {
        log(`[重试管理器] ${operationName} 尝试 ${attempt}/${config.maxRetries}`, 'info');
        
        const result = await operation();
        
        // 成功，更新统计
        const stats = this.retryStats.get(operationName);
        stats.totalAttempts += attempt;
        stats.totalSuccesses++;
        stats.averageAttempts = stats.totalAttempts / (stats.totalSuccesses + stats.totalFailures);
        
        const elapsedTime = Date.now() - startTime;
        log(`[重试管理器] ${operationName} 成功 (尝试${attempt}次, 耗时${Math.round(elapsedTime/1000)}秒)`, 'success');
        
        return result;
        
      } catch (error) {
        lastError = error;
        const attemptDuration = Date.now() - attemptStartTime;
        
        log(`[重试管理器] ${operationName} 失败 (尝试${attempt}/${config.maxRetries}, 耗时${Math.round(attemptDuration/1000)}秒): ${error.message}`, 'warning');
        
        // 检查是否应该重试
        if (!config.retryCondition(error, attempt, config)) {
          log(`[重试管理器] ${operationName} 不满足重试条件，停止重试`, 'warning');
          break;
        }
        
        // 网络错误特殊处理
        const isNetworkError = this.isNetworkError(error);
        if (isNetworkError) {
          consecutiveNetworkErrors++;
          if (consecutiveNetworkErrors >= maxConsecutiveNetworkErrors) {
            log(`[重试管理器] ${operationName} 连续网络错误过多 (${consecutiveNetworkErrors}次)，停止重试`, 'error');
            break;
          }
        } else {
          consecutiveNetworkErrors = 0;
        }
        
        // 计算延迟时间
        if (attempt < config.maxRetries) {
          const delay = this.calculateDelay(attempt, config, error, isNetworkError);
          const remainingTime = config.totalTimeout - (Date.now() - startTime);
          
          if (delay >= remainingTime) {
            log(`[重试管理器] ${operationName} 剩余时间不足，停止重试`, 'warning');
            break;
          }
          
          log(`[重试管理器] ${operationName} ${delay/1000}秒后重试...`, 'info');
          
          // 调用重试回调
          if (config.onRetry) {
            try {
              await config.onRetry(error, attempt, delay, config.context);
            } catch (callbackError) {
              log(`[重试管理器] 重试回调失败: ${callbackError.message}`, 'warning');
            }
          }
          
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // 所有重试都失败，更新统计
    const stats = this.retryStats.get(operationName);
    stats.totalAttempts += attempt;
    stats.totalFailures++;
    stats.averageAttempts = stats.totalAttempts / (stats.totalSuccesses + stats.totalFailures);

    const totalDuration = Date.now() - startTime;
    const errorMsg = `${operationName} 失败，已尝试${attempt}次，总耗时${Math.round(totalDuration/1000)}秒: ${lastError?.message || '未知错误'}`;
    
    log(`[重试管理器] ${errorMsg}`, 'error');
    logToFile(`重试管理器 - ${operationName}失败`, {
      attempts: attempt,
      totalDuration: totalDuration,
      consecutiveNetworkErrors: consecutiveNetworkErrors,
      lastError: lastError?.message,
      context: config.context
    });

    throw new Error(errorMsg);
  }

  /**
   * 默认重试条件
   * @param {Error} error 错误对象
   * @param {number} attempt 当前尝试次数
   * @param {Object} config 配置对象
   * @returns {boolean} 是否应该重试
   */
  defaultRetryCondition(error, attempt, config) {
    // 不重试的错误类型
    const nonRetryableErrors = [
      'authentication',
      'authorization',
      'invalid_key',
      'invalid_token',
      'permission_denied'
    ];

    // 流式响应超时不需要重试，直接视为成功
    const streamTimeoutErrors = [
      '流式响应等待超时',
      'stream timeout',
      'streaming timeout'
    ];

    const errorMessage = error.message.toLowerCase();

    // 检查是否是流式响应超时
    for (const streamError of streamTimeoutErrors) {
      if (errorMessage.includes(streamError.toLowerCase())) {
        return false; // 不重试，视为正常完成
      }
    }

    // 检查其他不可重试的错误
    for (const nonRetryable of nonRetryableErrors) {
      if (errorMessage.includes(nonRetryable)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 判断是否为网络错误
   * @param {Error} error 错误对象
   * @returns {boolean} 是否为网络错误
   */
  isNetworkError(error) {
    const networkErrorCodes = ['ECONNREFUSED', 'ENOTFOUND', 'ETIMEDOUT', 'ECONNRESET'];
    const networkErrorMessages = ['timeout', 'network', 'connection', 'dns', 'socket'];
    
    if (networkErrorCodes.includes(error.code)) {
      return true;
    }
    
    const errorMessage = error.message.toLowerCase();
    return networkErrorMessages.some(msg => errorMessage.includes(msg));
  }

  /**
   * 计算延迟时间
   * @param {number} attempt 当前尝试次数
   * @param {Object} config 配置对象
   * @param {Error} error 错误对象
   * @param {boolean} isNetworkError 是否为网络错误
   * @returns {number} 延迟时间(毫秒)
   */
  calculateDelay(attempt, config, error, isNetworkError) {
    let delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1);
    
    // 根据错误类型调整延迟
    if (isNetworkError) {
      delay *= 2; // 网络错误延迟加倍
    } else if (error.message.includes('timeout')) {
      delay *= 1.5; // 超时错误延迟增加50%
    } else if (error.message.includes('rate limit')) {
      delay *= 3; // 限流错误延迟增加200%
    }
    
    // 添加随机抖动，避免雷群效应
    const jitter = Math.random() * 0.3 + 0.85; // 85%-115%的随机抖动
    delay *= jitter;
    
    return Math.min(delay, config.maxDelay);
  }

  /**
   * 获取重试统计信息
   * @param {string} operationName 操作名称
   * @returns {Object} 统计信息
   */
  getStats(operationName = null) {
    if (operationName) {
      return this.retryStats.get(operationName) || null;
    }
    
    const allStats = {};
    for (const [name, stats] of this.retryStats.entries()) {
      allStats[name] = { ...stats };
    }
    return allStats;
  }

  /**
   * 重置统计信息
   * @param {string} operationName 操作名称，不提供则重置所有
   */
  resetStats(operationName = null) {
    if (operationName) {
      this.retryStats.delete(operationName);
    } else {
      this.retryStats.clear();
    }
  }

  /**
   * 打印统计信息
   */
  printStats() {
    log('========== 重试统计信息 ==========', 'info');
    for (const [name, stats] of this.retryStats.entries()) {
      const successRate = stats.totalSuccesses / (stats.totalSuccesses + stats.totalFailures) * 100;
      log(`${name}:`, 'info');
      log(`  成功率: ${successRate.toFixed(1)}% (${stats.totalSuccesses}/${stats.totalSuccesses + stats.totalFailures})`, 'info');
      log(`  平均尝试次数: ${stats.averageAttempts.toFixed(1)}`, 'info');
    }
    log('================================', 'info');
  }
}

// 导出单例
const retryManager = new RetryManager();
module.exports = retryManager;
