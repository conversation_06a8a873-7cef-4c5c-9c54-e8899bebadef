{"name": "http-proxy-agent", "version": "5.0.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "test": "mocha", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-http-proxy-agent.git"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": "<PERSON> <<EMAIL>> (http://n8.io/)", "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-http-proxy-agent/issues"}, "dependencies": {"@tootallnate/once": "2", "agent-base": "6", "debug": "4"}, "devDependencies": {"@types/debug": "4", "@types/node": "^12.19.2", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.2", "proxy": "1", "rimraf": "^3.0.0", "typescript": "^4.4.3"}, "engines": {"node": ">= 6"}}