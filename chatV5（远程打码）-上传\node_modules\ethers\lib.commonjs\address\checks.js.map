{"version": 3, "file": "checks.js", "sourceRoot": "", "sources": ["../../src.ts/address/checks.ts"], "names": [], "mappings": ";;;AAAA,gDAA2D;AAE3D,6CAA0C;AAK1C;;;;;;;;;;;;;GAaG;AACH,SAAgB,aAAa,CAAC,KAAU;IACpC,OAAO,CAAC,KAAK,IAAI,OAAM,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,UAAU,CAAC,CAAC;AAC9D,CAAC;AAFD,sCAEC;AAED;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,SAAgB,SAAS,CAAC,KAAU;IAChC,IAAI;QACA,IAAA,uBAAU,EAAC,KAAK,CAAC,CAAC;QAClB,OAAO,IAAI,CAAC;KACf;IAAC,OAAO,KAAK,EAAE,GAAG;IACnB,OAAO,KAAK,CAAC;AACjB,CAAC;AAND,8BAMC;AAED,KAAK,UAAU,YAAY,CAAC,MAAW,EAAE,OAA+B;IACpE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC;IAC7B,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,KAAK,4CAA4C,EAAE;QAC3E,IAAA,iBAAM,EAAC,OAAM,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;QACjG,IAAA,yBAAc,EAAC,KAAK,EAAE,+DAA+D,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;KAC5G;IACD,OAAO,IAAA,uBAAU,EAAC,MAAM,CAAC,CAAC;AAC9B,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoCG;AACH,SAAgB,cAAc,CAAC,MAAmB,EAAE,QAA8B;IAE9E,IAAI,OAAM,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;QAC7B,IAAI,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAE;YAAE,OAAO,IAAA,uBAAU,EAAC,MAAM,CAAC,CAAC;SAAE;QAErE,IAAA,iBAAM,EAAC,QAAQ,IAAI,IAAI,EAAE,oCAAoC,EACzD,uBAAuB,EAAE,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,CAAC;QAE3D,OAAO,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;KAE7D;SAAM,IAAI,aAAa,CAAC,MAAM,CAAC,EAAE;QAC9B,OAAO,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;KAEpD;SAAM,IAAI,MAAM,IAAI,OAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,UAAU,EAAE;QACrD,OAAO,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACvC;IAED,IAAA,yBAAc,EAAC,KAAK,EAAE,+BAA+B,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC7E,CAAC;AAlBD,wCAkBC"}