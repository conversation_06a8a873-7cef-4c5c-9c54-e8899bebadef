const pLimit = require("p-limit");
const path = require("path");
const fs = require("fs");
const { auth, chat, models, points, rateLimit } = require("./api");
const RateLimiter = require("./api/rate-limit");
// 移除对groq的依赖，因为我们只使用question.txt获取问题
// const { groq, apiManager } = require("./services");
const { log, logToFile, randomDelay, checkLogSize, updateProgress } = require("./utils/simple-logger");
const config = require("../config");
const walletProxyManager = require("./services/walletProxyManager");
const questionReader = require("./services/questionReader");
const walletStateManager = require("./services/walletStateManager");

// 直接使用配置文件中的线程数
const THREADS = config.THREADS || 10;

let isRunning = false;
let isPaused = false;
let currentTokenIndex = 0;
let allTokens = [];
let userInfoCache = {};
let lastShuffleDate = null;
let dailyProgress = {
  completed: 0,
  total: 0,
  startTime: null
};

// 当前正在执行的钱包任务
let currentRunningWallets = new Set();
// 最大并发执行的钱包数量
const MAX_CONCURRENT_WALLETS = config.THREADS || 10;
// 检查可执行钱包的间隔（毫秒）
const CHECK_INTERVAL = 10000; // 10秒

async function initAutomation() {
  try {
    log("正在初始化服务...", "info");
    logToFile("初始化自动化服务");

    // 移除对Groq API的初始化，因为我们只使用question.txt获取问题
    // await groq.initGroqClient();

    // 读取代理列表
    const proxies = readProxiesFromFile();
    log(`已加载 ${proxies.length} 个代理`, "info");

    // 初始化钱包队列
    await initWalletQueue();

    log("已准备好开始自动化", "success");
    return true;
  } catch (error) {
    log(`初始化错误: ${error.message}`, "error");
    logToFile("初始化错误", { error: error.message, stack: error.stack });
    return false;
  }
}

function readProxiesFromFile() {
  try {
    const proxyPath = path.join(process.cwd(), "proxies.txt");
    if (!fs.existsSync(proxyPath)) {
      log("未找到代理文件 proxies.txt", "warning");
      return [];
    }

    const content = fs.readFileSync(proxyPath, "utf8");
    const proxies = content
      .split("\n")
      .map(line => line.trim())
      .filter(line => line.length > 0);

    return proxies;
  } catch (error) {
    log(`读取代理文件失败: ${error.message}`, "error");
    return [];
  }
}

async function initWalletQueue() {
  try {
    // 读取所有会话令牌
    allTokens = auth.readAllSessionTokensFromFile();
    if (allTokens.length === 0) {
      log("未发现令牌。无法启动自动化。", "error");
      return false;
    }

    log(`已加载 ${allTokens.length} 个钱包会话`, "info");

    // 检查是否需要随机打乱钱包队列
    if (config.SHUFFLE_WALLETS_DAILY) {
      checkAndShuffleWallets();
    }

    // 初始化每日进度
    resetDailyProgress();

    // 如果启用了钱包代理绑定，自动为未绑定的钱包分配代理
    if (config.ENABLE_WALLET_PROXY_BINDING) {
      const walletAddresses = [];

      // 获取所有钱包地址
      for (const token of allTokens) {
        try {
          // 检查缓存
          if (userInfoCache[token] && userInfoCache[token].wallet_address) {
            walletAddresses.push(userInfoCache[token].wallet_address);
            // 初始化钱包状态（如果之前没有初始化过）
            walletStateManager.initializeWallet(userInfoCache[token].wallet_address);
          } else {
            auth.setCurrentToken(token);
            const userInfo = await auth.getUserInfo();
            if (userInfo && userInfo.wallet_address) {
              walletAddresses.push(userInfo.wallet_address);
              userInfoCache[token] = userInfo;
              // 初始化钱包状态（如果之前没有初始化过）
              walletStateManager.initializeWallet(userInfo.wallet_address);
            }
          }
        } catch (error) {
          log(`获取钱包地址失败: ${error.message}`, "error");
        }
      }

      // 读取代理列表
      const proxies = readProxiesFromFile();

      // 自动绑定钱包和代理
      if (walletAddresses.length > 0 && proxies.length > 0) {
        walletProxyManager.autoBindWallets(walletAddresses, proxies);
      }
    }

    // 设置每日进度总数
    dailyProgress.total = allTokens.length * config.CHATS_PER_WALLET;

    // 显示钱包状态统计
    displayWalletStats();

    return true;
  } catch (error) {
    log(`初始化钱包队列失败: ${error.message}`, "error");
    logToFile("初始化钱包队列失败", { error: error.message, stack: error.stack });
    return false;
  }
}

// 显示钱包状态统计信息
function displayWalletStats() {
  const stats = walletStateManager.getWalletStats();
  const executableWallets = walletStateManager.getExecutableWallets();
  
  log("========== 钱包状态统计 ==========", "info");
  log(`总钱包数: ${stats.totalWallets}`, "info");
  log(`当前可执行钱包: ${stats.executableNow}`, "info");
  log(`等待执行钱包: ${stats.pendingExecution}`, "info");
  log(`已完成任务总数: ${stats.totalCompletedTasks}`, "info");
  log(`当前正在执行的钱包数: ${currentRunningWallets.size}`, "info");
  
  // 显示正在执行的钱包
  if (currentRunningWallets.size > 0) {
    log("正在执行的钱包:", "info");
    let i = 1;
    for (const walletAddress of currentRunningWallets) {
      log(`  ${i++}. ${walletAddress.substring(0, 8)}...`, "info");
    }
  }
  
  // 显示可执行的钱包
  if (executableWallets.length > 0) {
    log("当前可执行的钱包:", "info");
    for (let i = 0; i < Math.min(executableWallets.length, 5); i++) { // 最多显示5个
      const wallet = executableWallets[i];
      const state = walletStateManager.getWalletState(wallet.address);
      const lastExecTime = state.lastExecutionTime ? new Date(state.lastExecutionTime).toLocaleString() : "从未执行";
      log(`  ${i+1}. ${wallet.address.substring(0, 8)}... (已完成: ${wallet.completedTasks}, 上次执行: ${lastExecTime})`, "info");
    }
    
    if (executableWallets.length > 5) {
      log(`  ... 还有 ${executableWallets.length - 5} 个可执行钱包未显示`, "info");
    }
  }
  
  // 显示下一个将要执行的钱包
  if (stats.nextExecutionIn !== null) {
    const hours = Math.floor(stats.nextExecutionIn / (60 * 60 * 1000));
    const minutes = Math.floor((stats.nextExecutionIn % (60 * 60 * 1000)) / (60 * 1000));
    const seconds = Math.floor((stats.nextExecutionIn % (60 * 1000)) / 1000);
    
    const { nextWallet, waitTime } = walletStateManager.getNextWalletWaitTime();
    if (nextWallet) {
      const nextExecTime = new Date(new Date().getTime() + waitTime).toLocaleString();
      log(`下一个钱包 ${nextWallet.substring(0, 8)}... 将在 ${hours > 0 ? hours + '小时' : ''}${minutes}分${seconds}秒 后执行`, "info");
      log(`预计执行时间: ${nextExecTime}`, "info");
    }
  }
  
  log("====================================", "info");
}

function checkAndShuffleWallets() {
  const today = new Date().toISOString().split('T')[0]; // 获取当前日期 YYYY-MM-DD

  if (!lastShuffleDate || lastShuffleDate !== today) {
    // 新的一天，需要重新打乱钱包队列
    shuffleWalletQueue();
    lastShuffleDate = today;
    resetDailyProgress();
  }
}

function shuffleWalletQueue() {
  if (allTokens.length <= 1) return;

  log("随机打乱钱包队列...", "info");
  logToFile("随机打乱钱包队列");

  // Fisher-Yates 洗牌算法
  for (let i = allTokens.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [allTokens[i], allTokens[j]] = [allTokens[j], allTokens[i]];
  }

  log(`钱包队列已随机打乱，共 ${allTokens.length} 个钱包`, "success");
}

function resetDailyProgress() {
  dailyProgress = {
    completed: 0,
    total: allTokens.length,
    startTime: new Date()
  };
  updateProgressDisplay();
}

function updateProgressDisplay() {
  const percentage = dailyProgress.total > 0
    ? Math.round((dailyProgress.completed / dailyProgress.total) * 100)
    : 0;

  const progressText = `进度: ${dailyProgress.completed}/${dailyProgress.total} (${percentage}%)`;
  updateProgress(dailyProgress.completed, dailyProgress.total);
}

async function runSingleAutomation(token, idx) {
  let consecutiveErrors = 0;
  const MAX_CONSECUTIVE_ERRORS = 3;

  function localLog(message, level = "info") {
    try {
      // 确保消息是字符串类型
      const safeMessage = String(message);
      // 修复token和idx显示格式以避免编码问题
      const tokenPreview = Buffer.from(token.slice(0, 8), 'utf8').toString('utf8');
      log(`[${tokenPreview}] [${idx}] ${safeMessage}`, level);
    } catch (err) {
      // 降级处理：如果出现编码问题，使用简化的日志格式
      log(`[令牌${idx}] ${String(message)}`, level);
    }
  }
  function localLogToFile(message, data = null, verbose = true) {
    try {
      const tokenPreview = Buffer.from(token.slice(0, 8), 'utf8').toString('utf8');
      logToFile(`[${tokenPreview}] [${idx}] ${message}`, data, verbose);
    } catch (err) {
      // 降级处理
      logToFile(`[令牌${idx}] ${message}`, data, verbose);
    }
  }

  try {
    auth.setCurrentToken(token);

    await auth.login(false);

    const limiter = new RateLimiter(token);

    const pts = await points.getUserPoints();
    localLog(`令牌 ${token.slice(0, 8)} => 积分: ${pts.total_points}`, "info");

    const modelList = await models.getModels();
    localLog(`令牌 ${token.slice(0, 8)} => 模型数量: ${modelList.length}`, "info");
    await models.selectDefaultModel();

    chat.createThread();

    localLog(`令牌 ${token.slice(0, 8)} 的自动化已启动`, "info");
    logToFile("令牌的自动化已启动", { tokenPreview: token.slice(0, 8) });

    while (isRunning) {
      checkLogSize();

      // const available = await rateLimit.checkRateLimitAvailability();
      // if (!available) {
      //   const info = await rateLimit.getRateLimit();
      //   if (info.remaining === 0) {
      //     log(`Token ${token.slice(0,8)} => exhausted daily limit => finishing`, "warning");
      //   } else {
      //     log(`Token ${token.slice(0,8)} => partial cooldown => finishing anyway`, "warning");
      //   }
      //   break;
      // } else {
      //   const info = await rateLimit.getRateLimit();
      //   if (info.remaining === 0) {
      //     log(`Token ${token.slice(0,8)} => remain=0 => finishing`, "warning");
      //     break;
      //   }
      // }

      try {
        const newRL = await limiter.getRateLimit();
        if (newRL.remaining === 0) {
          localLog(`令牌 ${token.slice(0, 8)} => 剩余次数=0 => 结束。切换到下一个令牌。`, "warning");
          await auth.switchToNextToken();
          break;
        }
      } catch (rlErr) {
        logToFile(`令牌 ${token.slice(0, 8)} 的速率限制错误`, { error: rlErr.message }, false);
      }

      // 保留selectedApi变量以便后续代码兼容
      const selectedApi = "gemini";
      localLog(`令牌 ${token.slice(0, 8)} => 准备从问题库获取问题`, "info");

      // 生成随机用户消息
      let userMessage;
      try {
        // 从问题库获取随机问题
        userMessage = questionReader.getRandomQuestion();
        localLog(`令牌 ${token.slice(0, 8)} => 从问题库获取问题: "${userMessage.substring(0, 50)}${userMessage.length > 50 ? '...' : ''}"`, "info");
      } catch (msgErr) {
        localLog(`令牌 ${token.slice(0, 8)} => 从问题库获取问题失败: ${msgErr.message}`, "error");

        // 使用备用问题列表
        const fallbackQuestions = [
          "你认为人工智能将如何改变我们的未来生活？",
          "如果可以和历史上任何一位人物共进晚餐，你会选择谁，为什么？",
          "你能分享一个鲜为人知但非常有趣的科学事实吗？",
          "你最喜欢的书籍或电影是什么，它如何影响了你？",
          "你认为人类最伟大的发明是什么？为什么？",
          "如果你有超能力，你会选择什么能力，你会如何使用它？",
          "未来十年，你认为科技会有哪些重大突破？",
          "地球上最神秘的地方是哪里？为什么它令人着迷？",
          "你能解释一个复杂的科学概念，让小学生也能理解吗？",
          "人类与其他动物最大的区别是什么？",
          "如何平衡科技使用和保持真实的人际关系？",
          "你能分享一个改变了你世界观的经历或概念吗？",
          "你认为宇宙中存在其他智慧生命吗？为什么？",
          "如何在日常生活中培养创造力？",
          "你能分享一个鲜为人知的历史事件吗？",
          "如果你可以发明一种新技术，它会是什么样的？",
          "你认为人类最大的潜力和局限性是什么？",
          "什么是你最感兴趣的科学领域，为什么？",
          "如何在信息爆炸的时代保持专注和高效？",
          "你对未来50年的预测是什么？",
          "生活中哪些小习惯对你影响最大？",
          "你认为人类进化的下一步是什么？",
          "如何培养批判性思维？",
          "你认为最被低估的技能是什么？",
          "人类应该移民到其他星球吗？为什么？"
        ];
        userMessage = fallbackQuestions[Math.floor(Math.random() * fallbackQuestions.length)];
        localLog(`令牌 ${token.slice(0, 8)} => 使用备用问题: "${userMessage}"`, "info");
      }

      try {
        // 发送聊天消息前记录详细信息
        localLog(`准备发送消息，消息长度: ${userMessage.length} 字符`, "info");
        localLogToFile("Turnstile验证前状态", {
          walletAddress: auth.getCurrentWalletAddress() || 'N/A',
          messageLength: userMessage.length,
          messagePreview: userMessage.substring(0, 30) + "..."
        });
        
        // 发送聊天消息，增加重试机制
        const MAX_SEND_RETRIES = 3;
        let sendRetries = 0;
        let sendSuccess = false;
        let lastError = null;
        
        while (sendRetries < MAX_SEND_RETRIES && !sendSuccess) {
          try {
            if (sendRetries > 0) {
              localLog(`重试发送消息 (第 ${sendRetries} 次尝试)`, "warning");
              // 在重试前等待一段时间
              await new Promise(r => setTimeout(r, 5000 * sendRetries));
            }
            
            await chat.sendChatMessage(userMessage);
            sendSuccess = true;
            consecutiveErrors = 0;
            localLog(`消息发送成功！`, "success");
            
            // 记录API使用统计
            const stats = apiManager.getStats();
            logToFile(`${apiName} API 使用统计`, {
              api: selectedApi,
              calls: stats[selectedApi].calls,
              errors: stats[selectedApi].errors,
              remaining: stats[selectedApi].remaining
            }, false);
          } catch (sendError) {
            lastError = sendError;
            sendRetries++;
            
            // 记录错误详情
            localLogToFile(
              `发送消息失败 (尝试 ${sendRetries}/${MAX_SEND_RETRIES})`,
              { 
                error: sendError.message,
                response: sendError.response ? {
                  status: sendError.response.status,
                  statusText: sendError.response.statusText,
                  data: typeof sendError.response.data === 'string' ? 
                    sendError.response.data.substring(0, 200) : 
                    JSON.stringify(sendError.response.data).substring(0, 200)
                } : 'No response',
                isTimeout: sendError.code === 'ECONNABORTED',
                isNetworkError: sendError.code === 'ENOTFOUND' || sendError.code === 'ECONNREFUSED'
              },
              true
            );
            
            // 检查是否是认证错误
            if (sendError.response && (sendError.response.status === 401 || sendError.response.status === 403)) {
              localLog(`令牌认证错误 => 终止尝试`, "error");
              break;
            }
          }
        }
        
        // 如果所有重试都失败
        if (!sendSuccess) {
          throw lastError || new Error('所有发送尝试都失败');
        }

      } catch (chatError) {
        consecutiveErrors++;
        logToFile(
          `令牌 ${token.slice(0, 8)} 的聊天错误 (连续错误: ${consecutiveErrors}/${MAX_CONSECUTIVE_ERRORS})`,
          { error: chatError.message },
          false
        );

        if (
          chatError.response &&
          (chatError.response.status === 401 || chatError.response.status === 403)
        ) {
          localLog(`令牌 ${token.slice(0, 8)} => 无效 => 停止`, "warning");
          break;
        }

        if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
          localLog(`令牌 ${token.slice(0, 8)} 的错误过多 => 暂停 1 分钟`, "error");
          await new Promise((r) => setTimeout(r, 60000));
          consecutiveErrors = 0;
        } else {
          await new Promise((r) => setTimeout(r, 5000));
        }
        continue;
      }

      try {
        const updatedPoints = await points.getUserPoints();
        localLog(`令牌 ${token.slice(0, 8)} => 更新后的积分: ${updatedPoints.total_points}`, "info");
      } catch (ptErr) {
        logToFile(`令牌 ${token.slice(0, 8)} 的积分更新错误`, { error: ptErr.message }, false);
      }

      // 使用配置文件中的聊天延迟设置
      const delayRange = config.MAX_CHAT_DELAY - config.MIN_CHAT_DELAY;
      const delay = Math.floor(Math.random() * delayRange) + config.MIN_CHAT_DELAY;
      const delaySeconds = (delay / 1000).toFixed(1);
      localLog(`令牌 ${token.slice(0, 8)} 休眠 ${delaySeconds} 秒...`, "info");
      await new Promise((r) => setTimeout(r, delay));
    }

    localLog(`令牌 ${token.slice(0, 8)} 的自动化已结束`, "info");
  } catch (error) {
    localLog(`令牌 ${token.slice(0, 8)} 的致命错误 => ${error.message}`, "error");
    logToFile("令牌的致命错误", { token: token.slice(0, 8), error: error.message }, false);
  }
}

async function startAutomation() {
  if (isRunning) {
    log("自动化已在运行中", "warning");
    return;
  }
  isRunning = true;
  log("正在启动多令牌并发自动化（无冷却等待）...", "info");
  logToFile("正在启动多令牌并发自动化（无冷却等待）");

  const tokens = auth.readAllSessionTokensFromFile();
  if (!tokens || tokens.length === 0) {
    log("未发现令牌。无法启动自动化。", "error");
    isRunning = false;
    return;
  }

  const limit = pLimit(THREADS);
  tokens.forEach((tk, i) => {
    limit(() => runSingleAutomation(tk, i + 1));
  });

  log(`已排队 ${tokens.length} 个令牌，并发数=${THREADS}`, "info");
}

function pauseAutomation() {
  if (!isRunning) {
    log("自动化未运行", "warning");
    return;
  }
  isRunning = false;
  log("自动化已暂停", "warning");
  logToFile("自动化已暂停");
}

function resumeAutomation() {
  if (isRunning) {
    log("自动化已在运行中", "warning");
    return;
  }
  log("正在恢复自动化...", "info");
  logToFile("恢复自动化");
  startAutomation();
}

function getRunningState() {
  return isRunning;
}

async function manualSwitchAccount() {
  if (allTokens.length <= 1) {
    log("只有一个账户，无法切换", "warning");
    return false;
  }

  switchToNextAccount();
  log(`已手动切换到账户 ${currentTokenIndex + 1}/${allTokens.length}`, "success");

  // 更新状态
  try {
    const currentToken = allTokens[currentTokenIndex];
    auth.setCurrentToken(currentToken);

    const userInfo = await auth.getUserInfo();
    if (userInfo) {
      const walletAddress = userInfo.wallet_address || "未知钱包";
      log(`账户 ${currentTokenIndex + 1}/${allTokens.length} - ${walletAddress.substring(0, 8)}...`, "info");

      // 如果启用了钱包代理绑定，设置当前钱包地址
      if (config.ENABLE_WALLET_PROXY_BINDING && userInfo.wallet_address) {
        auth.setCurrentWalletAddress(userInfo.wallet_address);
      }
    }
  } catch (error) {
    log(`获取用户信息失败: ${error.message}`, "error");
  }

  return true;
}

// 执行单个钱包任务
async function executeWalletTask(walletAddress) {
  try {
    // 如果钱包已经在执行中，跳过
    if (currentRunningWallets.has(walletAddress)) {
      return;
    }
    
    // 标记钱包为执行中
    currentRunningWallets.add(walletAddress);
    
    // 获取钱包对应的令牌
    const token = await getTokenForWallet(walletAddress);
    if (!token) {
      log(`找不到钱包 ${walletAddress} 对应的令牌，跳过执行`, "error");
      currentRunningWallets.delete(walletAddress);
      return;
    }
    
    // 设置当前令牌
    auth.setCurrentToken(token);
    
    // 设置当前钱包地址（用于代理绑定）
    if (config.ENABLE_WALLET_PROXY_BINDING) {
      auth.setCurrentWalletAddress(walletAddress);
      
      // 检查钱包是否已经绑定代理，如果没有，尝试自动绑定
      const binding = walletProxyManager.getWalletBinding(walletAddress);
      if (!binding || !binding.proxyUrl) {
        log(`钱包 ${walletAddress.substring(0, 8)}... 未找到绑定代理，尝试自动绑定`, "warning");
        
        // 读取代理列表
        const proxies = readProxiesFromFile();
        if (proxies.length > 0) {
          // 随机选择一个代理
          const randomIndex = Math.floor(Math.random() * proxies.length);
          const proxyUrl = proxies[randomIndex];
          
          // 绑定代理
          const bindResult = walletProxyManager.bindWalletToProxy(walletAddress, proxyUrl);
          if (bindResult) {
            log(`成功为钱包 ${walletAddress.substring(0, 8)}... 自动绑定代理: ${proxyUrl}`, "success");
          } else {
            log(`自动绑定代理失败，钱包 ${walletAddress.substring(0, 8)}... 将无法执行任务`, "error");
            currentRunningWallets.delete(walletAddress);
            return;
          }
        } else {
          log(`没有可用的代理，钱包 ${walletAddress.substring(0, 8)}... 将无法执行任务`, "error");
          currentRunningWallets.delete(walletAddress);
          return;
        }
      }
    }
    
    log(`开始执行钱包 ${walletAddress.substring(0, 8)}... 的任务`, "info");
    
    // 创建聊天线程
    chat.createThread();
    
    // 设置模型
    const modelName = models.getAvailableModels()[0] || "gemini-1.5-pro";
    chat.setSelectedModel(modelName);
    
    // 获取问题（从question.txt而不是Gemini API）
    const question = questionReader.getRandomQuestion();
    
    // 发送聊天消息
    log(`[${walletAddress.substring(0, 8)}...] 发送消息: ${question.substring(0, 50)}${question.length > 50 ? "..." : ""}`, "info");
    const response = await chat.sendChatMessage(question);
    
    // 记录响应
    log(`[${walletAddress.substring(0, 8)}...] 收到回复: ${response.substring(0, 50)}${response.length > 50 ? "..." : ""}`, "success");
    
    // 更新进度
    dailyProgress.completed++;
    updateProgressDisplay();
    
    // 记录任务完成并设置下次执行时间（24.5-26.5小时后）
    const completedTasks = walletStateManager.recordTaskCompletion(walletAddress);
    const nextExecTime = walletStateManager.getWalletState(walletAddress).nextExecutionTime;
    const nextExecDate = new Date(nextExecTime);
    
    log(`[${walletAddress.substring(0, 8)}...] 任务完成，已完成 ${completedTasks} 个任务，下次执行时间: ${nextExecDate.toLocaleString()}`, "success");
    
    // 从执行中列表移除
    currentRunningWallets.delete(walletAddress);
    
    return true;
  } catch (error) {
    log(`[${walletAddress.substring(0, 8)}...] 执行任务出错: ${error.message}`, "error");
    logToFile(`钱包 ${walletAddress} 执行任务出错`, { error: error.message, stack: error.stack });
    
    // 从执行中列表移除
    currentRunningWallets.delete(walletAddress);
    
    return false;
  }
}

// 主自动化循环
async function runAutomation() {
  if (!isRunning || isPaused) {
    return;
  }

  try {
    // 如果当前执行的钱包数量已达到最大并发数，等待下一次检查
    if (currentRunningWallets.size >= MAX_CONCURRENT_WALLETS) {
      log(`当前已有 ${currentRunningWallets.size} 个钱包正在执行，等待完成...`, "info");
      setTimeout(runAutomation, CHECK_INTERVAL);
      return;
    }
    
    // 获取下一个可执行的钱包
    const nextWallet = walletStateManager.getNextExecutableWallet();
    
    if (nextWallet) {
      // 执行钱包任务
      executeWalletTask(nextWallet);
      
      // 短暂延迟后继续检查其他可执行钱包
      setTimeout(runAutomation, 1000);
    } else {
      // 没有可执行的钱包，计算下一个钱包的等待时间
      const { nextWallet, waitTime } = walletStateManager.getNextWalletWaitTime();
      
      if (nextWallet && waitTime < Number.MAX_SAFE_INTEGER) {
        const minutes = Math.floor(waitTime / (60 * 1000));
        const seconds = Math.floor((waitTime % (60 * 1000)) / 1000);
        
        log(`没有可执行的钱包，下一个钱包 ${nextWallet.substring(0, 8)}... 将在 ${minutes}分${seconds}秒 后执行`, "info");
        
        // 显示钱包状态统计
        displayWalletStats();
        
        // 等待一段时间后再次检查
        const checkDelay = Math.min(waitTime, CHECK_INTERVAL);
        setTimeout(runAutomation, checkDelay);
      } else {
        log("没有找到可执行的钱包，将在 10 秒后重新检查", "warning");
        setTimeout(runAutomation, CHECK_INTERVAL);
      }
    }
  } catch (error) {
    log(`自动化主循环出错: ${error.message}`, "error");
    logToFile("自动化主循环出错", { error: error.message, stack: error.stack });
    
    // 出错后等待一段时间再继续
    setTimeout(runAutomation, CHECK_INTERVAL);
  }
}

function switchToNextAccount() {
  currentTokenIndex = (currentTokenIndex + 1) % allTokens.length;
  log(`切换到账户 ${currentTokenIndex + 1}/${allTokens.length}`, "info");
}

async function manualSwitchAccount() {
  if (allTokens.length <= 1) {
    log("只有一个账户，无法切换", "warning");
    return false;
  }

  switchToNextAccount();
  log(`已手动切换到账户 ${currentTokenIndex + 1}/${allTokens.length}`, "success");

  // 更新状态
  try {
    const currentToken = allTokens[currentTokenIndex];
    auth.setCurrentToken(currentToken);

    const userInfo = await auth.getUserInfo();
    if (userInfo) {
      const walletAddress = userInfo.wallet_address || "未知钱包";
      log(`账户 ${currentTokenIndex + 1}/${allTokens.length} - ${walletAddress.substring(0, 8)}...`, "info");

      // 如果启用了钱包代理绑定，设置当前钱包地址
      if (config.ENABLE_WALLET_PROXY_BINDING && userInfo.wallet_address) {
        auth.setCurrentWalletAddress(userInfo.wallet_address);
      }
    }
  } catch (error) {
    log(`获取用户信息失败: ${error.message}`, "error");
  }

  return true;
}

// 检查钱包执行窗口
async function checkExecutionWindow() {
  try {
    const stats = walletStateManager.getWalletStats();
    const executableWallets = walletStateManager.getExecutableWallets();
    
    log("========== 钱包执行窗口检查 ==========", "info");
    log(`当前可执行钱包: ${stats.executableNow}/${stats.totalWallets}`, "info");
    log(`已完成任务总数: ${stats.totalCompletedTasks}`, "info");
    
    if (executableWallets.length > 0) {
      log("可执行钱包列表:", "info");
      for (let i = 0; i < Math.min(executableWallets.length, 10); i++) { // 最多显示10个
        const wallet = executableWallets[i];
        log(`${i+1}. ${wallet.address.substring(0, 8)}... (已完成 ${wallet.completedTasks} 个任务)`, "info");
      }
      
      if (executableWallets.length > 10) {
        log(`... 还有 ${executableWallets.length - 10} 个可执行钱包未显示`, "info");
      }
    } else {
      log("当前没有可执行的钱包", "info");
      
      const { nextWallet, waitTime } = walletStateManager.getNextWalletWaitTime();
      if (nextWallet && waitTime < Number.MAX_SAFE_INTEGER) {
        const minutes = Math.floor(waitTime / (60 * 1000));
        const seconds = Math.floor((waitTime % (60 * 1000)) / 1000);
        log(`下一个钱包 ${nextWallet.substring(0, 8)}... 将在 ${minutes}分${seconds}秒 后执行`, "info");
      }
    }
    
    // 显示24小时进度统计
    updateProgressDisplay();
    
    log("====================================", "info");
    return true;
  } catch (error) {
    log(`检查执行窗口出错: ${error.message}`, "error");
    return false;
  }
}

// 获取钱包对应的令牌
async function getTokenForWallet(walletAddress) {
  for (const token of allTokens) {
    try {
      // 检查缓存
      if (userInfoCache[token] && userInfoCache[token].wallet_address === walletAddress) {
        return token;
      }
      
      // 如果没有缓存，获取用户信息
      auth.setCurrentToken(token);
      const userInfo = await auth.getUserInfo();
      if (userInfo && userInfo.wallet_address === walletAddress) {
        userInfoCache[token] = userInfo;
        return token;
      }
    } catch (error) {
      log(`获取钱包 ${walletAddress} 的令牌失败: ${error.message}`, "error");
    }
  }
  
  return null;
}

module.exports = {
  initAutomation,
  startAutomation,
  pauseAutomation,
  resumeAutomation,
  getRunningState,
  checkExecutionWindow,
  displayWalletStats
};
