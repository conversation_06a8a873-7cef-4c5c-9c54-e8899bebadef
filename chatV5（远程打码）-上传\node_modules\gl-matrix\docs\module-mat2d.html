<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>JSDoc: Module: mat2d</title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">
</head>

<body>

<div id="main">

    <h1 class="page-title">Module: mat2d</h1>

    




<section>

<header>
    
        
            
        
    
</header>

<article>
    <div class="container-overview">
    
        
            <div class="description">A mat2d contains six elements defined as:
<pre>
[a, c, tx,
 b, d, ty]
</pre>
This is a short form for the 3x3 matrix:
<pre>
[a, c, tx,
 b, d, ty,
 0, 0, 1]
</pre>
The last row is ignored so the array is shorter and operations are faster.</div>
        

        
            















<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line3">line 3</a>
    </li></ul></dd>
    

    

    

    
</dl>


















        
    
    </div>

    

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        
            

    

    
    <h4 class="name" id=".add"><span class="type-signature">(static) </span>add<span class="signature">(out, a, b)</span><span class="type-signature"> &rarr; {mat2d}</span></h4>
    

    



<div class="description">
    Adds two mat2d's
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line344">line 344</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2d</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".clone"><span class="type-signature">(static) </span>clone<span class="signature">(a)</span><span class="type-signature"> &rarr; {mat2d}</span></h4>
    

    



<div class="description">
    Creates a new mat2d initialized with values from an existing matrix
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">matrix to clone</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line46">line 46</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    a new 2x3 matrix
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2d</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".copy"><span class="type-signature">(static) </span>copy<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {mat2d}</span></h4>
    

    



<div class="description">
    Copy the values from one mat2d to another
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the source matrix</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line64">line 64</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2d</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".create"><span class="type-signature">(static) </span>create<span class="signature">()</span><span class="type-signature"> &rarr; {mat2d}</span></h4>
    

    



<div class="description">
    Creates a new identity mat2d
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line27">line 27</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    a new 2x3 matrix
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2d</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".determinant"><span class="type-signature">(static) </span>determinant<span class="signature">(a)</span><span class="type-signature"> &rarr; {Number}</span></h4>
    

    



<div class="description">
    Calculates the determinant of a mat2d
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the source matrix</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line166">line 166</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    determinant of a
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".equals"><span class="type-signature">(static) </span>equals<span class="signature">(a, b)</span><span class="type-signature"> &rarr; {Boolean}</span></h4>
    

    



<div class="description">
    Returns whether or not the matrices have approximately the same elements in the same position.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">The first matrix.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">The second matrix.</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line427">line 427</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    True if the matrices are equal, false otherwise.
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".exactEquals"><span class="type-signature">(static) </span>exactEquals<span class="signature">(a, b)</span><span class="type-signature"> &rarr; {Boolean}</span></h4>
    

    



<div class="description">
    Returns whether or not the matrices have exactly the same elements in the same position (when compared with ===)
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">The first matrix.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">The second matrix.</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line416">line 416</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    True if the matrices are equal, false otherwise.
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".frob"><span class="type-signature">(static) </span>frob<span class="signature">(a)</span><span class="type-signature"> &rarr; {Number}</span></h4>
    

    



<div class="description">
    Returns Frobenius norm of a mat2d
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the matrix to calculate Frobenius norm of</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line332">line 332</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    Frobenius norm
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromRotation"><span class="type-signature">(static) </span>fromRotation<span class="signature">(out, rad)</span><span class="type-signature"> &rarr; {mat2d}</span></h4>
    

    



<div class="description">
    Creates a matrix from a given angle
This is equivalent to (but much faster than):

    mat2d.identity(dest);
    mat2d.rotate(dest, dest, rad);
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">mat2d receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rad</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">the angle to rotate the matrix by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line262">line 262</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2d</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromScaling"><span class="type-signature">(static) </span>fromScaling<span class="signature">(out, v)</span><span class="type-signature"> &rarr; {mat2d}</span></h4>
    

    



<div class="description">
    Creates a matrix from a vector scaling
This is equivalent to (but much faster than):

    mat2d.identity(dest);
    mat2d.scale(dest, dest, vec);
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">mat2d receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>v</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec2</span>


            
            </td>

            

            

            <td class="description last">Scaling vector</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line284">line 284</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2d</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromTranslation"><span class="type-signature">(static) </span>fromTranslation<span class="signature">(out, v)</span><span class="type-signature"> &rarr; {mat2d}</span></h4>
    

    



<div class="description">
    Creates a matrix from a vector translation
This is equivalent to (but much faster than):

    mat2d.identity(dest);
    mat2d.translate(dest, dest, vec);
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">mat2d receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>v</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec2</span>


            
            </td>

            

            

            <td class="description last">Translation vector</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line305">line 305</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2d</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromValues"><span class="type-signature">(static) </span>fromValues<span class="signature">(a, b, c, d, tx, ty)</span><span class="type-signature"> &rarr; {mat2d}</span></h4>
    

    



<div class="description">
    Create a new mat2d with the given values
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component A (index 0)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component B (index 1)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>c</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component C (index 2)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>d</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component D (index 3)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>tx</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component TX (index 4)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>ty</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component TY (index 5)</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line101">line 101</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    A new mat2d
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2d</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".identity"><span class="type-signature">(static) </span>identity<span class="signature">(out)</span><span class="type-signature"> &rarr; {mat2d}</span></h4>
    

    



<div class="description">
    Set a mat2d to the identity matrix
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line80">line 80</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2d</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".invert"><span class="type-signature">(static) </span>invert<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {mat2d}</span></h4>
    

    



<div class="description">
    Inverts a mat2d
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the source matrix</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line141">line 141</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2d</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".mul"><span class="type-signature">(static) </span>mul<span class="signature">()</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    Alias for mat2d.multiply
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line442">line 442</a>
    </li></ul></dd>
    

    

    

    
</dl>


















        
            

    

    
    <h4 class="name" id=".multiply"><span class="type-signature">(static) </span>multiply<span class="signature">(out, a, b)</span><span class="type-signature"> &rarr; {mat2d}</span></h4>
    

    



<div class="description">
    Multiplies two mat2d's
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line178">line 178</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2d</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".multiplyScalar"><span class="type-signature">(static) </span>multiplyScalar<span class="signature">(out, a, b)</span><span class="type-signature"> &rarr; {mat2d}</span></h4>
    

    



<div class="description">
    Multiply each element of the matrix by a scalar.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the matrix to scale</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">amount to scale the matrix's elements by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line380">line 380</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2d</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".multiplyScalarAndAdd"><span class="type-signature">(static) </span>multiplyScalarAndAdd<span class="signature">(out, a, b, scale)</span><span class="type-signature"> &rarr; {mat2d}</span></h4>
    

    



<div class="description">
    Adds two mat2d's after multiplying each element of the second operand by a scalar value.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the receiving vector</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>scale</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">the amount to scale b's elements by before adding</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line399">line 399</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2d</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".rotate"><span class="type-signature">(static) </span>rotate<span class="signature">(out, a, rad)</span><span class="type-signature"> &rarr; {mat2d}</span></h4>
    

    



<div class="description">
    Rotates a mat2d by the given angle
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the matrix to rotate</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rad</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">the angle to rotate the matrix by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line198">line 198</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2d</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".scale"><span class="type-signature">(static) </span>scale<span class="signature">(out, a, v)</span><span class="type-signature"> &rarr; {mat2d}</span></h4>
    

    



<div class="description">
    Scales the mat2d by the dimensions in the given vec2
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the matrix to translate</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>v</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec2</span>


            
            </td>

            

            

            <td class="description last">the vec2 to scale the matrix by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line219">line 219</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2d</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".set"><span class="type-signature">(static) </span>set<span class="signature">(out, a, b, c, d, tx, ty)</span><span class="type-signature"> &rarr; {mat2d}</span></h4>
    

    



<div class="description">
    Set the components of a mat2d to the given values
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component A (index 0)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component B (index 1)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>c</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component C (index 2)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>d</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component D (index 3)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>tx</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component TX (index 4)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>ty</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component TY (index 5)</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line124">line 124</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2d</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".str"><span class="type-signature">(static) </span>str<span class="signature">(a)</span><span class="type-signature"> &rarr; {String}</span></h4>
    

    



<div class="description">
    Returns a string representation of a mat2d
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">matrix to represent as a string</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line321">line 321</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    string representation of the matrix
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".sub"><span class="type-signature">(static) </span>sub<span class="signature">()</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    Alias for mat2d.subtract
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line448">line 448</a>
    </li></ul></dd>
    

    

    

    
</dl>


















        
            

    

    
    <h4 class="name" id=".subtract"><span class="type-signature">(static) </span>subtract<span class="signature">(out, a, b)</span><span class="type-signature"> &rarr; {mat2d}</span></h4>
    

    



<div class="description">
    Subtracts matrix b from matrix a
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line362">line 362</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2d</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".translate"><span class="type-signature">(static) </span>translate<span class="signature">(out, a, v)</span><span class="type-signature"> &rarr; {mat2d}</span></h4>
    

    



<div class="description">
    Translates the mat2d by the dimensions in the given vec2
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2d</span>


            
            </td>

            

            

            <td class="description last">the matrix to translate</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>v</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec2</span>


            
            </td>

            

            

            <td class="description last">the vec2 to translate the matrix by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2d.js.html">mat2d.js</a>, <a href="mat2d.js.html#line239">line 239</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2d</span>


    </dd>
</dl>

    





        
    

    

    
</article>

</section>




</div>

<nav>
    <h2><a href="index.html">Home</a></h2><h3>Modules</h3><ul><li><a href="module-glMatrix.html">glMatrix</a></li><li><a href="module-mat2.html">mat2</a></li><li><a href="module-mat2d.html">mat2d</a></li><li><a href="module-mat3.html">mat3</a></li><li><a href="module-mat4.html">mat4</a></li><li><a href="module-quat.html">quat</a></li><li><a href="module-quat2.html">quat2</a></li><li><a href="module-vec2.html">vec2</a></li><li><a href="module-vec3.html">vec3</a></li><li><a href="module-vec4.html">vec4</a></li></ul>
</nav>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.5.5</a> on Fri Jul 13 2018 11:51:33 GMT+0200 (W. Europe Daylight Time)
</footer>

<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>
</body>
</html>