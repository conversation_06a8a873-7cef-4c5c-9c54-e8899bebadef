#!/bin/bash

# 客户端部署脚本
# 使用方法: bash deploy_client.sh <主服务器IP> <客户端名称>
# 示例: bash deploy_client.sh ************* client-01

SERVER_IP=$1
CLIENT_NAME=$2

if [ -z "$SERVER_IP" ] || [ -z "$CLIENT_NAME" ]; then
    echo "❌ 使用方法: bash deploy_client.sh <主服务器IP> <客户端名称>"
    echo "示例: bash deploy_client.sh ************* client-01"
    exit 1
fi

echo "🚀 开始部署客户端: $CLIENT_NAME"
echo "🖥️  主服务器IP: $SERVER_IP"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root用户运行此脚本"
    exit 1
fi

# 更新系统
echo "📦 更新系统包..."
apt update && apt upgrade -y

# 安装必要软件
echo "🔧 安装必要软件..."
apt install -y curl wget git docker.io docker-compose

# 启动Docker服务
systemctl start docker
systemctl enable docker

# 克隆项目
echo "📥 克隆项目..."
if [ -d "brush-captcha" ]; then
    echo "⚠️  项目目录已存在，正在更新..."
    cd brush-captcha
    git pull
else
    git clone https://github.com/Brush-Bot/brush-captcha.git
    cd brush-captcha
fi

# 进入客户端目录
cd client

# 检测主服务器是否使用SSL
echo "🔍 检测主服务器SSL配置..."
if curl -k -s "https://$SERVER_IP:8080" > /dev/null 2>&1; then
    WSS_URL="wss://$SERVER_IP:8080/ws/worker/"
    echo "✅ 检测到SSL，使用wss连接"
else
    WSS_URL="ws://$SERVER_IP:8080/ws/worker/"
    echo "ℹ️  未检测到SSL，使用ws连接"
fi

# 创建配置文件
echo "📝 创建配置文件..."
cat > config.yaml << EOF
# 并发数设置（自动根据系统资源计算）
concurrency: null

# Camoufox 参数配置
camoufox:
  # 支持的打码类型
  solver_type:
    - HcaptchaCracker
    - AntiTurnstileTaskProxyLess
  # 无头模式
  headless: "true"

worker:
  # 客户端名称
  name: "$CLIENT_NAME"
  # 主服务器地址
  wss_url: "$WSS_URL"
EOF

echo "✅ 配置文件已创建"

# 启动客户端
echo "🚀 启动客户端容器..."
docker compose up -d

# 检查容器状态
sleep 5
if docker ps | grep -q "capsolver-client"; then
    echo "✅ 客户端启动成功！"
    echo ""
    echo "📊 查看日志: docker logs capsolver-client"
    echo "🔄 重启客户端: docker compose restart"
    echo "🛑 停止客户端: docker compose down"
    echo ""
    echo "🌐 管理后台: https://$SERVER_IP:8080"
else
    echo "❌ 客户端启动失败，请检查日志:"
    echo "docker logs capsolver-client"
fi
