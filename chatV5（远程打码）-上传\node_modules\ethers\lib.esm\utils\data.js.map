{"version": 3, "file": "data.js", "sourceRoot": "", "sources": ["../../src.ts/utils/data.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AACH,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAqBrD,SAAS,SAAS,CAAC,KAAgB,EAAE,IAAa,EAAE,IAAc;IAC9D,IAAI,KAAK,YAAY,UAAU,EAAE;QAC7B,IAAI,IAAI,EAAE;YAAE,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;SAAE;QAC3C,OAAO,KAAK,CAAC;KAChB;IAED,IAAI,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,4BAA4B,CAAC,EAAE;QACzE,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACtD,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC9D,MAAM,IAAI,CAAC,CAAC;SACf;QACD,OAAO,MAAM,CAAC;KACjB;IAED,cAAc,CAAC,KAAK,EAAE,yBAAyB,EAAE,IAAI,IAAI,OAAO,EAAE,KAAK,CAAC,CAAC;AAC7E,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,QAAQ,CAAC,KAAgB,EAAE,IAAa;IACpD,OAAO,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACzC,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,YAAY,CAAC,KAAgB,EAAE,IAAa;IACxD,OAAO,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACxC,CAAC;AAGD;;;;;;GAMG;AACH,MAAM,UAAU,WAAW,CAAC,KAAU,EAAE,MAAyB;IAC7D,IAAI,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE;QAChE,OAAO,KAAK,CAAA;KACf;IAED,IAAI,OAAM,CAAC,MAAM,CAAC,KAAK,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,MAAM,EAAE;QAAE,OAAO,KAAK,CAAC;KAAE;IACrF,IAAI,MAAM,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;QAAE,OAAO,KAAK,CAAC;KAAE;IAElE,OAAO,IAAI,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,KAAU;IAClC,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,YAAY,UAAU,CAAC,CAAC,CAAC;AACvE,CAAC;AAED,MAAM,aAAa,GAAW,kBAAkB,CAAC;AAEjD;;GAEG;AACH,MAAM,UAAU,OAAO,CAAC,IAAe;IACnC,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;IAE7B,IAAI,MAAM,GAAG,IAAI,CAAC;IAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACnB,MAAM,IAAI,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;KACtE;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,KAA+B;IAClD,OAAO,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACrE,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,UAAU,CAAC,IAAe;IACtC,IAAI,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;QAAE,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;KAAE;IAC9D,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;AACjC,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,SAAS,CAAC,IAAe,EAAE,KAAc,EAAE,GAAY;IACnE,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC7B,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE;QACnC,MAAM,CAAC,KAAK,EAAE,iCAAiC,EAAE,gBAAgB,EAAE;YAC/D,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG;SACnD,CAAC,CAAC;KACN;IACD,OAAO,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAA,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/F,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,cAAc,CAAC,IAAe;IAC1C,IAAI,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IACvC,OAAO,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;QAAE,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;KAAE;IAC9D,OAAO,IAAI,GAAG,KAAK,CAAC;AACxB,CAAC;AAED,SAAS,OAAO,CAAC,IAAe,EAAE,MAAc,EAAE,IAAa;IAC3D,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC7B,MAAM,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,EAAE,6BAA6B,EAAE,gBAAgB,EAAE;QAC5E,MAAM,EAAE,IAAI,UAAU,CAAC,KAAK,CAAC;QAC7B,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,MAAM,GAAG,CAAC;KACrB,CAAC,CAAC;IAEH,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IACtC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACf,IAAI,IAAI,EAAE;QACN,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;KAC5C;SAAM;QACH,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;KACxB;IAED,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC;AAC3B,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,UAAU,YAAY,CAAC,IAAe,EAAE,MAAc;IACxD,OAAO,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AACvC,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,UAAU,YAAY,CAAC,IAAe,EAAE,MAAc;IACxD,OAAO,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACxC,CAAC"}