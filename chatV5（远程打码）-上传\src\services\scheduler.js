const fs = require('fs');
const path = require('path');
const { log, logToFile } = require('../utils/simple-logger');
const config = require('../../config');

/**
 * 钱包任务调度器
 * 负责管理钱包执行队列和状态
 */
class Scheduler {
  constructor() {
    this.stateFilePath = path.join(process.cwd(), 'wallet-queue-state.json');
    this.walletStates = {};
    this.runningProcesses = new Map(); // 存储运行中的进程
    
    this.loadState();
  }
  
  /**
   * 初始化调度器
   */
  init() {
    log("初始化钱包任务调度器", "info");
    this.loadState();
    return true;
  }
  
  /**
   * 加载钱包状态
   */
  loadState() {
    try {
      if (fs.existsSync(this.stateFilePath)) {
        const data = fs.readFileSync(this.stateFilePath, 'utf8');
        this.walletStates = JSON.parse(data);
        log(`已从 ${this.stateFilePath} 加载钱包状态`, 'info');
        logToFile('加载钱包状态成功', { count: Object.keys(this.walletStates).length });
      } else {
        log(`钱包状态文件不存在，将创建新文件`, 'info');
        this.walletStates = {};
        this.saveState();
      }
    } catch (error) {
      log(`加载钱包状态失败: ${error.message}`, 'error');
      logToFile('加载钱包状态失败', { error: error.message, stack: error.stack });
      this.walletStates = {};
    }
  }
  
  /**
   * 保存钱包状态
   */
  saveState() {
    try {
      fs.writeFileSync(this.stateFilePath, JSON.stringify(this.walletStates, null, 2), 'utf8');
      logToFile('保存钱包状态成功', { count: Object.keys(this.walletStates).length });
    } catch (error) {
      log(`保存钱包状态失败: ${error.message}`, 'error');
      logToFile('保存钱包状态失败', { error: error.message, stack: error.stack });
    }
  }
  
  /**
   * 获取钱包状态
   * @param {string} walletAddress 钱包地址
   * @returns {Object|null} 钱包状态对象
   */
  getWalletState(walletAddress) {
    return this.walletStates[walletAddress] || null;
  }
  
  /**
   * 获取所有钱包状态
   * @returns {Object} 所有钱包状态
   */
  getWalletStates() {
    return this.walletStates;
  }
  
  /**
   * 移除钱包
   * @param {string} walletAddress 钱包地址
   * @returns {boolean} 是否成功移除
   */
  removeWallet(walletAddress) {
    if (this.walletStates[walletAddress]) {
      delete this.walletStates[walletAddress];
      this.saveState();
      return true;
    }
    return false;
  }
  
  /**
   * 更新钱包状态
   * @param {string} walletAddress 钱包地址
   * @param {Object} state 状态对象
   */
  updateWalletState(walletAddress, state) {
    this.walletStates[walletAddress] = {
      ...this.getWalletState(walletAddress),
      ...state,
      lastUpdated: new Date().toISOString()
    };
    this.saveState();
  }
  
  /**
   * 设置钱包下次执行时间
   * @param {string} walletAddress 钱包地址
   * @param {string|null} completionTime 完成时间ISO字符串
   * @returns {Date} 下次执行时间
   */
  setNextExecutionTime(walletAddress, completionTime = null) {
    const now = completionTime ? new Date(completionTime) : new Date();
    
    // 生成24.5到26.5小时的随机延迟（毫秒）
    const minDelay = 24.5 * 60 * 60 * 1000; // 24.5小时
    const maxDelay = 26.5 * 60 * 60 * 1000; // 26.5小时
    // 正确计算随机延迟，确保在minDelay和maxDelay之间
    const randomDelay = Math.floor(Math.random() * (maxDelay - minDelay)) + minDelay;
    
    // 计算下次执行时间
    const nextExecutionTime = new Date(now.getTime() + randomDelay);
    
    // 更新钱包状态
    this.updateWalletState(walletAddress, {
      lastExecutionTime: now.toISOString(),
      nextExecutionTime: nextExecutionTime.toISOString(),
      randomDelay: randomDelay, // 保存随机延迟（毫秒）
      delayHours: (randomDelay / (60 * 60 * 1000)).toFixed(2) // 保存延迟小时数，便于查看
    });
    
    return nextExecutionTime;
  }
  
  /**
   * 初始化新钱包
   * @param {string} walletAddress 钱包地址
   * @param {number|null} initialDelay 初始延迟（毫秒）
   */
  initializeWallet(walletAddress, initialDelay = null) {
    // 如果钱包已存在，不做任何操作
    if (this.walletStates[walletAddress]) {
      return;
    }
    
    const now = new Date();
    let nextExecutionTime;
    
    if (initialDelay === null) {
      // 新增钱包立即执行，不再计算均匀分布

      // 新钱包立即执行，只等待短暂的时间
      const randomMinutes = Math.floor(Math.random() * 5) + 1; // 1-5分钟的随机延迟
      const shortDelay = randomMinutes * 60 * 1000;
      nextExecutionTime = new Date(now.getTime() + shortDelay);
      
      log(`新钱包 ${walletAddress.substring(0, 8)}... 将在 ${randomMinutes} 分钟后执行`, "success");
    } else {
      // 使用指定的初始延迟
      nextExecutionTime = new Date(now.getTime() + initialDelay);
    }
    
    this.updateWalletState(walletAddress, {
      initialized: true,
      completedTasks: 0,
      lastExecutionTime: null,
      nextExecutionTime: nextExecutionTime.toISOString(),
      initialDelay: initialDelay || 'calculated'
    });
  }
  
  /**
   * 获取所有可执行的钱包
   * @returns {Array} 可执行钱包数组
   */
  getExecutableWallets() {
    const now = new Date();
    const executableWallets = [];
    
    for (const [address, state] of Object.entries(this.walletStates)) {
      if (!state.nextExecutionTime) continue;
      
      const nextExecTime = new Date(state.nextExecutionTime);
      
      // 如果钱包可以执行（当前时间已经超过了下次执行时间）
      if (nextExecTime <= now) {
        // 检查该钱包是否已经在运行中
        if (!this.isWalletRunning(address)) {
          executableWallets.push({
            address,
            nextExecutionTime: state.nextExecutionTime,
            completedTasks: state.completedTasks || 0
          });
        }
      }
    }
    
    // 按照下次执行时间排序
    executableWallets.sort((a, b) => {
      return new Date(a.nextExecutionTime) - new Date(b.nextExecutionTime);
    });
    
    return executableWallets;
  }
  
  /**
   * 检查钱包是否正在运行
   * @param {string} walletAddress 钱包地址
   * @returns {boolean} 是否正在运行
   */
  isWalletRunning(walletAddress) {
    return this.runningProcesses.has(walletAddress);
  }
  
  /**
   * 添加运行中的进程
   * @param {string} walletAddress 钱包地址
   * @param {ChildProcess} process 子进程对象
   */
  addRunningProcess(walletAddress, process) {
    this.runningProcesses.set(walletAddress, process);
    log(`钱包 ${walletAddress.substring(0, 8)}... 已加入运行队列`, "info");

    // 安全地获取进程ID
    const pid = process && typeof process.pid !== 'undefined' ? process.pid : 'preparing';
    logToFile('钱包进程已启动', { walletAddress, pid });
  }
  
  /**
   * 移除运行中的进程
   * @param {string} walletAddress 钱包地址
   */
  removeRunningProcess(walletAddress) {
    if (this.runningProcesses.has(walletAddress)) {
      const process = this.runningProcesses.get(walletAddress);
      this.runningProcesses.delete(walletAddress);
      log(`钱包 ${walletAddress.substring(0, 8)}... 已从运行队列移除`, "info");

      // 安全地获取进程ID
      const pid = process && typeof process.pid !== 'undefined' ? process.pid : 'unknown';
      logToFile('钱包进程已结束', { walletAddress, pid });
    }
  }
  
  /**
   * 获取运行中的进程数量
   * @returns {number} 进程数量
   */
  getRunningProcessCount() {
    return this.runningProcesses.size;
  }

  /**
   * 获取所有运行中的进程
   * @returns {Map} 运行中的进程Map
   */
  getAllRunningProcesses() {
    return this.runningProcesses;
  }
  
  /**
   * 获取过去24小时完成任务的钱包数量
   * @returns {number} 过去24小时完成任务的钱包数量
   */
  getWalletsCompletedInLast24Hours() {
    const now = new Date().getTime();
    const oneDayAgo = now - (24 * 60 * 60 * 1000); // 24小时前的时间戳
    let completedCount = 0;
    
    for (const state of Object.values(this.walletStates)) {
      // 检查最后完成时间是否在过去24小时内
      if (state.lastCompletionTime) {
        const lastCompletionTime = new Date(state.lastCompletionTime).getTime();
        if (lastCompletionTime > oneDayAgo) {
          completedCount++;
        }
      }
    }
    
    return completedCount;
  }
  
  /**
   * 获取钱包状态统计
   * @returns {Object} 统计信息
   */
  getWalletStats() {
    const now = new Date().getTime();
    const stats = {
      totalWallets: Object.keys(this.walletStates).length,
      executableNow: 0,
      pendingExecution: 0,
      totalCompletedTasks: 0,
      nextExecutionIn: null,
      completedInLast24Hours: this.getWalletsCompletedInLast24Hours() // 添加过去24小时完成的钱包数量
    };
    
    let minWaitTime = Number.MAX_SAFE_INTEGER;
    
    for (const [address, state] of Object.entries(this.walletStates)) {
      // 统计已完成任务总数
      stats.totalCompletedTasks += (state.completedTasks || 0);
      
      if (!state.nextExecutionTime) continue;
      
      const nextExecTime = new Date(state.nextExecutionTime).getTime();
      const waitTime = nextExecTime - now;
      
      if (waitTime <= 0) {
        // 可立即执行
        if (!this.isWalletRunning(address)) {
          stats.executableNow++;
        }
      } else {
        // 等待执行
        stats.pendingExecution++;
        
        // 更新最短等待时间
        if (waitTime < minWaitTime) {
          minWaitTime = waitTime;
          stats.nextExecutionIn = waitTime;
        }
      }
    }
    
    return stats;
  }
  
  /**
   * 获取下一个将要可执行的钱包及其等待时间
   * @returns {Object} 包含钱包地址和等待时间
   */
  getNextWalletWaitTime() {
    const now = new Date().getTime();
    let nextWallet = null;
    let minWaitTime = Number.MAX_SAFE_INTEGER;
    
    for (const [address, state] of Object.entries(this.walletStates)) {
      if (!state.nextExecutionTime) continue;
      
      const nextExecTime = new Date(state.nextExecutionTime).getTime();
      const waitTime = nextExecTime - now;
      
      if (waitTime > 0 && waitTime < minWaitTime) {
        nextWallet = address;
        minWaitTime = waitTime;
      }
    }
    
    return { nextWallet, waitTime: minWaitTime };
  }
  
  /**
   * 记录任务完成
   * @param {string} walletAddress 钱包地址
   * @returns {number} 已完成任务数
   */
  recordTaskCompletion(walletAddress) {
    const state = this.getWalletState(walletAddress) || {};
    const completedTasks = (state.completedTasks || 0) + 1;
    
    this.updateWalletState(walletAddress, {
      completedTasks: completedTasks,
      lastCompletionTime: new Date().toISOString()
    });
    
    // 设置下次执行时间
    this.setNextExecutionTime(walletAddress);
    
    return completedTasks;
  }
  
  /**
   * 显示钱包状态统计信息
   */
  displayWalletStats() {
    const stats = this.getWalletStats();
    const executableWallets = this.getExecutableWallets();
    
    log("========== 钱包状态统计 ==========", "info");
    log(`总钱包数: ${stats.totalWallets}`, "info");
    log(`当前可执行钱包: ${stats.executableNow}`, "info");
    log(`等待执行钱包: ${stats.pendingExecution}`, "info");
    log(`已完成任务总数: ${stats.totalCompletedTasks}`, "info");
    log(`当前正在执行的钱包数: ${this.runningProcesses.size}`, "info");
    
    // 显示正在执行的钱包
    if (this.runningProcesses.size > 0) {
      log("正在执行的钱包:", "info");
      let i = 1;
      for (const walletAddress of this.runningProcesses.keys()) {
        log(`  ${i++}. ${walletAddress.substring(0, 8)}...`, "info");
      }
    }
    
    // 显示可执行的钱包
    if (executableWallets.length > 0) {
      log("当前可执行的钱包:", "info");
      for (let i = 0; i < Math.min(executableWallets.length, 5); i++) { // 最多显示5个
        const wallet = executableWallets[i];
        const state = this.getWalletState(wallet.address);
        const lastExecTime = state.lastExecutionTime ? new Date(state.lastExecutionTime).toLocaleString() : "从未执行";
        log(`  ${i+1}. ${wallet.address.substring(0, 8)}... (已完成: ${wallet.completedTasks}, 上次执行: ${lastExecTime})`, "info");
      }
      
      if (executableWallets.length > 5) {
        log(`  ... 还有 ${executableWallets.length - 5} 个可执行钱包未显示`, "info");
      }
    }
    
    // 显示下一个将要执行的钱包
    if (stats.nextExecutionIn !== null) {
      const hours = Math.floor(stats.nextExecutionIn / (60 * 60 * 1000));
      const minutes = Math.floor((stats.nextExecutionIn % (60 * 60 * 1000)) / (60 * 1000));
      const seconds = Math.floor((stats.nextExecutionIn % (60 * 1000)) / 1000);
      
      const { nextWallet, waitTime } = this.getNextWalletWaitTime();
      if (nextWallet) {
        const nextExecTime = new Date(new Date().getTime() + waitTime).toLocaleString();
        log(`下一个钱包 ${nextWallet.substring(0, 8)}... 将在 ${hours > 0 ? hours + '小时' : ''}${minutes}分${seconds}秒 后执行`, "info");
        log(`预计执行时间: ${nextExecTime}`, "info");
      }
    }
    
    log("====================================", "info");
  }
}

// 创建单例实例
const scheduler = new Scheduler();
module.exports = scheduler;
