import React, { useState } from "react";
import { Button, Input, Card, message } from "antd";
import md5 from "md5";

export default function Login({ onLogin }) {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");

  const handleSubmit = () => {
    const hash = md5(password);
    console.log("=== 登录调试信息 ===");
    console.log("输入的用户名:", username);
    console.log("输入的密码:", password);
    console.log("密码MD5:", hash);
    console.log("期望的MD5:", "5d68143c4a4e8a6af44d86defd7fd002");
    console.log("用户名匹配:", username === "ddk");
    console.log("密码匹配:", hash === "5d68143c4a4e8a6af44d86defd7fd002");

    if (username === "ddk" && hash === "5d68143c4a4e8a6af44d86defd7fd002") {
      localStorage.setItem("token", hash);
      onLogin();
    } else {
      message.error("用户名或密码错误");
    }
  };

  return (
    <Card title="管理员登录" style={{ maxWidth: 300, margin: "100px auto" }}>
      <Input placeholder="用户名" value={username} onChange={e => setUsername(e.target.value)} style={{ marginBottom: 8 }} />
      <Input.Password placeholder="密码" value={password} onChange={e => setPassword(e.target.value)} style={{ marginBottom: 8 }} />
      <Button type="primary" block onClick={handleSubmit}>登录</Button>
    </Card>
  );
}
