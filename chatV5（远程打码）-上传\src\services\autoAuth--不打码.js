/**
 * 自动认证服务
 * 负责在token无效时自动重新获取token
 */
const fs = require('fs');
const path = require('path');
const { ethers } = require('ethers');
const axios = require('axios');
const config = require('../../config');
const { log, logToFile } = require('../utils/simple-logger');
const { HttpsProxyAgent } = require('https-proxy-agent');
const { HttpProxyAgent } = require('http-proxy-agent');
const walletProxyManager = require('./walletProxyManager');
const yesCaptchaService = require('./yesCaptcha');
const crypto = require('crypto');

// 最大重试次数
const MAX_RETRIES = 5;
const RETRY_DELAY = 3000;

// 私钥文件路径
const privateKeyPath = path.join(process.cwd(), 'priv.txt');
const tokenPath = path.join(process.cwd(), 'session-token.key');

/**
 * 从私钥文件中读取所有私钥
 * @returns {Array<string>} 私钥列表
 */
function readPrivateKeys() {
  try {
    if (!fs.existsSync(privateKeyPath)) {
      log(`私钥文件不存在: ${privateKeyPath}`, "error");
      return [];
    }
    
    const content = fs.readFileSync(privateKeyPath, 'utf8');
    return content.split('\n')
      .map(line => line.trim())
      .filter(line => line && !line.startsWith('#'));
  } catch (error) {
    log(`读取私钥文件失败: ${error.message}`, "error");
    return [];
  }
}

/**
 * 根据钱包地址查找对应的私钥
 * @param {string} walletAddress 钱包地址
 * @returns {string|null} 私钥或null
 */
function findPrivateKeyByAddress(walletAddress) {
  const privateKeys = readPrivateKeys();
  
  for (const privateKey of privateKeys) {
    try {
      const wallet = new ethers.Wallet(privateKey);
      if (wallet.address.toLowerCase() === walletAddress.toLowerCase()) {
        return privateKey;
      }
    } catch (error) {
      // 忽略无效的私钥
      continue;
    }
  }
  
  return null;
}

/**
 * 生成随机字符串作为nonce
 * @returns {string} 随机nonce
 */
function generateNonce() {
  return Buffer.from(crypto.randomBytes(48)).toString("hex");
}

/**
 * 使用私钥签名消息
 * @param {Object} wallet 钱包对象
 * @returns {Promise<Object>} 签名结果
 */
async function signMessage(wallet) {
  try {
    const nonce = generateNonce();
    const timestamp = new Date().toISOString();
    const message = `klokapp.ai wants you to sign in with your Ethereum account:
${wallet.address}


URI: https://klokapp.ai/
Version: 1
Chain ID: 1
Nonce: ${nonce}
Issued At: ${timestamp}`;
    
    return {
      signature: await wallet.signMessage(message),
      message: message,
      nonce: nonce,
      timestamp: timestamp
    };
  } catch (error) {
    log(`签名消息失败: ${error.message}`, "error");
    throw error;
  }
}

/**
 * 认证钱包并获取token
 * @param {string} walletAddress 钱包地址
 * @returns {Promise<string|null>} 成功返回token，失败返回null
 */
async function authenticate(walletAddress) {
  // 查找私钥
  const privateKey = findPrivateKeyByAddress(walletAddress);
  if (!privateKey) {
    log(`未找到钱包 ${walletAddress.substring(0, 8)}... 对应的私钥`, "error");
    return null;
  }
  
  // 创建钱包对象
  const wallet = new ethers.Wallet(privateKey);
  
  let attempt = 0;
  while (attempt < MAX_RETRIES) {
    try {
      log(`钱包 ${walletAddress.substring(0, 8)}... 认证尝试 ${attempt + 1}/${MAX_RETRIES}`, "info");
      
      // 签名消息
      const signResult = await signMessage(wallet);
      log(`钱包 ${walletAddress.substring(0, 8)}... 签名成功`, "success");
      
      // 网站已移除Recaptcha验证，跳过验证码获取步骤
      log(`跳过验证码步骤，网站已移除Recaptcha验证`, "info");
      
      // 构建请求体
      const payload = {
        signedMessage: signResult.signature,
        message: signResult.message,
        referral_code: config.REFERRAL_CODE?.referral_code || ""
        // 移除recaptcha_token字段，因为网站已不需要
      };
      
      // 获取钱包绑定的代理
      const binding = walletProxyManager.getWalletBinding(walletAddress);
      const proxyUrl = binding ? binding.proxyUrl : null;
      
      // 配置请求选项
      const requestOptions = {
        headers: config.DEFAULT_HEADERS,
        timeout: 60000
      };
      
      // 如果有绑定代理，使用代理
      if (proxyUrl) {
        log(`使用钱包 ${walletAddress.substring(0, 8)}... 绑定的代理: ${proxyUrl}`, "info");
        if (config.BASE_URL.startsWith("https")) {
          requestOptions.httpsAgent = new HttpsProxyAgent(proxyUrl);
        } else {
          requestOptions.httpAgent = new HttpProxyAgent(proxyUrl);
        }
      }
      
      // 发送认证请求
      log(`发送认证请求...`, "info");
      const response = await axios.post(`${config.BASE_URL}/verify`, payload, requestOptions);
      
      // 获取session token
      const { session_token } = response.data;
      if (!session_token) {
        throw new Error("响应中未包含session_token");
      }
      
      log(`钱包 ${walletAddress.substring(0, 8)}... 认证成功，获取到token`, "success");
      
      // 更新钱包-token绑定
      walletProxyManager.updateWalletToken(walletAddress, session_token);
      
      // 保存token到文件
      try {
        fs.appendFileSync(tokenPath, `${session_token}\n`);
      } catch (writeError) {
        log(`保存token到文件失败: ${writeError.message}`, "warning");
      }
      
      return session_token;
    } catch (error) {
      attempt++;
      log(`钱包 ${walletAddress.substring(0, 8)}... 认证失败: ${error.message}`, "error");
      
      if (error.response) {
        log(`状态码: ${error.response.status}, 数据:`, error.response.data, "error");
        
        // 特殊处理服务器500错误
        if (error.response.status === 500) {
          // 引入调度器
          const scheduler = require('./scheduler');
          
          // 设置1小时后再尝试
          const delayMs = 60 * 60 * 1000; // 1小时
          const nextTime = new Date(new Date().getTime() + delayMs);
          
          // 更新钱包状态
          scheduler.updateWalletState(walletAddress, {
            nextExecutionTime: nextTime.toISOString(),
            lastError: `服务器错误(500): ${error.message}`,
            lastErrorTime: new Date().toISOString()
          });
          
          // 保存钱包状态到文件
          scheduler.saveState();
          
          log(`钱包 ${walletAddress.substring(0, 8)}... 遇到服务器错误，已设置1小时后重试`, "warning");
          
          // 服务器500错误直接返回null，不再重试
          return null;
        }
      }
      
      if (attempt < MAX_RETRIES) {
        // 等待一段时间后重试
        const waitTime = RETRY_DELAY * attempt;
        log(`等待 ${waitTime/1000} 秒后重试...`, "info");
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }
  
  log(`钱包 ${walletAddress.substring(0, 8)}... 认证失败，已达到最大重试次数`, "error");
  
  // 认证失败后强制更新钱包状态
  try {
    const scheduler = require('./scheduler');
    
    // 设置30分钟后再尝试
    const delayMs = 30 * 60 * 1000; // 30分钟
    const nextTime = new Date(new Date().getTime() + delayMs);
    
    // 更新钱包状态
    scheduler.updateWalletState(walletAddress, {
      nextExecutionTime: nextTime.toISOString(),
      lastError: "认证失败，已达到最大重试次数",
      lastErrorTime: new Date().toISOString()
    });
    
    // 保存钱包状态到文件
    scheduler.saveState();
    
    log(`钱包 ${walletAddress.substring(0, 8)}... 已设置30分钟后重试`, "warning");
  } catch (error) {
    log(`更新钱包状态失败: ${error.message}`, "error");
  }
  
  return null;
}

/**
 * 验证token是否有效
 * @param {string} token 要验证的token
 * @param {string} walletAddress 钱包地址
 * @returns {Promise<boolean>} 是否有效
 */
async function verifyToken(token, walletAddress) {
  if (!token) {
    log("无法验证空的token", "error");
    return false;
  }

  try {
    log("验证token有效性...", "info");
    
    // 按照原始代码设置请求头
    const headers = {
      ...config.DEFAULT_HEADERS,
      "X-Session-Token": token
    };
    
    // 获取代理
    const binding = walletProxyManager.getWalletBinding(walletAddress);
    const proxyUrl = binding ? binding.proxyUrl : null;
    
    // 配置请求选项
    const requestOptions = {
      headers,
      timeout: config.REQUEST_TIMEOUT || 30000
    };
    
    if (proxyUrl) {
      log(`验证token使用代理: ${proxyUrl}`, "info");
      if (config.BASE_URL.startsWith("https")) {
        requestOptions.httpsAgent = new HttpsProxyAgent(proxyUrl);
      } else {
        requestOptions.httpAgent = new HttpProxyAgent(proxyUrl);
      }
    }
    
    // 只验证/me端点，与原始代码一致
    let isValid = false;
    
    try {
      // 发送请求
      const response = await axios.get(`${config.BASE_URL}/me`, requestOptions);
      // 只检查状态码是否为200
      isValid = response.status === 200;
      log(`Token验证${isValid ? '成功' : '失败'}`, isValid ? "success" : "warning");
    } catch (error) {
      log(`Token验证失败: ${error.message}`, "warning");
      isValid = false;
    }
    
    // 如果token有效，更新绑定关系
    if (isValid && binding) {
      // 确保绑定关系中的token是最新的
      if (binding.sessionToken !== token) {
        log(`更新钱包 ${walletAddress.substring(0, 8)}... 的token绑定关系`, "info");
        walletProxyManager.updateWalletToken(walletAddress, token);
      }
      
      // 将token写入文件（与原始代码保持一致）
      try {
        const tokenPath = path.join(process.cwd(), "session-token.key");
        if (!fs.existsSync(tokenPath) || !fs.readFileSync(tokenPath, "utf8").includes(token)) {
          fs.appendFileSync(tokenPath, `${token}\n`);
          log(`已将token写入文件`, "success");
        }
      } catch (fileError) {
        log(`写入token文件失败: ${fileError.message}`, "error");
      }
    }
    
    return isValid;
  } catch (error) {
    log(`验证token失败: ${error.message}`, "error");
    return false;
  }
}

/**
 * 获取有效的token
 * 如果现有token有效则返回，否则尝试重新认证
 * @param {string} walletAddress 钱包地址
 * @returns {Promise<string|null>} token或null
 */
async function getValidToken(walletAddress) {
  // 从绑定关系中获取token
  const binding = walletProxyManager.getWalletBinding(walletAddress);
  const existingToken = binding ? binding.sessionToken : null;
  
  // 如果有token，验证其有效性
  if (existingToken) {
    log(`验证钱包 ${walletAddress.substring(0, 8)}... 的现有token`, "info");
    const isValid = await verifyToken(existingToken, walletAddress);
    
    if (isValid) {
      log(`钱包 ${walletAddress.substring(0, 8)}... 的token有效`, "success");
      return existingToken;
    }
    
    log(`钱包 ${walletAddress.substring(0, 8)}... 的token无效，尝试重新认证`, "warning");
  } else {
    log(`钱包 ${walletAddress.substring(0, 8)}... 没有token，尝试认证`, "info");
  }
  
  // 重新认证获取token
  return await authenticate(walletAddress);
}

module.exports = {
  authenticate,
  verifyToken,
  getValidToken,
  findPrivateKeyByAddress
};
