# 代理配置文件示例
# 每行一个代理，支持多种格式

# 格式1: user:pass@ip:port
username1:password1@*************:8080
username2:password2@*************:8080

# 格式2: *******************:port
*********************************************
*********************************************

# 格式3: socks5://user:pass@ip:port
socks5://username5:password5@*************:1080
socks5://username6:password6@*************:1080

# 格式4: ip:port:user:pass
*************:8080:username7:password7
*************:8080:username8:password8

# 如果代理不需要认证，可以只写ip:port
*************:8080
*************:8080

# 注意事项：
# 1. 确保代理服务器可以正常访问外网
# 2. 建议使用高质量的住宅代理或数据中心代理
# 3. 避免使用免费代理，稳定性和速度都不好
# 4. 代理数量建议与客户端数量匹配或更多
