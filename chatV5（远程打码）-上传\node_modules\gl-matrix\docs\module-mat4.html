<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>JSDoc: Module: mat4</title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">
</head>

<body>

<div id="main">

    <h1 class="page-title">Module: mat4</h1>

    




<section>

<header>
    
        
            
        
    
</header>

<article>
    <div class="container-overview">
    
        
            <div class="description">4x4 Matrix<br>Format: column-major, when typed out it looks like row-major<br>The matrices are being post multiplied.</div>
        

        
            















<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line3">line 3</a>
    </li></ul></dd>
    

    

    

    
</dl>


















        
    
    </div>

    

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        
            

    

    
    <h4 class="name" id=".add"><span class="type-signature">(static) </span>add<span class="signature">(out, a, b)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Adds two mat4's
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line1550">line 1550</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".adjoint"><span class="type-signature">(static) </span>adjoint<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Calculates the adjugate of a mat4
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the source matrix</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line310">line 310</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".clone"><span class="type-signature">(static) </span>clone<span class="signature">(a)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Creates a new mat4 initialized with values from an existing matrix
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">matrix to clone</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line42">line 42</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    a new 4x4 matrix
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".copy"><span class="type-signature">(static) </span>copy<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Copy the values from one mat4 to another
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the source matrix</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line70">line 70</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".create"><span class="type-signature">(static) </span>create<span class="signature">()</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Creates a new identity mat4
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line13">line 13</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    a new 4x4 matrix
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".determinant"><span class="type-signature">(static) </span>determinant<span class="signature">(a)</span><span class="type-signature"> &rarr; {Number}</span></h4>
    

    



<div class="description">
    Calculates the determinant of a mat4
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the source matrix</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line341">line 341</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    determinant of a
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".equals"><span class="type-signature">(static) </span>equals<span class="signature">(a, b)</span><span class="type-signature"> &rarr; {Boolean}</span></h4>
    

    



<div class="description">
    Returns whether or not the matrices have approximately the same elements in the same position.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">The first matrix.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">The second matrix.</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line1676">line 1676</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    True if the matrices are equal, false otherwise.
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".exactEquals"><span class="type-signature">(static) </span>exactEquals<span class="signature">(a, b)</span><span class="type-signature"> &rarr; {Boolean}</span></h4>
    

    



<div class="description">
    Returns whether or not the matrices have exactly the same elements in the same position (when compared with ===)
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">The first matrix.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">The second matrix.</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line1662">line 1662</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    True if the matrices are equal, false otherwise.
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".frob"><span class="type-signature">(static) </span>frob<span class="signature">(a)</span><span class="type-signature"> &rarr; {Number}</span></h4>
    

    



<div class="description">
    Returns Frobenius norm of a mat4
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the matrix to calculate Frobenius norm of</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line1538">line 1538</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    Frobenius norm
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromQuat"><span class="type-signature">(static) </span>fromQuat<span class="signature">(out, q)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Calculates a 4x4 matrix from the given quaternion
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">mat4 receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>q</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">Quaternion to create matrix from</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line1186">line 1186</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromQuat2"><span class="type-signature">(static) </span>fromQuat2<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Creates a new mat4 from a dual quat.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">Matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">Dual Quaternion</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line938">line 938</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    mat4 receiving operation result
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromRotation"><span class="type-signature">(static) </span>fromRotation<span class="signature">(out, rad, axis)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Creates a matrix from a given angle around a given axis
This is equivalent to (but much faster than):

    mat4.identity(dest);
    mat4.rotate(dest, dest, rad, axis);
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">mat4 receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rad</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">the angle to rotate the matrix by</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>axis</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">the axis to rotate around</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line738">line 738</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromRotationTranslation"><span class="type-signature">(static) </span>fromRotationTranslation<span class="signature">(out, q, v)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Creates a matrix from a quaternion rotation and vector translation
This is equivalent to (but much faster than):

    mat4.identity(dest);
    mat4.translate(dest, vec);
    let quatMat = mat4.create();
    quat4.toMat4(quat, quatMat);
    mat4.multiply(dest, quatMat);
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">mat4 receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>q</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat4</span>


            
            </td>

            

            

            <td class="description last">Rotation quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>v</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">Translation vector</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line894">line 894</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromRotationTranslationScale"><span class="type-signature">(static) </span>fromRotationTranslationScale<span class="signature">(out, q, v, s)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Creates a matrix from a quaternion rotation, vector translation and vector scale
This is equivalent to (but much faster than):

    mat4.identity(dest);
    mat4.translate(dest, vec);
    let quatMat = mat4.create();
    quat4.toMat4(quat, quatMat);
    mat4.multiply(dest, quatMat);
    mat4.scale(dest, scale)
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">mat4 receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>q</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat4</span>


            
            </td>

            

            

            <td class="description last">Rotation quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>v</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">Translation vector</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>s</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">Scaling vector</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line1063">line 1063</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromRotationTranslationScaleOrigin"><span class="type-signature">(static) </span>fromRotationTranslationScaleOrigin<span class="signature">(out, q, v, s, o)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Creates a matrix from a quaternion rotation, vector translation and vector scale, rotating and scaling around the given origin
This is equivalent to (but much faster than):

    mat4.identity(dest);
    mat4.translate(dest, vec);
    mat4.translate(dest, origin);
    let quatMat = mat4.create();
    quat4.toMat4(quat, quatMat);
    mat4.multiply(dest, quatMat);
    mat4.scale(dest, scale)
    mat4.translate(dest, negativeOrigin);
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">mat4 receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>q</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat4</span>


            
            </td>

            

            

            <td class="description last">Rotation quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>v</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">Translation vector</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>s</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">Scaling vector</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>o</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">The origin vector around which to scale and rotate</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line1123">line 1123</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromScaling"><span class="type-signature">(static) </span>fromScaling<span class="signature">(out, v)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Creates a matrix from a vector scaling
This is equivalent to (but much faster than):

    mat4.identity(dest);
    mat4.scale(dest, dest, vec);
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">mat4 receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>v</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">Scaling vector</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line706">line 706</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromTranslation"><span class="type-signature">(static) </span>fromTranslation<span class="signature">(out, v)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Creates a matrix from a vector translation
This is equivalent to (but much faster than):

    mat4.identity(dest);
    mat4.translate(dest, dest, vec);
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">mat4 receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>v</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">Translation vector</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line675">line 675</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromValues"><span class="type-signature">(static) </span>fromValues<span class="signature">(m00, m01, m02, m03, m10, m11, m12, m13, m20, m21, m22, m23, m30, m31, m32, m33)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Create a new mat4 with the given values
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>m00</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 0, row 0 position (index 0)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m01</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 0, row 1 position (index 1)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m02</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 0, row 2 position (index 2)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m03</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 0, row 3 position (index 3)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m10</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 1, row 0 position (index 4)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m11</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 1, row 1 position (index 5)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m12</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 1, row 2 position (index 6)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m13</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 1, row 3 position (index 7)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m20</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 2, row 0 position (index 8)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m21</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 2, row 1 position (index 9)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m22</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 2, row 2 position (index 10)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m23</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 2, row 3 position (index 11)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m30</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 3, row 0 position (index 12)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m31</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 3, row 1 position (index 13)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m32</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 3, row 2 position (index 14)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m33</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 3, row 3 position (index 15)</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line111">line 111</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    A new mat4
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromXRotation"><span class="type-signature">(static) </span>fromXRotation<span class="signature">(out, rad)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Creates a matrix from the given angle around the X axis
This is equivalent to (but much faster than):

    mat4.identity(dest);
    mat4.rotateX(dest, dest, rad);
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">mat4 receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rad</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">the angle to rotate the matrix by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line785">line 785</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromYRotation"><span class="type-signature">(static) </span>fromYRotation<span class="signature">(out, rad)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Creates a matrix from the given angle around the Y axis
This is equivalent to (but much faster than):

    mat4.identity(dest);
    mat4.rotateY(dest, dest, rad);
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">mat4 receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rad</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">the angle to rotate the matrix by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line820">line 820</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromZRotation"><span class="type-signature">(static) </span>fromZRotation<span class="signature">(out, rad)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Creates a matrix from the given angle around the Z axis
This is equivalent to (but much faster than):

    mat4.identity(dest);
    mat4.rotateZ(dest, dest, rad);
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">mat4 receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rad</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">the angle to rotate the matrix by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line855">line 855</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".frustum"><span class="type-signature">(static) </span>frustum<span class="signature">(out, left, right, bottom, top, near, far)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Generates a frustum matrix with the given bounds
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">mat4 frustum matrix will be written into</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>left</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Left bound of the frustum</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>right</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Right bound of the frustum</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>bottom</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Bottom bound of the frustum</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>top</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Top bound of the frustum</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>near</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Near bound of the frustum</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>far</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Far bound of the frustum</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line1237">line 1237</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".getRotation"><span class="type-signature">(static) </span>getRotation<span class="signature">(out, mat)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Returns a quaternion representing the rotational component
 of a transformation matrix. If a matrix is built with
 fromRotationTranslation, the returned quaternion will be the
 same as the quaternion originally supplied.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">Quaternion to receive the rotation component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>mat</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">Matrix to be decomposed (input)</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line1012">line 1012</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".getScaling"><span class="type-signature">(static) </span>getScaling<span class="signature">(out, mat)</span><span class="type-signature"> &rarr; {vec3}</span></h4>
    

    



<div class="description">
    Returns the scaling factor component of a transformation
 matrix. If a matrix is built with fromRotationTranslationScale
 with a normalized Quaternion paramter, the returned vector will be
 the same as the scaling vector
 originally supplied.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">Vector to receive scaling factor component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>mat</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">Matrix to be decomposed (input)</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line985">line 985</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">vec3</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".getTranslation"><span class="type-signature">(static) </span>getTranslation<span class="signature">(out, mat)</span><span class="type-signature"> &rarr; {vec3}</span></h4>
    

    



<div class="description">
    Returns the translation vector component of a transformation
 matrix. If a matrix is built with fromRotationTranslation,
 the returned vector will be the same as the translation vector
 originally supplied.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">Vector to receive translation component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>mat</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">Matrix to be decomposed (input)</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line967">line 967</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">vec3</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".identity"><span class="type-signature">(static) </span>identity<span class="signature">(out)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Set a mat4 to the identity matrix
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line181">line 181</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".invert"><span class="type-signature">(static) </span>invert<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Inverts a mat4
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the source matrix</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line256">line 256</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".lookAt"><span class="type-signature">(static) </span>lookAt<span class="signature">(out, eye, center, up)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Generates a look-at matrix with the given eye position, focal point, and up axis.
If you want a matrix that actually makes an object look at another object, you should use targetTo instead.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">mat4 frustum matrix will be written into</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>eye</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">Position of the viewer</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>center</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">Point the viewer is looking at</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>up</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">vec3 pointing up</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line1381">line 1381</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".mul"><span class="type-signature">(static) </span>mul<span class="signature">()</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    Alias for mat4.multiply
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line1709">line 1709</a>
    </li></ul></dd>
    

    

    

    
</dl>


















        
            

    

    
    <h4 class="name" id=".multiply"><span class="type-signature">(static) </span>multiply<span class="signature">(out, a, b)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Multiplies two mat4s
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line372">line 372</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".multiplyScalar"><span class="type-signature">(static) </span>multiplyScalar<span class="signature">(out, a, b)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Multiply each element of the matrix by a scalar.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the matrix to scale</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">amount to scale the matrix's elements by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line1606">line 1606</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".multiplyScalarAndAdd"><span class="type-signature">(static) </span>multiplyScalarAndAdd<span class="signature">(out, a, b, scale)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Adds two mat4's after multiplying each element of the second operand by a scalar value.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the receiving vector</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>scale</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">the amount to scale b's elements by before adding</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line1635">line 1635</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".ortho"><span class="type-signature">(static) </span>ortho<span class="signature">(out, left, right, bottom, top, near, far)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Generates a orthogonal projection matrix with the given bounds
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">mat4 frustum matrix will be written into</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>left</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last">Left bound of the frustum</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>right</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last">Right bound of the frustum</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>bottom</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last">Bottom bound of the frustum</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>top</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last">Top bound of the frustum</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>near</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last">Near bound of the frustum</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>far</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last">Far bound of the frustum</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line1348">line 1348</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".perspective"><span class="type-signature">(static) </span>perspective<span class="signature">(out, fovy, aspect, near, far)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Generates a perspective projection matrix with the given bounds.
Passing null/undefined/no value for far will generate infinite projection matrix.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">mat4 frustum matrix will be written into</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fovy</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last">Vertical field of view in radians</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>aspect</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last">Aspect ratio. typically viewport width/height</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>near</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last">Near bound of the frustum</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>far</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last">Far bound of the frustum, can be null or Infinity</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line1271">line 1271</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".perspectiveFromFieldOfView"><span class="type-signature">(static) </span>perspectiveFromFieldOfView<span class="signature">(out, fov, near, far)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Generates a perspective projection matrix with the given field of view.
This is primarily useful for generating projection matrices to be used
with the still experiemental WebVR API.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">mat4 frustum matrix will be written into</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fov</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>


            
            </td>

            

            

            <td class="description last">Object containing the following values: upDegrees, downDegrees, leftDegrees, rightDegrees</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>near</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last">Near bound of the frustum</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>far</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last">Far bound of the frustum</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line1309">line 1309</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".rotate"><span class="type-signature">(static) </span>rotate<span class="signature">(out, a, rad, axis)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Rotates a mat4 by the given angle around the given axis
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the matrix to rotate</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rad</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">the angle to rotate the matrix by</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>axis</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">the axis to rotate around</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line481">line 481</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".rotateX"><span class="type-signature">(static) </span>rotateX<span class="signature">(out, a, rad)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Rotates a matrix by the given angle around the X axis
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the matrix to rotate</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rad</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">the angle to rotate the matrix by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line543">line 543</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".rotateY"><span class="type-signature">(static) </span>rotateY<span class="signature">(out, a, rad)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Rotates a matrix by the given angle around the Y axis
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the matrix to rotate</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rad</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">the angle to rotate the matrix by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line586">line 586</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".rotateZ"><span class="type-signature">(static) </span>rotateZ<span class="signature">(out, a, rad)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Rotates a matrix by the given angle around the Z axis
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the matrix to rotate</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rad</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">the angle to rotate the matrix by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line629">line 629</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".scale"><span class="type-signature">(static) </span>scale<span class="signature">(out, a, v)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Scales the mat4 by the dimensions in the given vec3 not using vectorization
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the matrix to scale</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>v</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">the vec3 to scale the matrix by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line450">line 450</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".set"><span class="type-signature">(static) </span>set<span class="signature">(out, m00, m01, m02, m03, m10, m11, m12, m13, m20, m21, m22, m23, m30, m31, m32, m33)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Set the components of a mat4 to the given values
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m00</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 0, row 0 position (index 0)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m01</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 0, row 1 position (index 1)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m02</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 0, row 2 position (index 2)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m03</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 0, row 3 position (index 3)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m10</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 1, row 0 position (index 4)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m11</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 1, row 1 position (index 5)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m12</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 1, row 2 position (index 6)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m13</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 1, row 3 position (index 7)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m20</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 2, row 0 position (index 8)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m21</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 2, row 1 position (index 9)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m22</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 2, row 2 position (index 10)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m23</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 2, row 3 position (index 11)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m30</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 3, row 0 position (index 12)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m31</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 3, row 1 position (index 13)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m32</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 3, row 2 position (index 14)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m33</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 3, row 3 position (index 15)</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line154">line 154</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".str"><span class="type-signature">(static) </span>str<span class="signature">(a)</span><span class="type-signature"> &rarr; {String}</span></h4>
    

    



<div class="description">
    Returns a string representation of a mat4
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">matrix to represent as a string</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line1525">line 1525</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    string representation of the matrix
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".sub"><span class="type-signature">(static) </span>sub<span class="signature">()</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    Alias for mat4.subtract
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line1715">line 1715</a>
    </li></ul></dd>
    

    

    

    
</dl>


















        
            

    

    
    <h4 class="name" id=".subtract"><span class="type-signature">(static) </span>subtract<span class="signature">(out, a, b)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Subtracts matrix b from matrix a
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line1578">line 1578</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".targetTo"><span class="type-signature">(static) </span>targetTo<span class="signature">(out, eye, center, up)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Generates a matrix that makes something look at something else.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">mat4 frustum matrix will be written into</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>eye</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">Position of the viewer</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>center</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">Point the viewer is looking at</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>up</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">vec3 pointing up</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line1468">line 1468</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".translate"><span class="type-signature">(static) </span>translate<span class="signature">(out, a, v)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Translate a mat4 by the given vector
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the matrix to translate</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>v</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">vector to translate by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line413">line 413</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".transpose"><span class="type-signature">(static) </span>transpose<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {mat4}</span></h4>
    

    



<div class="description">
    Transpose the values of a mat4
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the source matrix</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat4.js.html">mat4.js</a>, <a href="mat4.js.html#line208">line 208</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat4</span>


    </dd>
</dl>

    





        
    

    

    
</article>

</section>




</div>

<nav>
    <h2><a href="index.html">Home</a></h2><h3>Modules</h3><ul><li><a href="module-glMatrix.html">glMatrix</a></li><li><a href="module-mat2.html">mat2</a></li><li><a href="module-mat2d.html">mat2d</a></li><li><a href="module-mat3.html">mat3</a></li><li><a href="module-mat4.html">mat4</a></li><li><a href="module-quat.html">quat</a></li><li><a href="module-quat2.html">quat2</a></li><li><a href="module-vec2.html">vec2</a></li><li><a href="module-vec3.html">vec3</a></li><li><a href="module-vec4.html">vec4</a></li></ul>
</nav>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.5.5</a> on Fri Jul 13 2018 11:51:34 GMT+0200 (W. Europe Daylight Time)
</footer>

<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>
</body>
</html>