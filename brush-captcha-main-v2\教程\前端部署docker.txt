vps
1.上传本脚本
2.更新\tmp
3.
cd brush-captcha
bash install_server_and_frontend.sh
SSL选否

http://ip:8080/
账号:ddk
密码:Ddk19940316.





# 强制停止所有容器
docker stop $(docker ps -aq) 2>/dev/null || true

# 删除所有容器
docker rm $(docker ps -aq) 2>/dev/null || true

# 删除所有镜像
docker rmi $(docker images -q) 2>/dev/null || true

# 清理所有未使用的资源
docker system prune -a -f --volumes

# 回到上级目录
cd ~

# 完全删除项目目录
rm -rf brush-captcha

# 重新克隆项目
git clone https://github.com/Brush-Bot/brush-captcha.git
cd brush-captcha


