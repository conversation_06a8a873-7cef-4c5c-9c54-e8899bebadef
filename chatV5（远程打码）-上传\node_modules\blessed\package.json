{"name": "blessed", "description": "A high-level terminal interface library for node.js.", "author": "<PERSON>", "version": "0.1.81", "license": "MIT", "main": "./lib/blessed.js", "bin": "./bin/tput.js", "preferGlobal": false, "repository": "git://github.com/chjj/blessed.git", "homepage": "https://github.com/chjj/blessed", "bugs": {"url": "http://github.com/chjj/blessed/issues"}, "keywords": ["curses", "tui", "tput", "terminfo", "termcap"], "tags": ["curses", "tui", "tput", "terminfo", "termcap"], "engines": {"node": ">= 0.8.0"}, "browserify": {"transform": ["./browser/transform.js"]}}