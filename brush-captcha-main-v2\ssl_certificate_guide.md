# 🔐 SSL证书配置指南

## 📍 证书部署位置

### ✅ **只在主服务器生成SSL证书**
- **主服务器**：需要SSL证书（提供HTTPS管理界面和WSS服务）
- **客户端服务器**：不需要SSL证书（只是连接方）

## 🛠️ SSL证书生成方式

### 方式1：自动生成（推荐）

在主服务器上运行部署脚本时选择生成：

```bash
bash scripts/deploy_server.sh
# 当询问是否生成SSL证书时，选择 y
```

### 方式2：手动生成

在主服务器上手动生成：

```bash
# 获取服务器公网IP
SERVER_IP=$(curl -s ifconfig.me)

# 创建SSL配置文件
cat > /tmp/ssl.conf << EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = CN
ST = Beijing
L = Beijing
O = MyCompany
OU = Dev
CN = $SERVER_IP

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
IP.1 = $SERVER_IP
DNS.1 = localhost
EOF

# 生成SSL证书
openssl req -x509 -newkey rsa:2048 -nodes \
    -keyout tmp/server.key -out tmp/server.crt -days 365 \
    -config /tmp/ssl.conf -extensions v3_req

echo "✅ SSL证书已生成"
rm /tmp/ssl.conf
```

### 方式3：使用现有证书

如果你有购买的SSL证书：

```bash
# 将证书文件复制到tmp目录
cp your_certificate.crt tmp/server.crt
cp your_private.key tmp/server.key

# 或者如果是PEM格式
cp your_certificate.pem tmp/
```

## 📂 证书文件位置

### 主服务器证书文件
```
brush-captcha/
├── tmp/
│   ├── server.crt    # SSL证书文件
│   ├── server.key    # SSL私钥文件
│   ├── proxies.txt   # 代理配置
│   └── user_keys.txt # API Keys
```

### 客户端服务器
```
brush-captcha/
└── client/
    └── config.yaml   # 只需要配置文件，无需SSL证书
```

## 🔗 连接配置

### 主服务器启用SSL后的地址
- **管理后台**：`https://主服务器IP:8080`
- **WebSocket服务**：`wss://主服务器IP:8080/ws/worker/`

### 客户端配置示例

#### 主服务器启用SSL时：
```yaml
worker:
  name: "client-01"
  wss_url: "wss://主服务器IP:8080/ws/worker/"
```

#### 主服务器未启用SSL时：
```yaml
worker:
  name: "client-01"
  wss_url: "ws://主服务器IP:8080/ws/worker/"
```

## 🚨 常见问题

### Q1: 客户端连接SSL服务器时出现证书错误？
**A:** 这是正常的，因为使用的是自签证书。客户端代码已经配置为忽略证书验证：

<augment_code_snippet path="client/core/ws_client.py" mode="EXCERPT">
````python
ssl_ctx = ssl._create_unverified_context()
async with websockets.connect(uri,ssl=ssl_ctx) as ws:
````
</augment_code_snippet>

### Q2: 浏览器访问管理后台时提示不安全？
**A:** 这是自签证书的正常现象，点击"高级" → "继续访问"即可。

### Q3: 是否可以不使用SSL？
**A:** 可以，在安装脚本中选择不启用SSL，但建议在生产环境中使用SSL。

### Q4: 如何更新SSL证书？
**A:** 
1. 替换 `tmp/server.crt` 和 `tmp/server.key` 文件
2. 重新运行安装脚本
3. 或者直接重启容器：`docker compose restart`

## 🔧 SSL证书验证

### 验证证书是否正确生成
```bash
# 查看证书信息
openssl x509 -in tmp/server.crt -text -noout

# 验证证书和私钥匹配
openssl x509 -noout -modulus -in tmp/server.crt | openssl md5
openssl rsa -noout -modulus -in tmp/server.key | openssl md5
# 两个MD5值应该相同
```

### 测试SSL连接
```bash
# 测试HTTPS连接
curl -k https://主服务器IP:8080

# 测试WSS连接（需要安装wscat）
npm install -g wscat
wscat -c wss://主服务器IP:8080/ws/worker/test --no-check
```

## 📋 部署检查清单

### ✅ 主服务器SSL配置
- [ ] SSL证书文件已生成：`tmp/server.crt`
- [ ] SSL私钥文件已生成：`tmp/server.key`
- [ ] 安装脚本选择启用SSL
- [ ] 管理后台可通过HTTPS访问
- [ ] 防火墙已开放8080端口

### ✅ 客户端配置
- [ ] 客户端配置使用正确的协议（wss://或ws://）
- [ ] 客户端配置使用正确的主服务器IP
- [ ] 客户端能成功连接到主服务器
- [ ] 在管理后台能看到客户端在线状态

## 💡 最佳实践

1. **生产环境**：建议购买正式的SSL证书
2. **测试环境**：使用自签证书即可
3. **证书有效期**：自签证书默认365天，注意及时更新
4. **备份证书**：保存好证书文件，便于迁移和恢复
5. **安全考虑**：不要将私钥文件泄露给他人
