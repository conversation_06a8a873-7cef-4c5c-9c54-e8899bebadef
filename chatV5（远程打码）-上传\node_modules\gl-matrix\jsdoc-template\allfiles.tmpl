<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
	<head>
		<meta http-equiv="content-type" content="text/html; charset={+IO.encoding+}" />
		{! Link.base = ""; /* all generated links will be relative to this */ !}
		<title>glMatrix - File Index</title>
		<meta name="generator" content="JsDoc Toolkit" />
		
		<style type="text/css">
		{+include("static/default.css")+}
		</style>
	</head>
	
	<body>
		{+include("static/header.html")+}
		
		<div class="wrapper">

			<header id="index">
				{+publish.classesIndex+}
			</header>

			<section id="content">
				<h1 class="classTitle">File Index</h1>
				
				<for each="item" in="data">
				<div>
					<h2>{+new Link().toSrc(item.alias).withText(item.name)+}</h2>
					<if test="item.desc">{+resolveLinks(item.desc)+}</if>
					<dl>
						<if test="item.author">
							<dt class="heading">Author:</dt>
							<dd>{+item.author+}</dd>
						</if>
						<if test="item.version">
							<dt class="heading">Version:</dt>
								<dd>{+item.version+}</dd>
						</if>
						{! var locations = item.comment.getTag('location').map(function($){return $.toString().replace(/(^\$ ?| ?\$$)/g, '').replace(/^HeadURL: https:/g, 'http:');}) !}
						<if test="locations.length">
							<dt class="heading">Location:</dt>
								<for each="location" in="locations">
								<dd><a href="{+location+}">{+location+}</a></dd>
								</for>
						</if>
					</dl>
				</div>
				<hr />
				</for>
				
			</section>

			<footer>
				<small>
					<if test="JSDOC.opt.D.copyright">&copy;{+JSDOC.opt.D.copyright+}<br /></if>
					Documentation generated by <a href="http://code.google.com/p/jsdoc-toolkit/" target="_blank">JsDoc Toolkit</a> {+JSDOC.VERSION+} on {+new Date()+}
					<br/><br/>
					Theme based on Github Pages template by <a href="https://github.com/orderedlist">orderedlist</a>
				</small>
			</footer>

		</div>
	</body>
</html>