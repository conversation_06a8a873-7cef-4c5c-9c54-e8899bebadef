<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>JSDoc: Module: mat2</title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">
</head>

<body>

<div id="main">

    <h1 class="page-title">Module: mat2</h1>

    




<section>

<header>
    
        
            
        
    
</header>

<article>
    <div class="container-overview">
    
        
            <div class="description">2x2 Matrix</div>
        

        
            















<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line3">line 3</a>
    </li></ul></dd>
    

    

    

    
</dl>


















        
    
    </div>

    

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        
            

    

    
    <h4 class="name" id=".add"><span class="type-signature">(static) </span>add<span class="signature">(out, a, b)</span><span class="type-signature"> &rarr; {mat2}</span></h4>
    

    



<div class="description">
    Adds two mat2's
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line321">line 321</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".adjoint"><span class="type-signature">(static) </span>adjoint<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {mat2}</span></h4>
    

    



<div class="description">
    Calculates the adjugate of a mat2
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the source matrix</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line161">line 161</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".clone"><span class="type-signature">(static) </span>clone<span class="signature">(a)</span><span class="type-signature"> &rarr; {mat2}</span></h4>
    

    



<div class="description">
    Creates a new mat2 initialized with values from an existing matrix
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">matrix to clone</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line30">line 30</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    a new 2x2 matrix
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".copy"><span class="type-signature">(static) </span>copy<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {mat2}</span></h4>
    

    



<div class="description">
    Copy the values from one mat2 to another
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the source matrix</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line46">line 46</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".create"><span class="type-signature">(static) </span>create<span class="signature">()</span><span class="type-signature"> &rarr; {mat2}</span></h4>
    

    



<div class="description">
    Creates a new identity mat2
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line13">line 13</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    a new 2x2 matrix
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".determinant"><span class="type-signature">(static) </span>determinant<span class="signature">(a)</span><span class="type-signature"> &rarr; {Number}</span></h4>
    

    



<div class="description">
    Calculates the determinant of a mat2
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the source matrix</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line178">line 178</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    determinant of a
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".equals"><span class="type-signature">(static) </span>equals<span class="signature">(a, b)</span><span class="type-signature"> &rarr; {Boolean}</span></h4>
    

    



<div class="description">
    Returns whether or not the matrices have approximately the same elements in the same position.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">The first matrix.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">The second matrix.</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line363">line 363</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    True if the matrices are equal, false otherwise.
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".exactEquals"><span class="type-signature">(static) </span>exactEquals<span class="signature">(a, b)</span><span class="type-signature"> &rarr; {Boolean}</span></h4>
    

    



<div class="description">
    Returns whether or not the matrices have exactly the same elements in the same position (when compared with ===)
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">The first matrix.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">The second matrix.</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line352">line 352</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    True if the matrices are equal, false otherwise.
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".frob"><span class="type-signature">(static) </span>frob<span class="signature">(a)</span><span class="type-signature"> &rarr; {Number}</span></h4>
    

    



<div class="description">
    Returns Frobenius norm of a mat2
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the matrix to calculate Frobenius norm of</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line293">line 293</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    Frobenius norm
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromRotation"><span class="type-signature">(static) </span>fromRotation<span class="signature">(out, rad)</span><span class="type-signature"> &rarr; {mat2}</span></h4>
    

    



<div class="description">
    Creates a matrix from a given angle
This is equivalent to (but much faster than):

    mat2.identity(dest);
    mat2.rotate(dest, dest, rad);
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">mat2 receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rad</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">the angle to rotate the matrix by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line248">line 248</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromScaling"><span class="type-signature">(static) </span>fromScaling<span class="signature">(out, v)</span><span class="type-signature"> &rarr; {mat2}</span></h4>
    

    



<div class="description">
    Creates a matrix from a vector scaling
This is equivalent to (but much faster than):

    mat2.identity(dest);
    mat2.scale(dest, dest, vec);
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">mat2 receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>v</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec2</span>


            
            </td>

            

            

            <td class="description last">Scaling vector</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line269">line 269</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromValues"><span class="type-signature">(static) </span>fromValues<span class="signature">(m00, m01, m10, m11)</span><span class="type-signature"> &rarr; {mat2}</span></h4>
    

    



<div class="description">
    Create a new mat2 with the given values
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>m00</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 0, row 0 position (index 0)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m01</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 0, row 1 position (index 1)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m10</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 1, row 0 position (index 2)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m11</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 1, row 1 position (index 3)</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line77">line 77</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out A new 2x2 matrix
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".identity"><span class="type-signature">(static) </span>identity<span class="signature">(out)</span><span class="type-signature"> &rarr; {mat2}</span></h4>
    

    



<div class="description">
    Set a mat2 to the identity matrix
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line60">line 60</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".invert"><span class="type-signature">(static) </span>invert<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {mat2}</span></h4>
    

    



<div class="description">
    Inverts a mat2
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the source matrix</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line135">line 135</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".LDU"><span class="type-signature">(static) </span>LDU<span class="signature">(L, D, U, a)</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    Returns L, D and U matrices (Lower triangular, Diagonal and Upper triangular) by factorizing the input matrix
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>L</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the lower triangular matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>D</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the diagonal matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>U</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the upper triangular matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the input matrix to factorize</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line305">line 305</a>
    </li></ul></dd>
    

    

    

    
</dl>


















        
            

    

    
    <h4 class="name" id=".mul"><span class="type-signature">(static) </span>mul<span class="signature">()</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    Alias for mat2.multiply
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line409">line 409</a>
    </li></ul></dd>
    

    

    

    
</dl>


















        
            

    

    
    <h4 class="name" id=".multiply"><span class="type-signature">(static) </span>multiply<span class="signature">(out, a, b)</span><span class="type-signature"> &rarr; {mat2}</span></h4>
    

    



<div class="description">
    Multiplies two mat2's
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line190">line 190</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".multiplyScalar"><span class="type-signature">(static) </span>multiplyScalar<span class="signature">(out, a, b)</span><span class="type-signature"> &rarr; {mat2}</span></h4>
    

    



<div class="description">
    Multiply each element of the matrix by a scalar.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the matrix to scale</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">amount to scale the matrix's elements by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line380">line 380</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".multiplyScalarAndAdd"><span class="type-signature">(static) </span>multiplyScalarAndAdd<span class="signature">(out, a, b, scale)</span><span class="type-signature"> &rarr; {mat2}</span></h4>
    

    



<div class="description">
    Adds two mat2's after multiplying each element of the second operand by a scalar value.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the receiving vector</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>scale</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">the amount to scale b's elements by before adding</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line397">line 397</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".rotate"><span class="type-signature">(static) </span>rotate<span class="signature">(out, a, rad)</span><span class="type-signature"> &rarr; {mat2}</span></h4>
    

    



<div class="description">
    Rotates a mat2 by the given angle
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the matrix to rotate</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rad</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">the angle to rotate the matrix by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line208">line 208</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".scale"><span class="type-signature">(static) </span>scale<span class="signature">(out, a, v)</span><span class="type-signature"> &rarr; {mat2}</span></h4>
    

    



<div class="description">
    Scales the mat2 by the dimensions in the given vec2
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the matrix to rotate</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>v</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec2</span>


            
            </td>

            

            

            <td class="description last">the vec2 to scale the matrix by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line227">line 227</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".set"><span class="type-signature">(static) </span>set<span class="signature">(out, m00, m01, m10, m11)</span><span class="type-signature"> &rarr; {mat2}</span></h4>
    

    



<div class="description">
    Set the components of a mat2 to the given values
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m00</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 0, row 0 position (index 0)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m01</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 0, row 1 position (index 1)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m10</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 1, row 0 position (index 2)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>m11</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Component in column 1, row 1 position (index 3)</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line96">line 96</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".str"><span class="type-signature">(static) </span>str<span class="signature">(a)</span><span class="type-signature"> &rarr; {String}</span></h4>
    

    



<div class="description">
    Returns a string representation of a mat2
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">matrix to represent as a string</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line283">line 283</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    string representation of the matrix
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".sub"><span class="type-signature">(static) </span>sub<span class="signature">()</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    Alias for mat2.subtract
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line415">line 415</a>
    </li></ul></dd>
    

    

    

    
</dl>


















        
            

    

    
    <h4 class="name" id=".subtract"><span class="type-signature">(static) </span>subtract<span class="signature">(out, a, b)</span><span class="type-signature"> &rarr; {mat2}</span></h4>
    

    



<div class="description">
    Subtracts matrix b from matrix a
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line337">line 337</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".transpose"><span class="type-signature">(static) </span>transpose<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {mat2}</span></h4>
    

    



<div class="description">
    Transpose the values of a mat2
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the receiving matrix</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat2</span>


            
            </td>

            

            

            <td class="description last">the source matrix</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="mat2.js.html">mat2.js</a>, <a href="mat2.js.html#line111">line 111</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">mat2</span>


    </dd>
</dl>

    





        
    

    

    
</article>

</section>




</div>

<nav>
    <h2><a href="index.html">Home</a></h2><h3>Modules</h3><ul><li><a href="module-glMatrix.html">glMatrix</a></li><li><a href="module-mat2.html">mat2</a></li><li><a href="module-mat2d.html">mat2d</a></li><li><a href="module-mat3.html">mat3</a></li><li><a href="module-mat4.html">mat4</a></li><li><a href="module-quat.html">quat</a></li><li><a href="module-quat2.html">quat2</a></li><li><a href="module-vec2.html">vec2</a></li><li><a href="module-vec3.html">vec3</a></li><li><a href="module-vec4.html">vec4</a></li></ul>
</nav>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.5.5</a> on Fri Jul 13 2018 11:51:33 GMT+0200 (W. Europe Daylight Time)
</footer>

<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>
</body>
</html>