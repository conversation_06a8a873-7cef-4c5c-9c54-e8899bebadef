import { formatDistance } from "./pt-BR/_lib/formatDistance.js";
import { formatLong } from "./pt-BR/_lib/formatLong.js";
import { formatRelative } from "./pt-BR/_lib/formatRelative.js";
import { localize } from "./pt-BR/_lib/localize.js";
import { match } from "./pt-BR/_lib/match.js";

/**
 * @category Locales
 * @summary Portuguese locale (Brazil).
 * @language Portuguese
 * @iso-639-2 por
 * <AUTHOR> [@duailibe](https://github.com/duailibe)
 * <AUTHOR> [@yagocarballo](https://github.com/YagoCarballo)
 */
export const ptBR = {
  code: "pt-BR",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default ptBR;
