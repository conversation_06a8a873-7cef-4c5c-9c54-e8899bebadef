#!/bin/bash

# 主服务器部署脚本
# 使用方法: bash deploy_server.sh

echo "🚀 开始部署 Brush-Captcha 主服务器..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root用户运行此脚本"
    exit 1
fi

# 更新系统
echo "📦 更新系统包..."
apt update && apt upgrade -y

# 安装必要软件
echo "🔧 安装必要软件..."
apt install -y curl wget git docker.io docker-compose openssl

# 启动Docker服务
systemctl start docker
systemctl enable docker

# 克隆项目
echo "📥 克隆项目..."
if [ -d "brush-captcha" ]; then
    echo "⚠️  项目目录已存在，正在更新..."
    cd brush-captcha
    git pull
else
    git clone https://github.com/Brush-Bot/brush-captcha.git
    cd brush-captcha
fi

# 创建tmp目录
mkdir -p tmp

echo ""
echo "📝 请按照以下步骤配置文件："
echo ""
echo "1. 编辑代理文件："
echo "   nano tmp/proxies.txt"
echo "   格式示例："
echo "   username:password@ip:port"
echo "   ***************************:port"
echo ""
echo "2. 编辑API Key文件："
echo "   nano tmp/user_keys.txt"
echo "   每行一个Gemini API Key"
echo ""
echo "3. （可选）准备SSL证书："
echo "   将 .crt 和 .key 文件放入 tmp/ 目录"
echo "   或者运行以下命令生成自签证书："
echo "   openssl req -x509 -newkey rsa:2048 -nodes \\"
echo "     -keyout tmp/server.key -out tmp/server.crt -days 365 \\"
echo "     -subj \"/C=CN/ST=Beijing/L=Beijing/O=MyCompany/OU=Dev/CN=$(curl -s ifconfig.me)\""
echo ""

read -p "是否现在生成自签SSL证书？[y/N]: " generate_ssl
if [[ "$generate_ssl" =~ ^[Yy]$ ]]; then
    SERVER_IP=$(curl -s ifconfig.me)
    echo "🔐 生成SSL证书，服务器IP: $SERVER_IP"

    # 生成SSL证书（包含IP地址的SAN扩展）
    cat > tmp/ssl.conf << EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = CN
ST = Beijing
L = Beijing
O = MyCompany
OU = Dev
CN = $SERVER_IP

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
IP.1 = $SERVER_IP
DNS.1 = localhost
EOF

    openssl req -x509 -newkey rsa:2048 -nodes \
        -keyout tmp/server.key -out tmp/server.crt -days 365 \
        -config tmp/ssl.conf -extensions v3_req

    echo "✅ SSL证书已生成（包含IP: $SERVER_IP）"
    rm tmp/ssl.conf
fi

echo ""
echo "📋 配置文件准备完成后，请运行："
echo "bash install_server_and_frontend.sh"
echo ""
echo "🌐 部署完成后访问地址："
echo "管理后台: https://$(curl -s ifconfig.me):8080"
echo "默认账号: admin"
echo "默认密码: admin"
echo ""
echo "🔧 防火墙设置："
echo "ufw allow 8080"
echo "ufw enable"
