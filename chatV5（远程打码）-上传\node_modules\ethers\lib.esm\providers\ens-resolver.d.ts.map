{"version": 3, "file": "ens-resolver.d.ts", "sourceRoot": "", "sources": ["../../src.ts/providers/ens-resolver.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAeH,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAEnD,OAAO,KAAK,EAAE,gBAAgB,EAAE,sBAAsB,EAAE,MAAM,wBAAwB,CAAC;AAEvF,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AAgB9C;;GAEG;AACH,MAAM,MAAM,iBAAiB,GAAG,MAAM,GAAG,QAAQ,GAAG,SAAS,GAAG,KAAK,GAAG,MAAM,GAAG,MAAM,GACnF,QAAQ,GAAG,SAAS,GAAG,cAAc,GAAG,eAAe,GACvD,QAAQ,GAAG,OAAO,GAAG,UAAU,GAAG,SAAS,GAC3C,mBAAmB,GAAG,uBAAuB,GAAG,cAAc,GAAG,eAAe,GAChF,WAAW,GAAG,UAAU,GACxB,WAAW,GAAG,eAAe,GAAG,UAAU,GAAG,gBAAgB,CAAC;AAElE;;GAEG;AACH,MAAM,WAAW,aAAa;IAC1B;;OAEG;IACH,IAAI,EAAE,iBAAiB,CAAC;IAExB;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;CACjB;AAED;;;;;;;GAOG;AACH,MAAM,WAAW,YAAY;IACzB;;;OAGG;IACH,OAAO,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;IAE9B;;;;OAIG;IACH,GAAG,EAAE,IAAI,GAAG,MAAM,CAAC;CACtB;AAED;;GAEG;AACH,8BAAsB,uBAAwB,YAAW,sBAAsB;IAC3E;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAG,MAAM,CAAC;IAEvB;;OAEG;gBACS,IAAI,EAAE,MAAM;IAIxB,OAAO,CAAC,QAAQ,EAAE,QAAQ,GAAG,uBAAuB;IAIpD;;OAEG;IACH,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO;IAI3C;;OAEG;IACG,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAIvE;;OAEG;IACG,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC;CAG1E;AAID;;;;GAIG;AACH,qBAAa,4BAA6B,SAAQ,uBAAuB;IACrE;;OAEG;;CAIN;AAUD;;;GAGG;AACH,qBAAa,WAAW;;IACpB;;OAEG;IACH,QAAQ,EAAG,gBAAgB,CAAC;IAE5B;;OAEG;IACH,OAAO,EAAG,MAAM,CAAC;IAEjB;;OAEG;IACH,IAAI,EAAG,MAAM,CAAC;gBAOF,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM;IAerE;;OAEG;IACG,gBAAgB,IAAI,OAAO,CAAC,OAAO,CAAC;IA8D1C;;;OAGG;IACG,UAAU,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;IAoD3D;;;OAGG;IACG,OAAO,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;IAMlD;;OAEG;IACG,cAAc,IAAI,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;IA6B9C;;;;;;;OAOG;IACG,SAAS,IAAI,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;IAKzC;;;;;;;OAOG;IACG,UAAU,IAAI,OAAO,CAAC,YAAY,CAAC;WAgK5B,aAAa,CAAC,QAAQ,EAAE,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC;IAoC/D;;;OAGG;WACU,QAAQ,CAAC,QAAQ,EAAE,gBAAgB,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC;CA2B/F"}