"use strict";
exports.bg = void 0;
var _index = require("./bg/_lib/formatDistance.cjs");
var _index2 = require("./bg/_lib/formatLong.cjs");
var _index3 = require("./bg/_lib/formatRelative.cjs");
var _index4 = require("./bg/_lib/localize.cjs");
var _index5 = require("./bg/_lib/match.cjs");

/**
 * @category Locales
 * @summary Bulgarian locale.
 * @language Bulgarian
 * @iso-639-2 bul
 * <AUTHOR> [@arvigeus](https://github.com/arvigeus)
 * <AUTHOR> [@fintara](https://github.com/fintara)
 */
const bg = (exports.bg = {
  code: "bg",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
});
