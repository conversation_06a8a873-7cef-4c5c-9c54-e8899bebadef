const axios = require('axios');
const config = require('../../config');
const { log, logToFile } = require('../utils/simple-logger');
const retryManager = require('./retryManager');

/**
 * 远程打码服务客户端
 * 兼容capsolver API接口
 */
class RemoteCaptchaService {
  constructor() {
    this.baseUrl = config.REMOTE_CAPTCHA_URL || 'http://localhost:8000';
    this.clientKey = config.REMOTE_CAPTCHA_CLIENT_KEY || 'default_key';
    this.maxRetries = config.REMOTE_CAPTCHA_MAX_RETRIES || 5;
    this.retryDelay = config.REMOTE_CAPTCHA_RETRY_DELAY || 3000;
    this.taskTimeout = config.REMOTE_CAPTCHA_TASK_TIMEOUT || 300000; // 5分钟
  }

  /**
   * 创建Turnstile验证任务
   * @param {string} websiteURL 网站URL
   * @param {string} websiteKey 网站密钥
   * @param {string} action 动作（可选）
   * @returns {Promise<string>} 任务ID
   */
  async createTurnstileTask(websiteURL, websiteKey, action = null) {
    try {
      log(`[远程打码] 创建Turnstile任务: ${websiteURL}`, 'info');
      
      const payload = {
        clientKey: this.clientKey,
        task: {
          type: 'AntiTurnstileTaskProxyLess',
          websiteURL: websiteURL,
          websiteKey: websiteKey
        }
      };

      if (action) {
        payload.task.metadata = { action: action };
      }

      const response = await axios.post(`${this.baseUrl}/createTask`, payload, {
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.data.errorId === 0) {
        const taskId = response.data.taskId;
        log(`[远程打码] 任务创建成功: ${taskId}`, 'success');
        logToFile('远程打码任务创建成功', {
          taskId: taskId,
          websiteURL: websiteURL,
          websiteKey: websiteKey
        });
        return taskId;
      } else {
        throw new Error(`创建任务失败: ${response.data.msg || '未知错误'}`);
      }
    } catch (error) {
      log(`[远程打码] 创建任务失败: ${error.message}`, 'error');
      logToFile('远程打码任务创建失败', { 
        error: error.message,
        websiteURL: websiteURL,
        websiteKey: websiteKey
      });
      throw error;
    }
  }

  /**
   * 获取任务结果
   * @param {string} taskId 任务ID
   * @returns {Promise<object>} 任务结果
   */
  async getTaskResult(taskId) {
    try {
      const response = await axios.post(`${this.baseUrl}/getTaskResult`, {
        taskId: taskId
      }, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      return response.data;
    } catch (error) {
      log(`[远程打码] 获取任务结果失败: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * 等待任务完成并获取结果
   * @param {string} taskId 任务ID
   * @returns {Promise<string>} 验证码token
   */
  async waitForTaskResult(taskId) {
    const startTime = Date.now();
    let attempts = 0;
    const maxAttempts = Math.floor(this.taskTimeout / 5000); // 每5秒检查一次
    
    while (attempts < maxAttempts) {
      attempts++;
      
      try {
        const result = await this.getTaskResult(taskId);
        
        if (result.status === 'ready') {
          if (result.errorId === 0) {
            const token = result.solution?.token;
            if (token) {
              const elapsedTime = Math.round((Date.now() - startTime) / 1000);
              log(`[远程打码] 任务完成: ${taskId} (耗时${elapsedTime}秒)`, 'success');
              logToFile('远程打码任务完成', {
                taskId: taskId,
                elapsedTime: elapsedTime,
                tokenLength: token.length
              });
              return token;
            } else {
              throw new Error('任务完成但未返回有效token');
            }
          } else {
            throw new Error(`任务失败: ${result.solution?.error || '未知错误'}`);
          }
        } else if (result.status === 'processing') {
          log(`[远程打码] 任务处理中: ${taskId} (${attempts}/${maxAttempts})`, 'info');
          await new Promise(resolve => setTimeout(resolve, 5000));
        } else {
          throw new Error(`未知任务状态: ${result.status}`);
        }
      } catch (error) {
        if (attempts >= maxAttempts) {
          throw error;
        }
        log(`[远程打码] 获取结果失败，重试中: ${error.message}`, 'warning');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    throw new Error(`任务超时: ${taskId} (${this.taskTimeout/1000}秒)`);
  }

  /**
   * 获取Turnstile Token（主要接口）
   * @param {string} websiteURL 网站URL
   * @param {string} websiteKey 网站密钥
   * @param {string} action 动作（可选）
   * @returns {Promise<string>} 验证码token
   */
  async getTurnstileToken(websiteURL = null, websiteKey = null, action = null) {
    // 使用默认值如果没有提供参数
    const url = websiteURL || config.LOCAL_TURNSTILE_WEBSITE_URL || 'https://klokapp.ai/';
    const key = websiteKey || config.LOCAL_TURNSTILE_WEBSITE_KEY || '0x4AAAAAABdQypM3HkDQTuaO';

    return await retryManager.executeWithRetry(
      '远程打码获取Token',
      async () => {
        // 检查服务健康状态
        const isHealthy = await this.checkServiceHealth();
        if (!isHealthy) {
          throw new Error('远程打码服务不可用');
        }

        // 创建任务
        const taskId = await this.createTurnstileTask(url, key, action);

        // 等待结果
        const token = await this.waitForTaskResult(taskId);

        return token;
      },
      {
        maxRetries: this.maxRetries,
        baseDelay: this.retryDelay,
        maxDelay: 60000, // 最大延迟60秒
        totalTimeout: this.taskTimeout + 60000, // 总超时时间比任务超时多1分钟
        context: {
          websiteURL: url,
          websiteKey: key,
          action: action
        },
        onRetry: async (error, attempt, delay, context) => {
          // 重试回调：记录详细信息
          logToFile('远程打码重试', {
            attempt: attempt,
            error: error.message,
            delay: delay,
            context: context
          });

          // 每3次重试后检查服务状态
          if (attempt % 3 === 0) {
            try {
              await this.checkServiceHealth();
            } catch (healthError) {
              log(`[远程打码] 服务健康检查失败: ${healthError.message}`, 'warning');
            }
          }
        }
      }
    );
  }

  /**
   * 检查服务状态
   * @returns {Promise<boolean>} 服务是否可用
   */
  async checkServiceHealth() {
    try {
      const response = await axios.get(`${this.baseUrl}/nodes`, {
        timeout: 5000
      });
      
      const nodes = response.data;
      const activeNodes = nodes.filter(node => node.status !== '离线');
      
      log(`[远程打码] 服务状态检查: ${activeNodes.length}/${nodes.length} 节点在线`, 'info');
      return activeNodes.length > 0;
    } catch (error) {
      log(`[远程打码] 服务状态检查失败: ${error.message}`, 'error');
      return false;
    }
  }
}

// 导出单例
const remoteCaptchaService = new RemoteCaptchaService();
module.exports = remoteCaptchaService;
