#!/usr/bin/env node

/**
 * CSV查看工具
 * 快速查看CSV表格的当前状态和统计信息
 */

const fs = require('fs');

/**
 * 解析CSV文件
 */
function parseCSV(csvContent) {
  const lines = csvContent.split('\n').filter(line => line.trim());
  if (lines.length < 2) {
    throw new Error('CSV文件格式错误');
  }
  
  // 解析表头
  const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());
  
  // 解析数据行
  const data = [];
  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim());
    if (values.length >= 3) {
      const row = {};
      headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });
      data.push(row);
    }
  }
  
  return { headers, data };
}

/**
 * 获取今天的日期字符串
 */
function getTodayDateString() {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');
  return `${year}/${month}/${day}`;
}

/**
 * 分析CSV数据
 */
function analyzeCSV(headers, data) {
  const today = getTodayDateString();
  const dateColumns = headers.filter(h => h.match(/^\d{4}\/\d{2}\/\d{2}$/));
  const latestDate = dateColumns.sort().pop();
  
  let stats = {
    totalWallets: data.length,
    dateColumns: dateColumns.length,
    latestDate: latestDate,
    todayData: {
      success: 0,
      running: 0,
      error: 0,
      noChange: 0,
      noData: 0,
      totalPoints: 0
    }
  };
  
  // 分析最新日期的数据
  if (latestDate) {
    for (const row of data) {
      const value = row[latestDate] || '';
      
      if (!value || value === '无数据') {
        stats.todayData.noData++;
      } else if (value === '执行中') {
        stats.todayData.running++;
      } else if (value === '错误') {
        stats.todayData.error++;
      } else if (value.includes('无变化')) {
        stats.todayData.noChange++;
        const points = parseInt(value.replace(/[^\d]/g, '')) || 0;
        stats.todayData.totalPoints += points;
      } else if (!isNaN(parseInt(value))) {
        stats.todayData.success++;
        stats.todayData.totalPoints += parseInt(value);
      }
    }
  }
  
  return stats;
}

/**
 * 显示统计信息
 */
function displayStats(stats) {
  console.log(`
📊 CSV积分表格统计
==================

📋 基本信息:
   总钱包数: ${stats.totalWallets}
   记录天数: ${stats.dateColumns}
   最新日期: ${stats.latestDate || '无数据'}

📈 最新日期统计:
   ✅ 成功获取: ${stats.todayData.success}
   🔄 执行中: ${stats.todayData.running}
   ❌ 失败错误: ${stats.todayData.error}
   🔴 积分无变化: ${stats.todayData.noChange}
   📭 无数据: ${stats.todayData.noData}
   💰 总积分: ${stats.todayData.totalPoints}

📊 成功率: ${stats.totalWallets > 0 ? ((stats.todayData.success / stats.totalWallets) * 100).toFixed(1) : 0}%
🔴 需关注: ${stats.todayData.noChange + stats.todayData.error} 个钱包
`);
}

/**
 * 显示问题钱包
 */
function displayProblems(headers, data) {
  const dateColumns = headers.filter(h => h.match(/^\d{4}\/\d{2}\/\d{2}$/));
  const latestDate = dateColumns.sort().pop();
  
  if (!latestDate) {
    console.log('❌ 没有找到日期数据');
    return;
  }
  
  const problems = [];
  
  for (const row of data) {
    const value = row[latestDate] || '';
    const walletAddr = (row['地址'] || '').substring(0, 10) + '...';
    
    if (value === '错误') {
      problems.push({
        type: '❌ 错误',
        wallet: walletAddr,
        status: '任务执行失败'
      });
    } else if (value.includes('无变化')) {
      problems.push({
        type: '🔴 无变化',
        wallet: walletAddr,
        status: `积分: ${value}`
      });
    } else if (value === '执行中') {
      problems.push({
        type: '🔄 执行中',
        wallet: walletAddr,
        status: '任务运行中'
      });
    }
  }
  
  if (problems.length > 0) {
    console.log(`🚨 需要关注的钱包 (${problems.length}个):`);
    console.log('==================');
    problems.forEach((problem, index) => {
      console.log(`${index + 1}. ${problem.type} ${problem.wallet} - ${problem.status}`);
    });
  } else {
    console.log('✅ 所有钱包状态正常！');
  }
}

/**
 * 显示积分排行
 */
function displayRanking(headers, data) {
  const dateColumns = headers.filter(h => h.match(/^\d{4}\/\d{2}\/\d{2}$/));
  const latestDate = dateColumns.sort().pop();
  
  if (!latestDate) {
    return;
  }
  
  const ranking = [];
  
  for (const row of data) {
    const value = row[latestDate] || '';
    const walletAddr = (row['地址'] || '').substring(0, 10) + '...';
    
    let points = 0;
    if (value.includes('无变化')) {
      points = parseInt(value.replace(/[^\d]/g, '')) || 0;
    } else if (!isNaN(parseInt(value))) {
      points = parseInt(value);
    }
    
    if (points > 0) {
      ranking.push({
        wallet: walletAddr,
        points: points
      });
    }
  }
  
  ranking.sort((a, b) => b.points - a.points);
  
  if (ranking.length > 0) {
    console.log(`\n🏆 积分排行榜 (前10名):`);
    console.log('==================');
    ranking.slice(0, 10).forEach((item, index) => {
      const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
      console.log(`${medal} ${item.wallet} - ${item.points} 积分`);
    });
  }
}

/**
 * 主函数
 */
function main() {
  const csvFile = 'klok-scores.csv';
  
  try {
    // 检查文件是否存在
    if (!fs.existsSync(csvFile)) {
      console.log(`❌ 未找到CSV文件: ${csvFile}`);
      console.log('💡 请先运行主程序生成CSV文件:');
      console.log('   npm run start-csv');
      process.exit(1);
    }
    
    // 读取和解析CSV文件
    console.log('📊 正在分析CSV文件...');
    const csvContent = fs.readFileSync(csvFile, 'utf8');
    const { headers, data } = parseCSV(csvContent);
    
    // 分析数据
    const stats = analyzeCSV(headers, data);
    
    // 显示统计信息
    displayStats(stats);
    
    // 显示问题钱包
    displayProblems(headers, data);
    
    // 显示积分排行
    displayRanking(headers, data);
    
    // 显示文件信息
    const fileStats = fs.statSync(csvFile);
    const fileSize = (fileStats.size / 1024).toFixed(2);
    const lastModified = fileStats.mtime.toLocaleString();
    
    console.log(`\n📁 文件信息:`);
    console.log(`   文件大小: ${fileSize} KB`);
    console.log(`   最后更新: ${lastModified}`);
    console.log(`   文件路径: ${csvFile}`);
    
  } catch (error) {
    console.error(`❌ 分析失败: ${error.message}`);
    process.exit(1);
  }
}

// 显示帮助信息
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
📊 CSV查看工具

用法: node csv-viewer.js

功能:
- 显示CSV表格的统计信息
- 列出需要关注的问题钱包
- 显示积分排行榜
- 显示文件基本信息

文件: klok-scores.csv

💡 提示: 
- 确保CSV文件存在
- 程序会自动分析最新日期的数据
- 重点关注"无变化"和"错误"状态的钱包
`);
  process.exit(0);
}

// 运行主函数
main();
