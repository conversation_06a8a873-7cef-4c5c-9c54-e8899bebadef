(()=>{var $;function C(G){return C=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},C(G)}function x(G,J){var H=Object.keys(G);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(G);J&&(X=X.filter(function(Y){return Object.getOwnPropertyDescriptor(G,Y).enumerable})),H.push.apply(H,X)}return H}function Q(G){for(var J=1;J<arguments.length;J++){var H=arguments[J]!=null?arguments[J]:{};J%2?x(Object(H),!0).forEach(function(X){N(G,X,H[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(H)):x(Object(H)).forEach(function(X){Object.defineProperty(G,X,Object.getOwnPropertyDescriptor(H,X))})}return G}function N(G,J,H){if(J=z(J),J in G)Object.defineProperty(G,J,{value:H,enumerable:!0,configurable:!0,writable:!0});else G[J]=H;return G}function z(G){var J=W(G,"string");return C(J)=="symbol"?J:String(J)}function W(G,J){if(C(G)!="object"||!G)return G;var H=G[Symbol.toPrimitive];if(H!==void 0){var X=H.call(G,J||"default");if(C(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(G)}var D=Object.defineProperty,JG=function G(J,H){for(var X in H)D(J,X,{get:H[X],enumerable:!0,configurable:!0,set:function Y(Z){return H[X]=function(){return Z}}})},E={lessThanXSeconds:{one:"1\u79D2\u672A\u6E80",other:"{{count}}\u79D2\u672A\u6E80",oneWithSuffix:"\u7D041\u79D2",otherWithSuffix:"\u7D04{{count}}\u79D2"},xSeconds:{one:"1\u79D2",other:"{{count}}\u79D2"},halfAMinute:"30\u79D2",lessThanXMinutes:{one:"1\u5206\u672A\u6E80",other:"{{count}}\u5206\u672A\u6E80",oneWithSuffix:"\u7D041\u5206",otherWithSuffix:"\u7D04{{count}}\u5206"},xMinutes:{one:"1\u5206",other:"{{count}}\u5206"},aboutXHours:{one:"\u7D041\u6642\u9593",other:"\u7D04{{count}}\u6642\u9593"},xHours:{one:"1\u6642\u9593",other:"{{count}}\u6642\u9593"},xDays:{one:"1\u65E5",other:"{{count}}\u65E5"},aboutXWeeks:{one:"\u7D041\u9031\u9593",other:"\u7D04{{count}}\u9031\u9593"},xWeeks:{one:"1\u9031\u9593",other:"{{count}}\u9031\u9593"},aboutXMonths:{one:"\u7D041\u304B\u6708",other:"\u7D04{{count}}\u304B\u6708"},xMonths:{one:"1\u304B\u6708",other:"{{count}}\u304B\u6708"},aboutXYears:{one:"\u7D041\u5E74",other:"\u7D04{{count}}\u5E74"},xYears:{one:"1\u5E74",other:"{{count}}\u5E74"},overXYears:{one:"1\u5E74\u4EE5\u4E0A",other:"{{count}}\u5E74\u4EE5\u4E0A"},almostXYears:{one:"1\u5E74\u8FD1\u304F",other:"{{count}}\u5E74\u8FD1\u304F"}},S=function G(J,H,X){X=X||{};var Y,Z=E[J];if(typeof Z==="string")Y=Z;else if(H===1)if(X.addSuffix&&Z.oneWithSuffix)Y=Z.oneWithSuffix;else Y=Z.one;else if(X.addSuffix&&Z.otherWithSuffix)Y=Z.otherWithSuffix.replace("{{count}}",String(H));else Y=Z.other.replace("{{count}}",String(H));if(X.addSuffix)if(X.comparison&&X.comparison>0)return Y+"\u5F8C";else return Y+"\u524D";return Y};function A(G){return function(){var J=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=J.width?String(J.width):G.defaultWidth,X=G.formats[H]||G.formats[G.defaultWidth];return X}}var M={full:"y\u5E74M\u6708d\u65E5EEEE",long:"y\u5E74M\u6708d\u65E5",medium:"y/MM/dd",short:"y/MM/dd"},R={full:"H\u6642mm\u5206ss\u79D2 zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},L={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},V={date:A({formats:M,defaultWidth:"full"}),time:A({formats:R,defaultWidth:"full"}),dateTime:A({formats:L,defaultWidth:"full"})},w={lastWeek:"\u5148\u9031\u306Eeeee\u306Ep",yesterday:"\u6628\u65E5\u306Ep",today:"\u4ECA\u65E5\u306Ep",tomorrow:"\u660E\u65E5\u306Ep",nextWeek:"\u7FCC\u9031\u306Eeeee\u306Ep",other:"P"},j=function G(J,H,X,Y){return w[J]};function I(G){return function(J,H){var X=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",Y;if(X==="formatting"&&G.formattingValues){var Z=G.defaultFormattingWidth||G.defaultWidth,B=H!==null&&H!==void 0&&H.width?String(H.width):Z;Y=G.formattingValues[B]||G.formattingValues[Z]}else{var T=G.defaultWidth,q=H!==null&&H!==void 0&&H.width?String(H.width):G.defaultWidth;Y=G.values[q]||G.values[T]}var U=G.argumentCallback?G.argumentCallback(J):J;return Y[U]}}var _={narrow:["BC","AC"],abbreviated:["\u7D00\u5143\u524D","\u897F\u66A6"],wide:["\u7D00\u5143\u524D","\u897F\u66A6"]},f={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["\u7B2C1\u56DB\u534A\u671F","\u7B2C2\u56DB\u534A\u671F","\u7B2C3\u56DB\u534A\u671F","\u7B2C4\u56DB\u534A\u671F"]},v={narrow:["1","2","3","4","5","6","7","8","9","10","11","12"],abbreviated:["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708"],wide:["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708"]},F={narrow:["\u65E5","\u6708","\u706B","\u6C34","\u6728","\u91D1","\u571F"],short:["\u65E5","\u6708","\u706B","\u6C34","\u6728","\u91D1","\u571F"],abbreviated:["\u65E5","\u6708","\u706B","\u6C34","\u6728","\u91D1","\u571F"],wide:["\u65E5\u66DC\u65E5","\u6708\u66DC\u65E5","\u706B\u66DC\u65E5","\u6C34\u66DC\u65E5","\u6728\u66DC\u65E5","\u91D1\u66DC\u65E5","\u571F\u66DC\u65E5"]},P={narrow:{am:"\u5348\u524D",pm:"\u5348\u5F8C",midnight:"\u6DF1\u591C",noon:"\u6B63\u5348",morning:"\u671D",afternoon:"\u5348\u5F8C",evening:"\u591C",night:"\u6DF1\u591C"},abbreviated:{am:"\u5348\u524D",pm:"\u5348\u5F8C",midnight:"\u6DF1\u591C",noon:"\u6B63\u5348",morning:"\u671D",afternoon:"\u5348\u5F8C",evening:"\u591C",night:"\u6DF1\u591C"},wide:{am:"\u5348\u524D",pm:"\u5348\u5F8C",midnight:"\u6DF1\u591C",noon:"\u6B63\u5348",morning:"\u671D",afternoon:"\u5348\u5F8C",evening:"\u591C",night:"\u6DF1\u591C"}},k={narrow:{am:"\u5348\u524D",pm:"\u5348\u5F8C",midnight:"\u6DF1\u591C",noon:"\u6B63\u5348",morning:"\u671D",afternoon:"\u5348\u5F8C",evening:"\u591C",night:"\u6DF1\u591C"},abbreviated:{am:"\u5348\u524D",pm:"\u5348\u5F8C",midnight:"\u6DF1\u591C",noon:"\u6B63\u5348",morning:"\u671D",afternoon:"\u5348\u5F8C",evening:"\u591C",night:"\u6DF1\u591C"},wide:{am:"\u5348\u524D",pm:"\u5348\u5F8C",midnight:"\u6DF1\u591C",noon:"\u6B63\u5348",morning:"\u671D",afternoon:"\u5348\u5F8C",evening:"\u591C",night:"\u6DF1\u591C"}},h=function G(J,H){var X=Number(J),Y=String(H===null||H===void 0?void 0:H.unit);switch(Y){case"year":return"".concat(X,"\u5E74");case"quarter":return"\u7B2C".concat(X,"\u56DB\u534A\u671F");case"month":return"".concat(X,"\u6708");case"week":return"\u7B2C".concat(X,"\u9031");case"date":return"".concat(X,"\u65E5");case"hour":return"".concat(X,"\u6642");case"minute":return"".concat(X,"\u5206");case"second":return"".concat(X,"\u79D2");default:return"".concat(X)}},b={ordinalNumber:h,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function G(J){return Number(J)-1}}),month:I({values:v,defaultWidth:"wide"}),day:I({values:F,defaultWidth:"wide"}),dayPeriod:I({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function y(G){return function(J){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.match(G.matchPattern);if(!X)return null;var Y=X[0],Z=J.match(G.parsePattern);if(!Z)return null;var B=G.valueCallback?G.valueCallback(Z[0]):Z[0];B=H.valueCallback?H.valueCallback(B):B;var T=J.slice(Y.length);return{value:B,rest:T}}}function O(G){return function(J){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=H.width,Y=X&&G.matchPatterns[X]||G.matchPatterns[G.defaultMatchWidth],Z=J.match(Y);if(!Z)return null;var B=Z[0],T=X&&G.parsePatterns[X]||G.parsePatterns[G.defaultParseWidth],q=Array.isArray(T)?c(T,function(K){return K.test(B)}):m(T,function(K){return K.test(B)}),U;U=G.valueCallback?G.valueCallback(q):q,U=H.valueCallback?H.valueCallback(U):U;var HG=J.slice(B.length);return{value:U,rest:HG}}}function m(G,J){for(var H in G)if(Object.prototype.hasOwnProperty.call(G,H)&&J(G[H]))return H;return}function c(G,J){for(var H=0;H<G.length;H++)if(J(G[H]))return H;return}var d=/^第?\d+(年|四半期|月|週|日|時|分|秒)?/i,g=/\d+/i,p={narrow:/^(B\.?C\.?|A\.?D\.?)/i,abbreviated:/^(紀元[前後]|西暦)/i,wide:/^(紀元[前後]|西暦)/i},l={narrow:[/^B/i,/^A/i],any:[/^(紀元前)/i,/^(西暦|紀元後)/i]},u={narrow:/^[1234]/i,abbreviated:/^Q[1234]/i,wide:/^第[1234一二三四１２３４]四半期/i},i={any:[/(1|一|１)/i,/(2|二|２)/i,/(3|三|３)/i,/(4|四|４)/i]},n={narrow:/^([123456789]|1[012])/,abbreviated:/^([123456789]|1[012])月/i,wide:/^([123456789]|1[012])月/i},s={any:[/^1\D/,/^2/,/^3/,/^4/,/^5/,/^6/,/^7/,/^8/,/^9/,/^10/,/^11/,/^12/]},o={narrow:/^[日月火水木金土]/,short:/^[日月火水木金土]/,abbreviated:/^[日月火水木金土]/,wide:/^[日月火水木金土]曜日/},r={any:[/^日/,/^月/,/^火/,/^水/,/^木/,/^金/,/^土/]},a={any:/^(AM|PM|午前|午後|正午|深夜|真夜中|夜|朝)/i},e={any:{am:/^(A|午前)/i,pm:/^(P|午後)/i,midnight:/^深夜|真夜中/i,noon:/^正午/i,morning:/^朝/i,afternoon:/^午後/i,evening:/^夜/i,night:/^深夜/i}},t={ordinalNumber:y({matchPattern:d,parsePattern:g,valueCallback:function G(J){return parseInt(J,10)}}),era:O({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),quarter:O({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function G(J){return J+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},GG={code:"ja",formatDistance:S,formatLong:V,formatRelative:j,localize:b,match:t,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{ja:GG})})})();

//# debugId=6015A11700A6C96564756E2164756E21
