from datetime import datetime
from common.logger import get_logger, emoji
logger = get_logger("worker_manager")
worker_pool = {}

def register_worker(worker_id, ws, data):
    worker_pool[worker_id] = {
        "id": worker_id,
        "ws": ws,
        "task_types": data["task_types"],
        "max_concurrency": data["max_concurrency"],
        "current_tasks": 0,
        "pending_tasks": 0,
        "connected_at": datetime.utcnow(),
        "last_ping": datetime.utcnow(),
        "ip": ws.client.host,
    }

def update_worker_status(worker_id, status):
    if worker_id in worker_pool:
        # 只更新心跳时间和pending_tasks，不覆盖server端维护的current_tasks
        # current_tasks由server端在任务分发和完成时维护，确保准确性
        worker_pool[worker_id]["pending_tasks"] = status.get("pending_tasks", 0)
        worker_pool[worker_id]["last_ping"] = datetime.utcnow()

        # 记录client端上报的任务数，用于调试对比
        client_reported_tasks = status.get("current_tasks", 0)
        server_tracked_tasks = worker_pool[worker_id]["current_tasks"]

        # 如果差异过大，记录警告日志
        if abs(client_reported_tasks - server_tracked_tasks) > 1:
            logger.warning(emoji("SYNC",
                f"Worker {worker_id} 任务计数不同步 - "
                f"Client上报: {client_reported_tasks}, Server追踪: {server_tracked_tasks}"
            ))

def get_worker_status(info):
    if info["current_tasks"] + info["pending_tasks"] == 0:
        status = "空闲"
    elif info["current_tasks"] >= info["max_concurrency"]:
        status = "忙碌"
    else:
        status = "工作中"
    return {
        **info,
        "status": status,
        "uptime": str(datetime.utcnow() - info["connected_at"]),
    }
