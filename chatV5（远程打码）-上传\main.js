/**
 * KlokAI自动化工具 - 主进程
 * 基于进程隔离架构，每个钱包在独立子进程中运行
 */
const fs = require("fs");
const path = require("path");
const { fork } = require("child_process");
const { auth } = require("./src/api");
const {
  log,
  logToFile,
  checkLogSize
} = require("./src/utils/simple-logger");
const questionReader = require("./src/services/questionReader");
const walletProxyManager = require("./src/services/walletProxyManager");
const scheduler = require("./src/services/scheduler");
const autoAuth = require("./src/services/autoAuth");
const retryManager = require("./src/services/retryManager");
const config = require("./config");
const { ethers } = require("ethers");

// 全局状态
let isRunning = false;
let isPaused = false;
let allTokens = [];
let walletAddressMap = new Map(); // 钱包地址到令牌的映射
let validTokens = []; // 有效的token列表

// CSV追踪功能
class SimpleCSVTracker {
  constructor() {
    this.csvFile = 'klok-scores.csv';
    this.scoreData = this.loadScoreData();
    this.walletList = new Map();

    // 定期保存CSV
    setInterval(() => {
      this.saveCSV();
    }, 30000); // 每30秒保存一次
  }

  getTodayDateString() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
  }

  loadScoreData() {
    const dataFile = 'score-data.json';
    if (fs.existsSync(dataFile)) {
      try {
        const data = fs.readFileSync(dataFile, 'utf8');
        return JSON.parse(data);
      } catch (error) {
        console.log(`[CSV追踪] 读取数据失败: ${error.message}`);
        return {};
      }
    }
    return {};
  }

  initializeWallet(walletAddress, privateKey) {
    this.walletList.set(walletAddress, {
      privateKey: privateKey,
      address: walletAddress
    });

    if (!this.scoreData[walletAddress]) {
      this.scoreData[walletAddress] = {
        privateKey: privateKey,
        scores: {}
      };
    }

    console.log(`[CSV追踪] 初始化钱包: ${walletAddress.substring(0, 8)}...`);
  }

  recordWalletScore(walletAddress, scoreInfo) {
    const today = this.getTodayDateString();

    if (!this.scoreData[walletAddress]) {
      this.scoreData[walletAddress] = {
        privateKey: 'unknown',
        scores: {}
      };
    }

    this.scoreData[walletAddress].scores[today] = scoreInfo;
    console.log(`[CSV追踪] 记录积分: ${walletAddress.substring(0, 8)}... - ${scoreInfo.success ? scoreInfo.totalPoints + '分' : '失败'}`);
  }

  recordTaskStart(walletAddress) {
    this.recordWalletScore(walletAddress, {
      success: false,
      status: 'running',
      totalPoints: 0,
      error: '执行中...'
    });
  }

  recordTaskCompletion(walletAddress, result) {
    if (result && result.success) {
      this.recordWalletScore(walletAddress, {
        success: true,
        totalPoints: result.afterPoints || 0,
        pointsEarned: result.pointsEarned || 0
      });
    } else {
      this.recordWalletScore(walletAddress, {
        success: false,
        totalPoints: 0,
        error: result ? result.error || '任务失败' : '未知错误'
      });
    }
  }

  recordTaskError(walletAddress, error) {
    this.recordWalletScore(walletAddress, {
      success: false,
      totalPoints: 0,
      error: error
    });
  }

  getAllDates() {
    const allDates = new Set();
    for (const walletAddress of Object.keys(this.scoreData)) {
      const scores = this.scoreData[walletAddress].scores;
      Object.keys(scores).forEach(date => allDates.add(date));
    }
    return Array.from(allDates).sort();
  }

  hasScoreChanged(walletAddress, date) {
    const scores = this.scoreData[walletAddress]?.scores || {};
    const todayScore = scores[date];

    if (!todayScore || !todayScore.success) {
      return false;
    }

    const dates = Object.keys(scores).sort();
    const todayIndex = dates.indexOf(date);

    if (todayIndex <= 0) {
      return true;
    }

    const yesterdayDate = dates[todayIndex - 1];
    const yesterdayScore = scores[yesterdayDate];

    if (!yesterdayScore || !yesterdayScore.success) {
      return true;
    }

    return todayScore.totalPoints !== yesterdayScore.totalPoints;
  }

  generateCSV() {
    const walletAddresses = Array.from(this.walletList.keys());
    const allDates = this.getAllDates();

    let csv = '\uFEFF';

    csv += '序号,私钥,地址';
    for (const date of allDates) {
      const formattedDate = `${date.substring(0, 4)}/${date.substring(4, 6)}/${date.substring(6, 8)}`;
      csv += `,${formattedDate}`;
    }
    csv += '\n';

    walletAddresses.forEach((walletAddress, index) => {
      const walletInfo = this.walletList.get(walletAddress);
      const privateKey = walletInfo?.privateKey || 'unknown';

      csv += `${index + 1},"${privateKey}","${walletAddress}"`;

      for (const date of allDates) {
        const scoreInfo = this.scoreData[walletAddress]?.scores[date];

        if (!scoreInfo) {
          csv += ',无数据';
        } else if (scoreInfo.status === 'running') {
          csv += ',执行中';
        } else if (!scoreInfo.success) {
          csv += ',错误';
        } else {
          const points = scoreInfo.totalPoints || 0;
          const hasChanged = this.hasScoreChanged(walletAddress, date);
          if (!hasChanged && date !== allDates[0]) {
            csv += `,${points}(无变化)`;
          } else {
            csv += `,${points}`;
          }
        }
      }

      csv += '\n';
    });

    return csv;
  }

  saveCSV() {
    try {
      const csv = this.generateCSV();
      fs.writeFileSync(this.csvFile, csv, 'utf8');

      const walletCount = this.walletList.size;
      const dateCount = this.getAllDates().length;
      console.log(`[CSV追踪] CSV已更新: ${this.csvFile} (${walletCount}个钱包, ${dateCount}天数据)`);
    } catch (error) {
      console.error(`[CSV追踪] 保存CSV失败: ${error.message}`);
    }
  }

  getStats() {
    const today = this.getTodayDateString();
    const walletAddresses = Array.from(this.walletList.keys());

    let totalSuccess = 0;
    let totalErrors = 0;
    let totalRunning = 0;
    let totalPoints = 0;
    let noChangeCount = 0;

    for (const walletAddress of walletAddresses) {
      const todayScore = this.scoreData[walletAddress]?.scores[today];

      if (!todayScore) {
        continue;
      }

      if (todayScore.status === 'running') {
        totalRunning++;
      } else if (todayScore.success) {
        totalSuccess++;
        totalPoints += todayScore.totalPoints || 0;

        if (!this.hasScoreChanged(walletAddress, today)) {
          noChangeCount++;
        }
      } else {
        totalErrors++;
      }
    }

    return {
      totalWallets: walletAddresses.length,
      totalSuccess,
      totalErrors,
      totalRunning,
      totalPoints,
      noChangeCount,
      successRate: walletAddresses.length > 0 ? ((totalSuccess / walletAddresses.length) * 100).toFixed(1) : 0
    };
  }
}

let csvTracker = null;
let checkWindowInterval = null; // 存储定时器引用

// 检查可执行钱包的间隔（毫秒）
const CHECK_INTERVAL = 5000; // 5秒，更频繁检查以支持高并发
// 最大并发执行的钱包数量
const MAX_CONCURRENT_WALLETS = config.THREADS || 50;

/**
 * 读取私钥文件
 * @param {string} filePath 文件路径
 * @returns {Array<string>} 私钥数组
 */
function readPrivateKeysFromFile(filePath) {
  const absolutePath = path.resolve(filePath);
  if (!fs.existsSync(absolutePath)) {
    console.error(`文件 ${absolutePath} 不存在。`);
    return [];
  }
  const data = fs.readFileSync(absolutePath, "utf8");
  return data.split(/\r?\n/).filter(line => line.trim() !== "");
}

/**
 * 读取代理文件
 * @param {string} filePath 文件路径
 * @returns {Array<string>} 代理数组
 */
function readProxiesFromFile(filePath) {
  const absolutePath = path.resolve(filePath);
  if (!fs.existsSync(absolutePath)) {
    console.error(`文件 ${absolutePath} 不存在。`);
    return [];
  }
  const data = fs.readFileSync(absolutePath, "utf8");
  return data.split(/\r?\n/).filter(line => line.trim() !== "");
}

/**
 * 初始化钱包代理绑定
 */
async function initializeWalletProxyBindings() {
  if (!config.ENABLE_WALLET_PROXY_BINDING) {
    log("钱包代理绑定功能未启用", "info");
    return;
  }
  
  try {
    const privateKeys = readPrivateKeysFromFile("priv.txt");
    const proxies = readProxiesFromFile("proxies.txt");
    
    if (privateKeys.length === 0) {
      log("未找到钱包私钥，无法初始化钱包代理绑定", "warning");
      return;
    }
    
    if (proxies.length === 0) {
      log("未找到代理列表，无法初始化钱包代理绑定", "warning");
      return;
    }
    
    // 解析钱包地址
    const walletAddresses = [];
    for (const key of privateKeys) {
      try {
        const wallet = new ethers.Wallet(key.trim());
        walletAddresses.push(wallet.address);
      } catch (error) {
        log(`解析钱包地址失败: ${error.message}`, "error");
      }
    }
    
    log(`找到 ${walletAddresses.length} 个钱包地址和 ${proxies.length} 个代理`, "info");
    
    // 调用钱包代理管理器进行绑定
    walletProxyManager.autoBindWallets(walletAddresses, proxies);
    
    log("钱包代理绑定初始化完成", "success");
  } catch (error) {
    log(`初始化钱包代理绑定失败: ${error.message}`, "error");
    logToFile("初始化钱包代理绑定失败", { error: error.message });
  }
}

/**
 * 清空会话令牌文件
 */
function clearSessionTokenFile() {
  const tokenPath = path.join(process.cwd(), "session-token.key");
  fs.writeFileSync(tokenPath, "", "utf8");
  console.log("[信息] session-token.key 已清空。");
}

/**
 * 显示启动信息
 */
function showStartupInfo() {
  console.log(`
📊 Klok自动聊天脚本 - CSV积分追踪版
=====================================

功能特点:
✅ 自动执行聊天任务
📊 实时记录积分到CSV表格
📋 按日期列显示积分变化
🔴 积分无变化时标记提醒
📈 支持Excel/WPS直接打开

CSV文件: klok-scores.csv
格式: 序号 | 私钥 | 地址 | 20250614 | 20250615 | ...

=====================================
`);
}

/**
 * 初始化自动化服务
 */
async function initAutomation() {
  try {
    // 显示启动信息
    showStartupInfo();

    log("正在初始化服务...", "info");
    logToFile("初始化自动化服务");

    // 初始化CSV积分追踪器
    csvTracker = new SimpleCSVTracker();
    log("CSV积分追踪器已初始化", "success");

    // 读取所有会话令牌（只是为了显示，不影响启动）
    allTokens = auth.readAllSessionTokensFromFile();
    log(`从文件中加载了 ${allTokens.length} 个会话令牌（仅供参考）`, "info");

    // 初始化钱包状态管理（不需要验证token）
    await initWalletTokenMap();

    // 初始化钱包队列
    await initWalletQueue();

    // 初始化调度器
    scheduler.init();

    log("已准备好开始自动化", "success");

    // 显示CSV使用提示
    setTimeout(() => {
      console.log(`
💡 CSV使用提示:
- 程序运行时会自动生成 klok-scores.csv
- CSV表格每30秒自动更新
- 可以用Excel、WPS等软件打开查看
- 积分无变化的单元格会标记"(无变化)"
- 新增钱包会自动添加到表格中

📊 CSV表格格式:
序号 | 私钥 | 地址 | 20250614 | 20250615 | 20250616 | ...
  1  | 0x... | 0x... |   125    |   150    |   175    | ...
  2  | 0x... | 0x... |   100    | 100(无变化) |   120    | ...

🔴 状态说明:
- "执行中" = 任务正在运行
- "错误" = 任务执行失败
- "无数据" = 该日期无记录
- "数字(无变化)" = 积分与前一天相同，需要关注
`);
    }, 3000);

    return true;
  } catch (error) {
    log(`初始化错误: ${error.message}`, "error");
    logToFile("初始化错误", { error: error.message, stack: error.stack });
    return false;
  }
}

/**
 * 初始化钱包状态管理
 * 不再验证token，而是从私钥文件中加载钱包地址
 */
async function initWalletTokenMap() {
  walletAddressMap.clear();
  validTokens = [];
  
  // 读取私钥文件中的钱包地址
  const privateKeys = readPrivateKeysFromFile("priv.txt");
  const actualWalletAddresses = [];
  
  // 从私钥生成钱包地址
  for (const privateKey of privateKeys) {
    try {
      const wallet = new ethers.Wallet(privateKey);
      actualWalletAddresses.push(wallet.address);
    } catch (error) {
      log(`无效的私钥: ${privateKey.substring(0, 6)}...`, "error");
    }
  }
  
  log(`从私钥文件中读取到 ${actualWalletAddresses.length} 个有效钱包地址`, "info");
  
  // 清除状态文件中不存在的钱包
  const currentWallets = Object.keys(scheduler.getWalletStates());
  for (const existingWallet of currentWallets) {
    if (!actualWalletAddresses.includes(existingWallet)) {
      scheduler.removeWallet(existingWallet);
      log(`移除不存在的钱包: ${existingWallet.substring(0, 8)}...`, "info");
    }
  }
  
  // 为每个钱包初始化状态
  for (const walletAddress of actualWalletAddresses) {
    // 初始化钱包状态
    scheduler.initializeWallet(walletAddress);
    // 添加到钱包映射
    walletAddressMap.set(walletAddress, null);

    // 初始化CSV积分追踪
    if (csvTracker) {
      const privateKey = privateKeys.find(key => {
        try {
          const wallet = new ethers.Wallet(key);
          return wallet.address === walletAddress;
        } catch {
          return false;
        }
      });
      csvTracker.initializeWallet(walletAddress, privateKey || 'unknown');
    }
  }
  
  // 从文件中加载所有token，但不验证
  allTokens = auth.readAllSessionTokensFromFile();
  log(`从文件中加载了 ${allTokens.length} 个会话令牌（不验证）`, "info");
  
  log(`已初始化 ${actualWalletAddresses.length} 个钱包状态`, "info");
  return actualWalletAddresses.length > 0;
}

/**
 * 初始化钱包队列
 */
async function initWalletQueue() {
  try {
    // 如果启用了钱包代理绑定，自动为未绑定的钱包分配代理
    if (config.ENABLE_WALLET_PROXY_BINDING) {
      const walletAddresses = Array.from(walletAddressMap.keys());
      
      // 读取代理列表
      const proxies = readProxiesFromFile("proxies.txt");

      // 自动绑定钱包和代理
      if (walletAddresses.length > 0 && proxies.length > 0) {
        walletProxyManager.autoBindWallets(walletAddresses, proxies);
      }
    }

    // 显示钱包状态统计
    scheduler.displayWalletStats();

    return true;
  } catch (error) {
    log(`初始化钱包队列失败: ${error.message}`, "error");
    logToFile("初始化钱包队列失败", { error: error.message, stack: error.stack });
    return false;
  }
}

/**
 * 启动自动化
 */
function startAutomation() {
  if (isRunning) {
    log("自动化已在运行中", "warning");
    return;
  }
  
  isRunning = true;
  isPaused = false;
  
  log("开始自动化", "success");
  logToFile("开始自动化");
  
  // 立即执行一次执行窗口检查，显示钱包统计信息
  checkExecutionWindow();
  
  // 清除之前的定时器（如果存在）
  if (checkWindowInterval) {
    clearInterval(checkWindowInterval);
  }

  // 设置定时器，每分钟执行一次执行窗口检查
  checkWindowInterval = setInterval(async () => {
    await checkExecutionWindow();
  }, 60 * 1000); // 60秒 * 1000毫秒
  
  // 启动主自动化循环
  log("正在启动主自动化循环...", "info");
  runAutomation();
}

/**
 * 暂停自动化
 */
function pauseAutomation() {
  if (!isRunning) {
    log("自动化未运行", "warning");
    return;
  }
  
  isPaused = true;
  log("自动化已暂停", "info");
  logToFile("自动化已暂停");
}

/**
 * 恢复自动化
 */
function resumeAutomation() {
  if (!isRunning) {
    startAutomation();
    return;
  }
  
  if (!isPaused) {
    log("自动化未暂停", "warning");
    return;
  }
  
  isPaused = false;
  log("自动化已恢复", "success");
  logToFile("自动化已恢复");
}

/**
 * 获取运行状态
 */
function getRunningState() {
  return isRunning && !isPaused;
}

/**
 * 异步准备并启动钱包，避免阻塞主循环
 */
async function prepareAndStartWallet(walletAddress) {
  // 从代理绑定文件中获取钱包绑定信息
  let binding = walletProxyManager.getWalletBinding(walletAddress);

  if (!binding) {
    log(`钱包 ${walletAddress.substring(0, 8)}... 未找到绑定信息，尝试自动绑定`, "warning");

    try {
      // 使用重试机制进行代理绑定（每次重试使用不同代理）
      let usedProxies = []; // 记录已使用的代理

      binding = await executeWithRetry(
        walletAddress,
        "自动绑定代理",
        (() => {
          // 返回一个闭包函数，保持usedProxies的状态
          return async () => {
          // 读取代理列表
          const proxies = readProxiesFromFile("proxies.txt");
          if (!proxies || proxies.length === 0) {
            throw new Error("没有可用的代理");
          }

          // 过滤掉已使用的代理
          let availableProxies = proxies.filter(proxy => !usedProxies.includes(proxy));

          if (availableProxies.length === 0) {
            // 如果所有代理都试过了，重置已使用列表
            usedProxies = [];
            availableProxies = [...proxies]; // 重新创建数组
          }

          // 随机选择一个未使用的代理
          const randomIndex = Math.floor(Math.random() * availableProxies.length);
          const proxyUrl = availableProxies[randomIndex];

          // 记录已使用的代理
          usedProxies.push(proxyUrl);

          // 绑定代理
          const bindResult = walletProxyManager.bindWalletToProxy(walletAddress, proxyUrl);
          if (!bindResult) {
            throw new Error(`绑定代理${proxyUrl}失败`);
          }

          // 重新获取绑定信息验证
          const newBinding = walletProxyManager.getWalletBinding(walletAddress);
          if (!newBinding || !newBinding.proxyUrl) {
            throw new Error("绑定后无法获取有效的绑定信息");
          }

          log(`成功为钱包 ${walletAddress.substring(0, 8)}... 绑定代理: ${proxyUrl}`, "success");
          return newBinding;
          };
        })(), // 立即执行闭包
        5, // 最多重试5次
        5000, // 基础延迟5秒
        30, // 失败后30分钟重试
        60000 // 总超时时间60秒
      );
    } catch (error) {
      // executeWithRetry已经设置了重试时间和状态
      log(`钱包 ${walletAddress.substring(0, 8)}... 代理绑定失败，已设置重试时间`, "warning");
      throw error; // 重新抛出错误，让调用者处理
    }
  }

  // 获取代理
  const proxyUrl = binding.proxyUrl;
  if (!proxyUrl) {
    log(`钱包 ${walletAddress.substring(0, 8)}... 绑定信息中未找到代理URL，尝试重新绑定`, "warning");

    try {
      // 使用重试机制重新绑定代理（每次重试使用不同代理）
      let usedProxies = []; // 记录已使用的代理

      const newBinding = await executeWithRetry(
        walletAddress,
        "重新绑定代理",
        (() => {
          // 返回一个闭包函数，保持usedProxies的状态
          return async () => {
          // 清除无效绑定
          walletProxyManager.unbindWallet(walletAddress);

          // 重新从代理池选择
          const proxies = readProxiesFromFile("proxies.txt");
          if (!proxies || proxies.length === 0) {
            throw new Error("没有可用的代理进行重新绑定");
          }

          // 过滤掉已使用的代理
          let availableProxies = proxies.filter(proxy => !usedProxies.includes(proxy));

          if (availableProxies.length === 0) {
            // 如果所有代理都试过了，重置已使用列表
            usedProxies = [];
            availableProxies = [...proxies]; // 重新创建数组
          }

          // 随机选择一个未使用的代理
          const randomIndex = Math.floor(Math.random() * availableProxies.length);
          const newProxyUrl = availableProxies[randomIndex];

          // 记录已使用的代理
          usedProxies.push(newProxyUrl);

          const bindResult = walletProxyManager.bindWalletToProxy(walletAddress, newProxyUrl);
          if (!bindResult) {
            throw new Error(`绑定代理${newProxyUrl}失败`);
          }

          const newBinding = walletProxyManager.getWalletBinding(walletAddress);
          if (!newBinding || !newBinding.proxyUrl) {
            throw new Error("绑定后无法获取有效的绑定信息");
          }

          log(`成功为钱包 ${walletAddress.substring(0, 8)}... 重新绑定代理: ${newProxyUrl}`, "success");
          return newBinding;
          };
        })(), // 立即执行闭包
        5, // 最多重试5次
        5000, // 基础延迟5秒
        30, // 失败后30分钟重试
        60000 // 总超时时间60秒
      );

      // 更新binding变量，继续执行
      binding = newBinding;
    } catch (error) {
      // executeWithRetry已经设置了重试时间和状态
      log(`钱包 ${walletAddress.substring(0, 8)}... 代理重新绑定失败，已设置重试时间`, "warning");
      throw error; // 重新抛出错误，让调用者处理
    }
  }

  // 检查钱包是否需要重新登录
  const walletState = scheduler.getWalletState(walletAddress);
  const needRelogin = walletState && walletState.needRelogin;

  // 获取有效token
  let token;

  if (needRelogin) {
    log(`钱包 ${walletAddress.substring(0, 8)}... 需要重新登录，尝试自动认证`, "info");

    try {
      // 使用重试机制进行认证
      token = await executeWithRetry(
        walletAddress,
        "自动认证",
        async () => {
          const authToken = await autoAuth.authenticate(walletAddress);
          if (!authToken) {
            throw new Error("认证返回空token");
          }
          return authToken;
        },
        5, // 最多重试5次
        5000, // 基础延迟5秒
        30, // 失败后30分钟重试
        90000 // 总超时时间90秒（认证可能需要更长时间）
      );

      log(`钱包 ${walletAddress.substring(0, 8)}... 自动认证成功，获取到新token`, "success");
      // 更新钱包状态，清除重新登录标记
      scheduler.updateWalletState(walletAddress, { needRelogin: false });
    } catch (error) {
      // executeWithRetry已经设置了重试时间和状态
      log(`钱包 ${walletAddress.substring(0, 8)}... 自动认证失败，已设置重试时间`, "warning");
      throw error; // 重新抛出错误，让调用者处理
    }
  } else {
    // 获取并验证现有token（带重试机制）
    log(`获取钱包 ${walletAddress.substring(0, 8)}... 的有效token`, "info");

    try {
      // 使用重试机制获取token
      token = await executeWithRetry(
        walletAddress,
        "获取有效token",
        async () => {
          const validToken = await autoAuth.getValidToken(walletAddress);
          if (!validToken) {
            throw new Error("获取到空token");
          }
          return validToken;
        },
        5, // 最多重试5次
        5000, // 基础延迟5秒
        15, // 失败后15分钟重试
        30000 // 总超时时间30秒（获取token应该很快）
      );

      log(`钱包 ${walletAddress.substring(0, 8)}... 获取到有效token`, "success");
    } catch (error) {
      // executeWithRetry已经设置了重试时间和状态
      // 标记需要重新登录
      scheduler.updateWalletState(walletAddress, { needRelogin: true });
      log(`钱包 ${walletAddress.substring(0, 8)}... 获取token失败，已设置重试时间`, "warning");
      throw error; // 重新抛出错误，让调用者处理
    }
  }

  // 记录任务开始
  if (csvTracker) {
    csvTracker.recordTaskStart(walletAddress);
  }

  // 启动新的钱包工作进程
  log(`启动钱包 ${walletAddress.substring(0, 8)}... 的工作进程`, "info");
  const worker = fork('./wallet-worker.js', [walletAddress, token]);

  // 声明心跳检测变量
  let heartbeatInterval;

  // 注册进程消息处理
  worker.on('message', (message) => {
    if (message.type === 'completed') {
      log(`钱包 ${message.walletAddress.substring(0, 8)}... 任务完成`, "success");

      // 记录任务完成（内部已包含设置下次执行时间）
      scheduler.recordTaskCompletion(message.walletAddress);

      // 更新钱包状态（不重复设置lastExecutionTime，recordTaskCompletion已经设置了）
      scheduler.updateWalletState(message.walletAddress, {
        lastResult: message.result,
        needRelogin: false // 清除重新登录标记
      });

      // 记录到CSV积分追踪器
      if (csvTracker) {
        csvTracker.recordTaskCompletion(message.walletAddress, message.result);
      }

      // 注意：不需要再次调用setNextExecutionTime，recordTaskCompletion已经处理了
    } else if (message.type === 'error') {
      log(`钱包 ${message.walletAddress.substring(0, 8)}... 任务失败: ${message.error}`, "error");

      // 检查是否是token无效错误
      const isTokenError = message.error.includes('token') ||
                         message.error.includes('unauthorized') ||
                         message.error.includes('authentication') ||
                         message.error.includes('授权') ||
                         message.error.includes('认证');

      // 检查是否是流式响应超时（这是正常情况）
      const isStreamTimeout = message.error.includes('流式响应等待超时') ||
                             message.error.includes('stream timeout') ||
                             message.error.includes('streaming timeout');

      // 更新钱包状态
      scheduler.updateWalletState(message.walletAddress, {
        lastError: message.error,
        lastErrorTime: new Date().toISOString(),
        needRelogin: isTokenError // 如果是token错误，标记需要重新登录
      });

      // 记录到CSV积分追踪器
      if (csvTracker) {
        if (isStreamTimeout) {
          // 流式响应超时视为成功完成，记录为成功
          csvTracker.recordTaskCompletion(message.walletAddress, {
            success: true,
            totalPoints: 0, // 无法获取积分，设为0
            note: '流式响应超时但任务正常完成'
          });
        } else {
          csvTracker.recordTaskError(message.walletAddress, message.error);
        }
      }

      if (isStreamTimeout) {
        // 流式响应超时是正常情况，立即设置下次执行时间（正常24小时调度）
        log(`钱包 ${message.walletAddress.substring(0, 8)}... 流式响应超时，这是正常情况，按正常调度执行`, "info");
        scheduler.recordTaskCompletion(message.walletAddress); // 视为任务完成
      } else {
        // 其他错误，设置下次执行时间（失败后仍然设置下次执行时间）
        scheduler.setNextExecutionTime(message.walletAddress);
      }

      // 如果是token错误，清除绑定的token
      if (isTokenError) {
        walletProxyManager.updateWalletToken(message.walletAddress, null);
        log(`已清除钱包 ${message.walletAddress.substring(0, 8)}... 的无效token`, "warning");
      }
    } else if (message.type === 'token_invalid') {
      // 处理token无效的情况
      log(`钱包 ${message.walletAddress.substring(0, 8)}... 的token无效`, "warning");

      // 清除绑定的token
      walletProxyManager.updateWalletToken(message.walletAddress, null);

      // 更新钱包状态
      scheduler.updateWalletState(message.walletAddress, {
        needRelogin: true,
        lastError: "Token无效",
        lastErrorTime: new Date().toISOString()
      });

      // 记录到CSV积分追踪器
      if (csvTracker) {
        csvTracker.recordTaskError(message.walletAddress, "Token无效");
      }

      // 设置下次执行时间（使用正常的24小时调度，而不是短期重试）
      scheduler.setNextExecutionTime(message.walletAddress);
    }
  });

  // 注册进程错误处理
  worker.on('error', (error) => {
    log(`钱包 ${walletAddress.substring(0, 8)}... 进程发生错误: ${error.message}`, "error");

    // 更新钱包状态
    scheduler.updateWalletState(walletAddress, {
      lastError: `进程错误: ${error.message}`,
      lastErrorTime: new Date().toISOString()
    });

    // 设置下次执行时间
    scheduler.setNextExecutionTime(walletAddress);

    // 记录到CSV追踪器
    if (csvTracker) {
      csvTracker.recordTaskError(walletAddress, `进程错误: ${error.message}`);
    }

    // 移除运行进程记录
    scheduler.removeRunningProcess(walletAddress);
  });

  // 注册进程退出处理
  worker.on('exit', (code) => {
    // 清理心跳检测
    clearInterval(heartbeatInterval);

    // 移除运行进程记录
    scheduler.removeRunningProcess(walletAddress);
    log(`钱包 ${walletAddress.substring(0, 8)}... 进程已退出，退出码: ${code}`, code === 0 ? "info" : "warning");

    // 如果进程异常退出（非0退出码），设置下次执行时间
    if (code !== 0) {
      log(`钱包 ${walletAddress.substring(0, 8)}... 异常退出，退出码: ${code}`, "warning");

      // 检查是否是网络相关的异常退出
      const isNetworkExit = code === 1; // 通常网络错误导致的退出码

      if (isNetworkExit) {
        // 网络异常退出，设置较短的重试时间（5-10分钟）
        const shortRetryMs = (5 + Math.random() * 5) * 60 * 1000; // 5-10分钟
        const nextTime = new Date(new Date().getTime() + shortRetryMs);

        scheduler.updateWalletState(walletAddress, {
          nextExecutionTime: nextTime.toISOString(),
          lastError: `网络异常退出，退出码: ${code}，${Math.round(shortRetryMs/60000)}分钟后重试`,
          lastErrorTime: new Date().toISOString()
        });

        log(`钱包 ${walletAddress.substring(0, 8)}... 网络异常退出，${Math.round(shortRetryMs/60000)}分钟后重试`, "warning");
      } else {
        // 其他异常退出，使用正常的24小时调度
        scheduler.updateWalletState(walletAddress, {
          lastError: `进程异常退出，退出码: ${code}`,
          lastErrorTime: new Date().toISOString()
        });
        scheduler.setNextExecutionTime(walletAddress);
      }

      // 记录到CSV追踪器
      if (csvTracker) {
        csvTracker.recordTaskError(walletAddress, `进程异常退出，退出码: ${code}`);
      }
    }
  });

  // 更新运行中的进程记录（替换临时标记）
  scheduler.removeRunningProcess(walletAddress);
  scheduler.addRunningProcess(walletAddress, worker);

  // 设置心跳检测 - 恶劣环境下每1分钟检查一次进程是否还活着
  heartbeatInterval = setInterval(() => {
    if (worker && !worker.killed) {
      try {
        worker.send({ type: 'ping' });

        // 监听pong响应
        const pongHandler = (message) => {
          if (message.type === 'pong' && message.walletAddress === walletAddress) {
            clearTimeout(pongTimeout);
            worker.removeListener('message', pongHandler);
          }
        };

        // 设置10秒超时，恶劣环境下给更多时间响应
        const pongTimeout = setTimeout(() => {
          log(`钱包 ${walletAddress.substring(0, 8)}... 心跳检测超时，强制终止进程`, "error");

          // 清理监听器
          worker.removeListener('message', pongHandler);

          try {
            // 先尝试优雅终止
            worker.kill('SIGTERM');

            // 2秒后强制终止
            setTimeout(() => {
              try {
                if (!worker.killed) {
                  worker.kill('SIGKILL');
                  log(`钱包 ${walletAddress.substring(0, 8)}... 进程已强制终止`, "warning");
                }
              } catch (forceKillError) {
                log(`强制终止进程失败: ${forceKillError.message}`, "error");
              }
            }, 2000);
          } catch (killError) {
            log(`终止进程失败: ${killError.message}`, "error");
          }

          // 更新钱包状态
          scheduler.updateWalletState(walletAddress, {
            lastError: "心跳检测超时，进程被强制终止",
            lastErrorTime: new Date().toISOString()
          });
          scheduler.setNextExecutionTime(walletAddress);

          // 记录到CSV追踪器
          if (csvTracker) {
            csvTracker.recordTaskError(walletAddress, "心跳检测超时");
          }

          clearInterval(heartbeatInterval);
        }, 10000); // 10秒超时

        worker.on('message', pongHandler);

      } catch (pingError) {
        log(`发送心跳失败: ${pingError.message}`, "warning");
        clearInterval(heartbeatInterval);
      }
    } else {
      clearInterval(heartbeatInterval);
    }
  }, 60000); // 恶劣环境下1分钟间隔
}

/**
 * 通用重试机制（使用新的重试管理器）
 * @param {string} walletAddress 钱包地址
 * @param {string} operation 操作名称
 * @param {Function} operationFunc 操作函数
 * @param {number} maxRetries 最大重试次数
 * @param {number} baseDelay 基础延迟时间(毫秒)
 * @param {number} fallbackMinutes 最终失败后的等待时间(分钟)
 * @returns {Promise<any>} 操作结果
 */
async function executeWithRetry(walletAddress, operation, operationFunc, maxRetries = 5, baseDelay = 5000, fallbackMinutes = 30, totalTimeoutMs = 120000) {
  try {
    return await retryManager.executeWithRetry(
      `${operation}(${walletAddress.substring(0, 8)}...)`,
      operationFunc,
      {
        maxRetries: maxRetries,
        baseDelay: baseDelay,
        totalTimeout: totalTimeoutMs,
        context: {
          walletAddress: walletAddress,
          operation: operation
        },
        onRetry: async (error, attempt, delay, context) => {
          // 重试回调：记录钱包相关信息
          log(`钱包 ${context.walletAddress.substring(0, 8)}... ${context.operation}重试 ${attempt}/${maxRetries}`, "warning");
          logToFile(`钱包重试 - ${context.operation}`, {
            walletAddress: context.walletAddress,
            attempt: attempt,
            error: error.message,
            delay: delay
          });
        }
      }
    );
  } catch (error) {
    // 使用重试管理器失败后，设置fallback等待时间
    const retryTime = new Date(new Date().getTime() + fallbackMinutes * 60 * 1000);
    scheduler.updateWalletState(walletAddress, {
      lastError: `${operation}失败: ${error.message}`,
      lastErrorTime: new Date().toISOString(),
      nextExecutionTime: retryTime.toISOString()
    });

    log(`钱包 ${walletAddress.substring(0, 8)}... 已设置${fallbackMinutes}分钟后重试，原因: ${operation}失败`, "warning");
    throw error;
  }
}

/**
 * 主自动化循环 - 使用简化版本避免阻塞
 */
async function runAutomation() {
  return runAutomationSimple();
}

/**
 * 原始主自动化循环 - 备用
 */
async function runAutomationOriginal() {
  // 检查是否暂停
  if (isPaused) {
    log("自动化已暂停，等待恢复...", "warning");
    setTimeout(runAutomation, CHECK_INTERVAL);
    return;
  }

  try {
    // 获取可执行钱包
    const executableWallets = scheduler.getExecutableWallets();

    // 检查当前运行的子进程数量
    const runningCount = scheduler.getRunningProcessCount();

    // 显示当前状态
    log(`当前状态: 可执行钱包 ${executableWallets.length} 个, 运行中 ${runningCount} 个`, "info");

    // 如果有可执行钱包且未达到最大并发数
    if (executableWallets.length > 0 && runningCount < MAX_CONCURRENT_WALLETS) {
      // 计算可以启动的钱包数量
      const availableSlots = MAX_CONCURRENT_WALLETS - runningCount;
      const walletsToStart = Math.min(executableWallets.length, availableSlots);

      log(`准备批量启动 ${walletsToStart} 个钱包 (可执行: ${executableWallets.length}, 运行中: ${runningCount}/${MAX_CONCURRENT_WALLETS})`, "info");

      // 批量启动多个钱包
      for (let i = 0; i < walletsToStart; i++) {
        const wallet = executableWallets[i];
        const walletAddress = wallet.address;

        log(`[${i+1}/${walletsToStart}] 准备执行钱包 ${walletAddress.substring(0, 8)}...`, "info");

        // 立即标记钱包为运行中，防止重复启动
        scheduler.addRunningProcess(walletAddress, { pid: 'preparing' });

        // 异步处理钱包准备工作，避免阻塞主循环
        setImmediate(async () => {
          try {
            await prepareAndStartWallet(walletAddress);
          } catch (error) {
            log(`钱包 ${walletAddress.substring(0, 8)}... 准备失败: ${error.message}`, "error");
            // 清理临时运行标记
            scheduler.removeRunningProcess(walletAddress);
            // 设置重试时间
            scheduler.updateWalletState(walletAddress, {
              lastError: `钱包准备失败: ${error.message}`,
              lastErrorTime: new Date().toISOString()
            });
            scheduler.setNextExecutionTime(walletAddress);
          }
        });
      }
    } else {
      // 没有钱包执行时的调试信息
      if (executableWallets.length === 0) {
        // 每10次循环显示一次详细信息
        if (Math.random() < 0.1) {
          log(`没有可执行的钱包`, "warning");
          const stats = scheduler.getWalletStats();
          log(`调度状态: 总钱包${stats.totalWallets}个, 等待执行${stats.pendingExecution}个, 运行中${runningCount}个`, "info");
        }
      } else if (runningCount >= MAX_CONCURRENT_WALLETS) {
        // 每10次循环显示一次并发限制信息
        if (Math.random() < 0.1) {
          log(`已达到最大并发数限制 (${runningCount}/${MAX_CONCURRENT_WALLETS})，等待进程完成`, "warning");
        }
      } else {
        // 这种情况不应该发生，如果发生了说明有bug
        log(`异常状态: 有${executableWallets.length}个可执行钱包，运行中${runningCount}个，但没有启动新钱包`, "error");

        // 显示前3个可执行钱包的详细信息
        for (let i = 0; i < Math.min(3, executableWallets.length); i++) {
          const wallet = executableWallets[i];
          const state = scheduler.getWalletState(wallet.address);
          const isRunning = scheduler.isWalletRunning(wallet.address);
          const nextExecTime = new Date(wallet.nextExecutionTime);
          const now = new Date();
          const timeDiff = now.getTime() - nextExecTime.getTime();

          log(`  可执行钱包${i+1}: ${wallet.address.substring(0, 8)}...`, "info");
          log(`    下次执行时间: ${wallet.nextExecutionTime}`, "info");
          log(`    当前时间: ${now.toISOString()}`, "info");
          log(`    时间差: ${Math.round(timeDiff/1000)}秒 (${timeDiff > 0 ? '已过期' : '未到时间'})`, "info");
          log(`    是否运行中: ${isRunning}`, "info");
          log(`    最后错误: ${state?.lastError || '无'}`, "info");
          log(`    需要重新登录: ${state?.needRelogin || false}`, "info");
        }
      }
    }

    // 定期显示钱包状态统计
    if (Math.random() < 0.05) { // 约5%的几率显示统计
      scheduler.displayWalletStats();
    }
  } catch (error) {
    log(`自动化循环错误: ${error.message}`, "error");
    logToFile("自动化循环错误", { error: error.message, stack: error.stack });
  }

  // M4 mini内存监控和垃圾回收 - 更积极的内存管理
  if (Math.random() < 0.02) { // 2%的几率进行内存检查，M4 mini需要更频繁
    const memUsage = process.memoryUsage();
    const memMB = Math.round(memUsage.heapUsed / 1024 / 1024);
    const rssMB = Math.round(memUsage.rss / 1024 / 1024);

    // M4 mini内存限制更严格
    if (memMB > 300 || rssMB > 800) { // 降低内存阈值
      log(`M4 mini内存使用较高: 堆内存${memMB}MB, 系统内存${rssMB}MB，触发垃圾回收`, "warning");
      if (global.gc) {
        global.gc();
        const newMemUsage = process.memoryUsage();
        const newMemMB = Math.round(newMemUsage.heapUsed / 1024 / 1024);
        const newRssMB = Math.round(newMemUsage.rss / 1024 / 1024);
        log(`垃圾回收后内存: 堆内存${newMemMB}MB, 系统内存${newRssMB}MB`, "info");

        // 如果内存仍然很高，记录警告
        if (newMemMB > 400 || newRssMB > 1000) {
          log(`⚠️ M4 mini内存使用仍然很高，可能需要重启程序`, "warning");
          logToFile("M4 mini内存使用警告", {
            heapMB: newMemMB,
            rssMB: newRssMB,
            runningProcesses: scheduler.getRunningProcessCount()
          });
        }
      }
    } else if (Math.random() < 0.1) { // 10%的几率显示内存信息
      log(`M4 mini内存使用: 堆内存${memMB}MB, 系统内存${rssMB}MB`, "info");
    }
  }

  // 继续循环
  setTimeout(runAutomation, CHECK_INTERVAL);
}

/**
 * 主自动化循环 - 简化版本，避免阻塞
 */
async function runAutomationSimple() {
  // 检查是否暂停
  if (isPaused) {
    log("自动化已暂停，等待恢复...", "warning");
    setTimeout(runAutomationSimple, CHECK_INTERVAL);
    return;
  }

  try {
    // 获取可执行钱包
    const executableWallets = scheduler.getExecutableWallets();

    // 检查当前运行的子进程数量
    const runningCount = scheduler.getRunningProcessCount();

    // 显示当前状态
    log(`当前状态: 可执行钱包 ${executableWallets.length} 个, 运行中 ${runningCount} 个`, "info");

    // 如果有可执行钱包且未达到最大并发数
    if (executableWallets.length > 0 && runningCount < MAX_CONCURRENT_WALLETS) {
      // 计算可以启动的钱包数量
      const availableSlots = MAX_CONCURRENT_WALLETS - runningCount;
      const walletsToStart = Math.min(executableWallets.length, availableSlots);

      log(`准备批量启动 ${walletsToStart} 个钱包 (可执行: ${executableWallets.length}, 运行中: ${runningCount}/${MAX_CONCURRENT_WALLETS})`, "info");

      // 批量启动多个钱包
      for (let i = 0; i < walletsToStart; i++) {
        const wallet = executableWallets[i];
        const walletAddress = wallet.address;

        log(`[${i+1}/${walletsToStart}] 准备执行钱包 ${walletAddress.substring(0, 8)}...`, "info");

        // 立即标记钱包为运行中，防止重复启动
        scheduler.addRunningProcess(walletAddress, { pid: 'preparing' });

        // 异步处理钱包准备工作，避免阻塞主循环
        setImmediate(async () => {
          try {
            await prepareAndStartWallet(walletAddress);
          } catch (error) {
            log(`钱包 ${walletAddress.substring(0, 8)}... 准备失败: ${error.message}`, "error");
            // 清理临时运行标记
            scheduler.removeRunningProcess(walletAddress);
            // 设置重试时间
            scheduler.updateWalletState(walletAddress, {
              lastError: `钱包准备失败: ${error.message}`,
              lastErrorTime: new Date().toISOString()
            });
            scheduler.setNextExecutionTime(walletAddress);
          }
        });
      }
    } else {
      // 没有钱包执行时的调试信息
      if (executableWallets.length === 0) {
        // 每10次循环显示一次详细信息
        if (Math.random() < 0.1) {
          log(`没有可执行的钱包`, "warning");
          const stats = scheduler.getWalletStats();
          log(`调度状态: 总钱包${stats.totalWallets}个, 等待执行${stats.pendingExecution}个, 运行中${runningCount}个`, "info");
        }
      } else if (runningCount >= MAX_CONCURRENT_WALLETS) {
        // 每10次循环显示一次并发限制信息
        if (Math.random() < 0.1) {
          log(`已达到最大并发数限制 (${runningCount}/${MAX_CONCURRENT_WALLETS})，等待进程完成`, "warning");
        }
      } else {
        // 这种情况不应该发生，如果发生了说明有bug
        log(`异常状态: 有${executableWallets.length}个可执行钱包，运行中${runningCount}个，但没有启动新钱包`, "error");

        // 显示前3个可执行钱包的详细信息
        for (let i = 0; i < Math.min(3, executableWallets.length); i++) {
          const wallet = executableWallets[i];
          const state = scheduler.getWalletState(wallet.address);
          const isRunning = scheduler.isWalletRunning(wallet.address);
          const nextExecTime = new Date(wallet.nextExecutionTime);
          const now = new Date();
          const timeDiff = now.getTime() - nextExecTime.getTime();

          log(`  可执行钱包${i+1}: ${wallet.address.substring(0, 8)}...`, "info");
          log(`    下次执行时间: ${wallet.nextExecutionTime}`, "info");
          log(`    当前时间: ${now.toISOString()}`, "info");
          log(`    时间差: ${Math.round(timeDiff/1000)}秒 (${timeDiff > 0 ? '已过期' : '未到时间'})`, "info");
          log(`    是否运行中: ${isRunning}`, "info");
          log(`    最后错误: ${state?.lastError || '无'}`, "info");
          log(`    需要重新登录: ${state?.needRelogin || false}`, "info");
        }
      }
    }

    // 定期显示钱包状态统计
    if (Math.random() < 0.05) { // 约5%的几率显示统计
      scheduler.displayWalletStats();
    }
  } catch (error) {
    log(`自动化循环错误: ${error.message}`, "error");
    logToFile("自动化循环错误", { error: error.message, stack: error.stack });
  }
  
  // M4 mini内存监控和垃圾回收 - 更积极的内存管理
  if (Math.random() < 0.02) { // 2%的几率进行内存检查，M4 mini需要更频繁
    const memUsage = process.memoryUsage();
    const memMB = Math.round(memUsage.heapUsed / 1024 / 1024);
    const rssMB = Math.round(memUsage.rss / 1024 / 1024);

    // M4 mini内存限制更严格
    if (memMB > 300 || rssMB > 800) { // 降低内存阈值
      log(`M4 mini内存使用较高: 堆内存${memMB}MB, 系统内存${rssMB}MB，触发垃圾回收`, "warning");
      if (global.gc) {
        global.gc();
        const newMemUsage = process.memoryUsage();
        const newMemMB = Math.round(newMemUsage.heapUsed / 1024 / 1024);
        const newRssMB = Math.round(newMemUsage.rss / 1024 / 1024);
        log(`垃圾回收后内存: 堆内存${newMemMB}MB, 系统内存${newRssMB}MB`, "info");

        // 如果内存仍然很高，记录警告
        if (newMemMB > 400 || newRssMB > 1000) {
          log(`⚠️ M4 mini内存使用仍然很高，可能需要重启程序`, "warning");
          logToFile("M4 mini内存使用警告", {
            heapMB: newMemMB,
            rssMB: newRssMB,
            runningProcesses: scheduler.getRunningProcessCount()
          });
        }
      }
    } else if (Math.random() < 0.1) { // 10%的几率显示内存信息
      log(`M4 mini内存使用: 堆内存${memMB}MB, 系统内存${rssMB}MB`, "info");
    }
  }

  // 继续循环
  setTimeout(runAutomation, CHECK_INTERVAL);
}

/**
 * 检查执行窗口
 */
async function checkExecutionWindow() {
  try {
    const stats = scheduler.getWalletStats();
    const executableWallets = scheduler.getExecutableWallets();
    
    log("========== 执行窗口检查 ==========", "info");
    log(`总钱包数: ${stats.totalWallets}`, "info");
    log(`当前可执行钱包: ${executableWallets.length}`, "info");
    log(`等待执行钱包: ${stats.pendingExecution}`, "info");
    log(`过去24小时完成的钱包数量: ${stats.completedInLast24Hours}`, "success"); // 使用绿色字体
    log(`已完成任务总数: ${stats.totalCompletedTasks}`, "info");

    // 显示CSV积分统计
    if (csvTracker) {
      const scoreStats = csvTracker.getStats();
      log(`📊 CSV积分统计:`, "info");
      log(`   今日成功: ${scoreStats.totalSuccess}/${scoreStats.totalWallets} (${scoreStats.successRate}%)`, "success");
      log(`   运行中: ${scoreStats.totalRunning}`, "warning");
      log(`   失败: ${scoreStats.totalErrors}`, "error");
      log(`   无变化: ${scoreStats.noChangeCount}`, "warning");
      log(`   总积分: ${scoreStats.totalPoints}`, "info");
      log(`📊 CSV表格: klok-scores.csv`, "info");
    }

    // 显示下一个将要执行的钱包
    if (stats.nextExecutionIn !== null) {
      const hours = Math.floor(stats.nextExecutionIn / (60 * 60 * 1000));
      const minutes = Math.floor((stats.nextExecutionIn % (60 * 60 * 1000)) / (60 * 1000));

      const { nextWallet, waitTime } = scheduler.getNextWalletWaitTime();
      if (nextWallet) {
        const nextExecTime = new Date(new Date().getTime() + waitTime).toLocaleString();
        log(`下一个钱包将在 ${hours}小时${minutes}分钟 后执行`, "info");
        log(`预计执行时间: ${nextExecTime}`, "info");
      }
    }

    log("====================================", "info");
    
    return {
      executableWallets: executableWallets.length,
      pendingExecution: stats.pendingExecution,
      nextExecutionIn: stats.nextExecutionIn
    };
  } catch (error) {
    log(`检查执行窗口失败: ${error.message}`, "error");
    return null;
  }
}

/**
 * 自动启动函数
 */
async function autoStartAutomation() {
  // 检查代理绑定文件
  const bindings = walletProxyManager.getAllBindings();
  const walletAddresses = Object.keys(bindings);
  
  if (walletAddresses.length === 0) {
    log("未找到钱包代理绑定关系。请先初始化钱包代理绑定。", "error");
    return false;
  }

  log("正在自动启动...", "info");
  logToFile("自动启动");
  
  // 初始化自动化
  const initResult = await initAutomation();
  if (!initResult) {
    log("初始化失败，无法启动自动化", "error");
    return false;
  }
  
  // 启动自动化
  startAutomation();
  return true;
}

/**
 * 停止自动化并清理资源
 */
function stopAutomation() {
  isRunning = false;
  isPaused = false;

  // 清理定时器
  if (checkWindowInterval) {
    clearInterval(checkWindowInterval);
    checkWindowInterval = null;
    log("已清理执行窗口检查定时器", "info");
  }

  // 终止所有运行中的进程
  const runningProcesses = scheduler.getAllRunningProcesses();
  for (const [walletAddress, worker] of runningProcesses) {
    try {
      worker.kill('SIGTERM');
      log(`已终止钱包 ${walletAddress.substring(0, 8)}... 的进程`, "info");
    } catch (error) {
      log(`终止进程失败: ${error.message}`, "warning");
    }
  }

  log("自动化已停止", "success");
}

/**
 * 处理进程退出信号
 */
function setupExitHandler() {
  try {
    // 优雅退出函数
    const gracefulExit = (signal) => {
      log(`接收到${signal}信号，正在优雅退出...`, "info");

      // 停止自动化
      stopAutomation();

      // 清理所有运行中的子进程
      const runningProcesses = scheduler.getAllRunningProcesses();
      if (runningProcesses.size > 0) {
        log(`正在清理 ${runningProcesses.size} 个运行中的子进程...`, "info");

        for (const [walletAddress, worker] of runningProcesses) {
          try {
            if (worker && worker.pid && !worker.killed) {
              worker.kill('SIGTERM');
              log(`已终止钱包 ${walletAddress.substring(0, 8)}... 的进程`, "info");
            }
          } catch (error) {
            log(`终止进程失败: ${error.message}`, "warning");
          }
        }

        // 等待2秒让子进程优雅退出
        setTimeout(() => {
          // 强制终止剩余进程
          for (const [walletAddress, worker] of runningProcesses) {
            try {
              if (worker && worker.pid && !worker.killed) {
                worker.kill('SIGKILL');
              }
            } catch (error) {
              // 忽略错误
            }
          }

          logToFile(`程序退出（${signal}信号）`);
          process.exit(0);
        }, 2000);
      } else {
        logToFile(`程序退出（${signal}信号）`);
        process.exit(0);
      }
    };

    // 处理各种退出信号
    process.on('SIGINT', () => gracefulExit('SIGINT'));   // Ctrl+C
    process.on('SIGTERM', () => gracefulExit('SIGTERM')); // 系统终止
    process.on('SIGHUP', () => gracefulExit('SIGHUP'));   // 终端断开

    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
      log(`未捕获的异常: ${error.message}`, "error");
      logToFile("未捕获的异常", { error: error.message, stack: error.stack });
      gracefulExit('uncaughtException');
    });

    // 处理未处理的Promise拒绝
    process.on('unhandledRejection', (reason, promise) => {
      log(`未处理的Promise拒绝: ${reason}`, "error");
      logToFile("未处理的Promise拒绝", { reason: reason.toString(), promise: promise.toString() });
      gracefulExit('unhandledRejection');
    });

    log("完整的退出处理已设置（SIGINT, SIGTERM, SIGHUP, uncaughtException, unhandledRejection）", "info");
  } catch (error) {
    log(`设置退出处理失败: ${error.message}`, "error");
  }
}

/**
 * 显示系统信息
 */
function showSystemInfo() {
  log("========== 系统信息 ==========", "info");
  log(`运行状态: ${isRunning ? (isPaused ? "已暂停" : "运行中") : "未运行"}`, "info");
  log(`有效Token数: ${validTokens.length}`, "info");
  log(`钱包映射数: ${walletAddressMap.size}`, "info");
  log(`并发限制: ${MAX_CONCURRENT_WALLETS}`, "info");
  log(`Node.js版本: ${process.version}`, "info");
  log(`平台: ${process.platform} ${process.arch}`, "info");
  log(`进程ID: ${process.pid}`, "info");
  log(`启动时间: ${new Date(Date.now() - process.uptime() * 1000).toLocaleString()}`, "info");
  log(`运行时长: ${Math.floor(process.uptime() / 3600)}小时${Math.floor((process.uptime() % 3600) / 60)}分钟`, "info");

  // 内存信息
  const memUsage = process.memoryUsage();
  log(`内存使用: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB / ${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`, "info");
  log(`系统内存: ${Math.round(memUsage.rss / 1024 / 1024)}MB`, "info");

  // M4 mini专用配置提示
  if (process.arch === 'arm64' && MAX_CONCURRENT_WALLETS >= 50) {
    log("🍎 检测到ARM64架构(可能是M4 mini)，当前配置:", "info");
    log(`   并发钱包数: ${MAX_CONCURRENT_WALLETS}`, "info");
    log(`   检查间隔: ${CHECK_INTERVAL/1000}秒`, "info");
    log(`   建议监控内存使用，必要时调整并发数`, "warning");
  }

  log("================================", "info");
}

/**
 * VPS健康检查
 */
function performHealthCheck() {
  try {
    const memUsage = process.memoryUsage();
    const memMB = Math.round(memUsage.heapUsed / 1024 / 1024);
    const uptimeHours = Math.floor(process.uptime() / 3600);

    log("========== VPS健康检查 ==========", "info");
    log(`运行时长: ${uptimeHours}小时`, "info");
    log(`内存使用: ${memMB}MB`, memMB > 500 ? "warning" : "info");
    log(`活跃定时器: ${process._getActiveHandles().length}`, "info");
    log(`活跃请求: ${process._getActiveRequests().length}`, "info");

    // 检查调度器状态
    const stats = scheduler.getWalletStats();
    log(`钱包状态: 总计${stats.totalWallets}, 运行中${scheduler.getRunningProcessCount()}`, "info");

    // 检查文件系统
    try {
      fs.accessSync('./wallet-queue-state.json', fs.constants.R_OK | fs.constants.W_OK);
      log(`状态文件: 正常`, "info");
    } catch (error) {
      log(`状态文件: 异常 - ${error.message}`, "error");
    }

    log("===============================", "info");

    // 如果内存使用过高，触发垃圾回收
    if (memMB > 800 && global.gc) {
      log("内存使用过高，触发垃圾回收", "warning");
      global.gc();
    }

    logToFile("VPS健康检查", {
      uptime: uptimeHours,
      memory: memMB,
      wallets: stats.totalWallets,
      running: scheduler.getRunningProcessCount()
    });

  } catch (error) {
    log(`健康检查失败: ${error.message}`, "error");
  }
}

/**
 * 设置每天自动重新加载问题库
 */
function setupDailyQuestionReloader() {
  // 计算今天的结束时间（23:59:59）
  const now = new Date();
  const endOfDay = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate(),
    23,
    59,
    59,
    999
  );
  
  // 计算到今天结束的毫秒数
  const msUntilEndOfDay = endOfDay.getTime() - now.getTime();
  
  // 设置定时器，在今天结束后重新加载问题库
  setTimeout(() => {
    try {
      // 重新加载问题库
      const questionCount = questionReader.reloadQuestions();
      log(`问题库已自动重新加载，共 ${questionCount} 个问题`, "success");
      logToFile("问题库已自动重新加载", { count: questionCount });
      
      // 设置明天的定时器
      setupDailyQuestionReloader();
    } catch (error) {
      log(`自动重新加载问题库失败: ${error.message}`, "error");
      
      // 如果失败，1小时后重试
      setTimeout(setupDailyQuestionReloader, 60 * 60 * 1000);
    }
  }, msUntilEndOfDay + 1000); // 加1秒，确保在下一天开始
  
  log(`问题库将在 ${new Date(now.getTime() + msUntilEndOfDay).toLocaleString()} 自动重新加载`, "info");
}

/**
 * 主函数
 */
/**
 * 检查关键文件权限
 */
function checkFilePermissions() {
  const criticalFiles = [
    'priv.txt',
    'proxies.txt',
    'wallet-queue-state.json',
    'klok-scores.csv',
    'src/data/questions.json'
  ];

  log("========== 文件权限检查 ==========", "info");

  for (const file of criticalFiles) {
    try {
      if (fs.existsSync(file)) {
        fs.accessSync(file, fs.constants.R_OK | fs.constants.W_OK);
        log(`✅ ${file}: 可读写`, "info");
      } else {
        log(`⚠️  ${file}: 文件不存在`, "warning");
      }
    } catch (error) {
      log(`❌ ${file}: 权限错误 - ${error.message}`, "error");
    }
  }

  // 检查目录权限
  const criticalDirs = ['src', 'src/data', 'src/services', 'logs'];
  for (const dir of criticalDirs) {
    try {
      if (fs.existsSync(dir)) {
        fs.accessSync(dir, fs.constants.R_OK | fs.constants.W_OK);
        log(`✅ ${dir}/: 可读写`, "info");
      } else {
        log(`⚠️  ${dir}/: 目录不存在`, "warning");
      }
    } catch (error) {
      log(`❌ ${dir}/: 权限错误 - ${error.message}`, "error");
    }
  }

  log("===============================", "info");
}

async function main() {
  try {
    // 检查文件权限
    checkFilePermissions();

    // 检查日志文件大小
    await checkLogSize();

    // 显示系统信息
    showSystemInfo();
    
    // 初始化钱包代理绑定
    await initializeWalletProxyBindings();
    
    // 设置退出处理
    setupExitHandler();
    
    // 设置每天自动重新加载问题库
    setupDailyQuestionReloader();

    // 设置定期健康检查（每6小时一次）
    setInterval(performHealthCheck, 6 * 60 * 60 * 1000);

    // 强制自动启动，忽略配置
    log("正在自动启动...", "info");
    await autoStartAutomation();
  } catch (error) {
    log(`主函数错误: ${error.message}`, "error");
    logToFile("主函数错误", { error: error.message, stack: error.stack });
  }
}

// 启动主程序
main();
