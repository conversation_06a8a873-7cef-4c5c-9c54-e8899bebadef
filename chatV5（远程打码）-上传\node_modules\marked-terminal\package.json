{"name": "marked-terminal", "version": "5.2.0", "description": "A custom render for marked to output to the Terminal", "main": "./index.cjs", "browser": "./index.js", "exports": {"node": {"import": "./index.js", "require": "./index.cjs"}, "default": "./index.js"}, "type": "module", "engines": {"node": ">=14.13.1 || >=16.0.0"}, "scripts": {"build": "rollup -c", "prepack": "npm run build", "test": "FORCE_HYPERLINK=0 mocha tests/*.js --reporter spec"}, "files": ["index.js", "index.cjs"], "keywords": ["marked", "render", "terminal", "markdown", "markdown-to-terminal"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "peerDependencies": {"marked": "^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0 || ^5.0.0"}, "dependencies": {"ansi-escapes": "^6.2.0", "cardinal": "^2.1.1", "chalk": "^5.2.0", "cli-table3": "^0.6.3", "node-emoji": "^1.11.0", "supports-hyperlinks": "^2.3.0"}, "directories": {"example": "example"}, "devDependencies": {"@rollup/plugin-commonjs": "^24.1.0", "@rollup/plugin-node-resolve": "^15.0.2", "marked": "^5.0.1", "mocha": "^10.2.0", "rollup": "^3.21.5"}, "repository": {"type": "git", "url": "https://github.com/mikaelbr/marked-terminal.git"}, "bugs": {"url": "https://github.com/mikaelbr/marked-terminal/issues"}, "homepage": "https://github.com/mikaelbr/marked-terminal"}