import { minutesInHour } from "./constants.js";

/**
 * @name hoursToMinutes
 * @category Conversion Helpers
 * @summary Convert hours to minutes.
 *
 * @description
 * Convert a number of hours to a full number of minutes.
 *
 * @param hours - number of hours to be converted
 *
 * @returns The number of hours converted in minutes
 *
 * @example
 * // Convert 2 hours to minutes:
 * const result = hoursToMinutes(2)
 * //=> 120
 */
export function hoursToMinutes(hours) {
  return Math.trunc(hours * minutesInHour);
}

// Fallback for modularized imports:
export default hoursToMinutes;
