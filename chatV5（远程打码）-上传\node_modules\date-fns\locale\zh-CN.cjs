"use strict";
exports.zhCN = void 0;
var _index = require("./zh-CN/_lib/formatDistance.cjs");
var _index2 = require("./zh-CN/_lib/formatLong.cjs");
var _index3 = require("./zh-CN/_lib/formatRelative.cjs");
var _index4 = require("./zh-CN/_lib/localize.cjs");
var _index5 = require("./zh-CN/_lib/match.cjs");

/**
 * @category Locales
 * @summary Chinese Simplified locale.
 * @language Chinese Simplified
 * @iso-639-2 zho
 * <AUTHOR> [@KingMario](https://github.com/KingMario)
 * <AUTHOR> [@fnlctrl](https://github.com/fnlctrl)
 * <AUTHOR> [@sabrinamiao](https://github.com/sabrinamiao)
 * <AUTHOR> [@cubicwork](https://github.com/cubicwork)
 * <AUTHOR> [@skyuplam](https://github.com/skyuplam)
 */
const zhCN = (exports.zhCN = {
  code: "zh-CN",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
