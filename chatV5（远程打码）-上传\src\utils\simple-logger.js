const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

// 日志文件路径
const LOG_FILE_PATH = path.join(process.cwd(), 'info.log');
const MAX_LOG_SIZE = 10 * 1024 * 1024; // 10MB

// 日志级别颜色
const LOG_COLORS = {
  info: chalk.blue,
  success: chalk.green,
  warning: chalk.yellow,
  error: chalk.red,
  debug: chalk.gray
};

// 进度信息
let progressInfo = {
  completed: 0,
  total: 0,
  percentage: 0
};

/**
 * 输出日志到控制台和文件
 * @param {string} message 日志消息
 * @param {string} type 日志类型 (info, success, warning, error, debug)
 */
function log(message, type = 'info') {
  // 确保type是字符串
  if (typeof type !== 'string') {
    type = 'info';
  }
  
  // 确保message是字符串
  if (message === undefined || message === null) {
    message = '';
  } else if (typeof message !== 'string') {
    try {
      message = String(message);
    } catch (e) {
      message = '[无法转换为字符串的对象]';
    }
  }
  
  const timestamp = new Date().toLocaleTimeString();
  const colorFn = LOG_COLORS[type] || LOG_COLORS.info;
  const typeUpper = type.toUpperCase();
  
  // 控制台输出
  console.log(`[${timestamp}] ${colorFn(`[${typeUpper}]`)} ${message}`);

  // 不再写入日志文件，只在控制台显示
}

/**
 * 记录聊天消息
 * @param {string} message 聊天消息
 * @param {string} role 角色 (user, assistant)
 */
function logChat(message, role) {
  const prefix = role === 'user' ? '发送' : '接收';
  const type = role === 'user' ? 'info' : 'success';
  const preview = message.length > 50 ? message.substring(0, 50) + '...' : message;
  log(`${prefix}: ${preview}`, type);
}

/**
 * 记录详细日志（现在只显示重要信息）
 * @param {string} action 操作描述
 * @param {object} data 相关数据
 * @param {boolean} important 是否重要
 */
function logToFile(action, data = null, important = true) {
  // 只显示重要信息，忽略API详细响应
  if (important) {
    const timestamp = new Date().toLocaleTimeString();
    console.log(`[${timestamp}] [IMPORTANT] ${action}`);

    // 只显示关键数据，不显示完整的API响应
    if (data && typeof data === 'object') {
      // 如果是钱包任务完成等重要信息，显示简化版本
      if (data.walletAddress || data.completedQuestions || data.pointsEarned) {
        const summary = {
          钱包: data.walletAddress ? data.walletAddress.substring(0, 8) + '...' : undefined,
          完成问题: data.completedQuestions,
          获得积分: data.pointsEarned,
          错误: data.error
        };
        // 移除undefined字段
        Object.keys(summary).forEach(key => summary[key] === undefined && delete summary[key]);
        console.log(JSON.stringify(summary, null, 2));
      }
    }
  }
  // 不重要的信息（如API响应详情）直接忽略
}

/**
 * 记录API请求（简化显示）
 * @param {string} method 请求方法
 * @param {string} url 请求URL
 * @param {object} data 请求数据
 * @param {object} headers 请求头
 * @param {boolean} sensitive 是否包含敏感信息
 */
function logApiRequest(method, url, data, headers, sensitive = false) {
  // 不显示API请求详情，太冗余
  return;
}

/**
 * 记录API响应（简化显示）
 * @param {string} endpoint 接口
 * @param {object} data 响应数据
 * @param {number} status 状态码
 * @param {object} headers 响应头
 * @param {boolean} sensitive 是否包含敏感信息
 */
function logApiResponse(endpoint, data, status, headers, sensitive = false) {
  // 不显示API响应详情，太冗余
  return;
}

/**
 * 记录API错误（只显示关键错误信息）
 * @param {string} endpoint 接口
 * @param {Error} error 错误对象
 */
function logApiError(endpoint, error) {
  // 只显示关键错误信息
  const timestamp = new Date().toLocaleTimeString();
  const status = error.response ? error.response.status : '未知';
  console.log(`[${timestamp}] [ERROR] API错误: ${endpoint} - ${error.message} (状态码: ${status})`);
}

/**
 * 检查日志文件大小（现在不需要，保留函数以兼容现有代码）
 */
function checkLogSize() {
  // 不再需要检查日志文件大小，因为不再写入文件
  return;
}

/**
 * 备份日志文件（现在不需要，保留函数以兼容现有代码）
 * @returns {string} 备份文件路径
 */
function backupLogFile() {
  // 不再需要备份日志文件，因为不再写入文件
  return null;
}

/**
 * 清空日志文件（现在不需要，保留函数以兼容现有代码）
 */
function clearLogFile() {
  // 不再需要清空日志文件，因为不再写入文件
  return;
}

/**
 * 更新进度信息
 * @param {number} completed 已完成数量
 * @param {number} total 总数量
 */
function updateProgress(completed, total) {
  progressInfo = {
    completed,
    total,
    percentage: total > 0 ? Math.round((completed / total) * 100) : 0
  };
  
  log(`进度: ${completed}/${total} (${progressInfo.percentage}%)`, 'info');
}

/**
 * 获取当前进度信息
 * @returns {object} 进度信息
 */
function getProgressInfo() {
  return { ...progressInfo };
}

/**
 * 生成随机延迟时间
 * @param {number} min 最小延迟(毫秒)
 * @param {number} max 最大延迟(毫秒)
 * @returns {number} 随机延迟时间
 */
function randomDelay(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

module.exports = {
  log,
  logChat,
  logToFile,
  logApiRequest,
  logApiResponse,
  logApiError,
  checkLogSize,
  backupLogFile,
  clearLogFile,
  updateProgress,
  getProgressInfo,
  randomDelay
};
