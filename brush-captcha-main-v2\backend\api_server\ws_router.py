from fastapi import APIRouter, WebSocket, WebSocketDisconnect

from core.task_stats import task_stats
from core.worker_manager import register_worker, update_worker_status, worker_pool
from core.task_manager import task_pool
from datetime import datetime
from core.task_manager import Status
from common.logger import get_logger,emoji
logger = get_logger("ws_routes")
router = APIRouter()
@router.websocket("/worker/{worker_id}")
async def worker_ws(ws: WebSocket, worker_id: str):
    await ws.accept()
    try:
        while True:
            msg = await ws.receive_json()
            logger.debug(f"received message: {msg}")
            if msg["type"] == "register":
                register_worker(worker_id, ws, msg)
            elif msg["type"] == "status_update":
                update_worker_status(worker_id, msg)
            elif msg["type"] == "task_result":
                logger.debug(f"Task result: {msg}")
                task_id = msg["taskId"]
                error_id = msg["errorId"]
                if task_id in task_pool:
                    # 获取分配的worker并减少任务计数
                    assigned_worker = task_pool[task_id].get("assignedTo")
                    if assigned_worker and assigned_worker in worker_pool:
                        if worker_pool[assigned_worker]["current_tasks"] > 0:
                            worker_pool[assigned_worker]["current_tasks"] -= 1
                            logger.debug(f"📉 Worker {assigned_worker} 任务计数减1，当前: {worker_pool[assigned_worker]['current_tasks']}")
                        else:
                            logger.warning(f"⚠️ Worker {assigned_worker} 任务计数已为0，无法减少")

                    # 根据错误码确定任务状态
                    if error_id == 0:
                        task_pool[task_id]["status"] = Status.SUCCESS
                        logger.info(f"✅ 任务 {task_id} 成功完成")
                    elif error_id == -2:
                        task_pool[task_id]["status"] = Status.TIMEOUT
                        logger.warning(f"⏰ 任务 {task_id} 执行超时")
                    else:
                        task_pool[task_id]["status"] = Status.FAILED
                        logger.error(f"❌ 任务 {task_id} 执行失败，错误码: {error_id}")

                    # 更新任务结果
                    task_pool[task_id]["errorId"] = error_id
                    task_pool[task_id]["result"] = msg["result"]
                    await task_stats.increment_completed()
                    # if error_id != 0:
                    #     task_pool[task_id]["status"] = Status.SUCCESS
                    #     task_pool[task_id]["errorId"] = error_id
                    #     task_pool[task_id]["result"] = msg["result"]
                    #     await task_stats.increment_completed()
                    # else:
                    #     task_pool[task_id]["status"] = Status.SUCCESS
                    #     task_pool[task_id]["errorId"] = 0
                    #     task_pool[task_id]["result"] = msg["result"]
                    #     await task_stats.increment_completed()
    except WebSocketDisconnect:
        logger.warning(f"Worker {worker_id} 断开连接，正在清理资源")
        await cleanup_worker_resources(worker_id)
        worker_pool.pop(worker_id, None)

async def cleanup_worker_resources(worker_id):
    """清理断开连接的worker的所有资源"""
    try:
        # 重置分配给该worker的所有任务
        from core.task_manager import task_pool, Status

        reset_tasks = []
        for task_id, task in task_pool.items():
            if task.get('assignedTo') == worker_id and task['status'] == Status.PENDING:
                reset_tasks.append(task_id)

        for task_id in reset_tasks:
            task_pool[task_id]['status'] = Status.WAITING
            task_pool[task_id]['assignedTo'] = None
            logger.info(f"重置任务 {task_id} 状态为等待分发")

        logger.info(f"已清理worker {worker_id} 的 {len(reset_tasks)} 个任务")

    except Exception as e:
        logger.error(f"清理worker {worker_id} 资源失败: {e}")
