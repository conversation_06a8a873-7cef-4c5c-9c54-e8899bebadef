/**
 * 钱包工作进程
 * 每个钱包在独立进程中执行，确保不同钱包之间完全隔离
 */
const { auth, chat, models, points, rateLimit } = require("./src/api");
const questionReader = require("./src/services/questionReader");
const walletProxyManager = require("./src/services/walletProxyManager");
const { log, logToFile, randomDelay } = require("./src/utils/simple-logger");
const config = require("./config");

// 从命令行参数获取钱包地址和令牌
const walletAddress = process.argv[2];
const token = process.argv[3];

// 创建进程唯一的上下文键
const contextKey = `${walletAddress}_${process.pid}`;

/**
 * 网络重试机制 - 专门用于恶劣网络环境
 */
async function retryNetworkOperation(operation, operationName, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await operation();
      if (attempt > 1) {
        log(`${operationName} 重试第${attempt}次成功`, "success");
      }
      return result;
    } catch (error) {
      const isNetworkError = error.message.includes('timeout') ||
                            error.message.includes('ECONNRESET') ||
                            error.message.includes('ENOTFOUND') ||
                            error.message.includes('ECONNREFUSED') ||
                            error.message.includes('socket hang up') ||
                            error.message.includes('network') ||
                            error.message.includes('Turnstile') ||
                            error.message.includes('captcha') ||
                            error.message.includes('验证码') ||
                            error.message.includes('打码') ||
                            error.code === 'ECONNRESET' ||
                            error.code === 'ETIMEDOUT';

      if (isNetworkError && attempt < maxRetries) {
        const waitTime = Math.min(2000 * attempt, 10000); // 2秒、4秒、6秒，最多10秒
        log(`${operationName} 网络错误，${waitTime/1000}秒后重试 (${attempt}/${maxRetries}): ${error.message}`, "warning");
        await new Promise(resolve => setTimeout(resolve, waitTime));
        continue;
      }

      if (attempt === maxRetries) {
        log(`${operationName} 重试${maxRetries}次后仍然失败: ${error.message}`, "error");
      }
      throw error;
    }
  }
}

// 简化版流式响应检测器
const streamDetector = {
  streamStates: new Map(),
  
  resetStreamState(key) {
    this.streamStates.set(key, {
      isComplete: false,
      startTime: Date.now(),
      lastUpdateTime: Date.now(),
      chunks: 0
    });
    log(`[流式响应] 已重置状态 (${key})`, "info");
  },
  
  markStreamComplete(key) {
    const state = this.streamStates.get(key);
    if (!state) return;
    
    state.isComplete = true;
    state.lastUpdateTime = Date.now();
    this.streamStates.set(key, state);
    
    const duration = state.lastUpdateTime - state.startTime;
    log(`[流式响应] 已完成 (${key}), 耗时: ${duration}ms`, "success");
  },
  
  updateStreamState(key, chunk) {
    let state = this.streamStates.get(key);
    if (!state) {
      this.resetStreamState(key);
      state = this.streamStates.get(key);
    }
    
    state.lastUpdateTime = Date.now();
    state.chunks++;
    
    // 检查是否包含完成标记
    if (chunk && (
      chunk.includes('[DONE]') || 
      chunk.includes('"finish_reason":"stop"') ||
      chunk.includes('"finish_reason": "stop"') ||
      /\}\s*$/.test(chunk.trim())
    )) {
      state.isComplete = true;
      log(`[流式响应] 检测到完成标记 (${key})`, "info");
    }
    
    this.streamStates.set(key, state);
  },
  
  isStreamComplete(key) {
    const state = this.streamStates.get(key);
    return !state || state.isComplete;
  },
  
  async waitForStreamCompletion(key, timeout = 90000) { // 恶劣环境下增加到90秒超时
    return new Promise((resolve) => {
      const checkInterval = 500; // 每500毫秒检查一次
      const startTime = Date.now();
      
      const check = () => {
        if (this.isStreamComplete(key)) {
          const duration = Date.now() - startTime;
          log(`[流式响应] 等待完成 (${key}), 耗时: ${duration}ms`, "success");
          resolve(true);
          return;
        }
        
        if (Date.now() - startTime > timeout) {
          log(`[流式响应] 等待超时 (${key}), 超时: ${timeout}ms`, "warning");
          this.markStreamComplete(key);
          resolve(false);
          return;
        }
        
        setTimeout(check, checkInterval);
      };
      
      check();
    });
  }
};

/**
 * 执行聊天任务
 * @returns {Promise<Object>} 任务结果
 */
async function executeChatTask() {
  try {
    // 设置当前token和钱包地址
    auth.setCurrentToken(token);
    auth.setCurrentWalletAddress(walletAddress);
    
    // 获取钱包对应的代理
    const proxy = walletProxyManager.getProxyForWallet(walletAddress);
    if (proxy) {
      log(`使用代理: ${proxy}`, "info");
    }
    
    // 验证token有效性
    log(`验证钱包 ${walletAddress.substring(0, 8)}... 的token有效性`, "info");
    updateActivity(); // 更新活跃度
    try {
      // 使用重试机制验证token
      const isValid = await retryNetworkOperation(
        () => auth.verifyToken(token),
        "Token验证",
        3
      );
      
      if (!isValid) {
        log(`Token无效: 验证失败`, "error");
        process.send({
          type: 'token_invalid',
          walletAddress,
          error: "Token无效: 验证失败"
        });
        throw new Error("Token无效: 验证失败");
      }
      
      log(`Token验证成功，可以执行任务`, "success");
    } catch (error) {
      log(`Token验证失败: ${error.message}`, "error");
      process.send({
        type: 'token_invalid',
        walletAddress,
        error: `Token验证失败: ${error.message}`
      });
      throw new Error(`Token验证失败: ${error.message}`);
    }
    
    // 获取用户信息（仅用于日志输出）
    log(`钱包地址: ${walletAddress}`, "info");
    logToFile("开始执行钱包任务", { walletAddress: walletAddress, pid: process.pid });
    
    // 初始化积分
    let beforePoints = 0;
    try {
      const pointData = await retryNetworkOperation(
        () => points.getUserPoints(),
        "获取初始积分",
        3
      );
      beforePoints = pointData.total_points || 0;
      log(`初始积分: ${beforePoints}`, "info");
    } catch (error) {
      log(`获取初始积分失败: ${error.message}`, "warning");
      beforePoints = 0;
    }
    
    // 获取初始可用问题数（仅用于日志显示）
    try {
      const rateData = await rateLimit.getRateLimit();
      const remainingQuestions = rateData.remaining || 0;
      log(`初始剩余可用问题数: ${remainingQuestions}`, "info");

      // 如果剩余问题数为0，直接抛出异常终止任务
      if (remainingQuestions <= 0) {
        throw new Error("没有可用的问题数量，终止任务");
      }
    } catch (error) {
      log(`获取初始剩余问题数失败: ${error.message}`, "warning");
      // 不再设置默认值，让while循环中的检查来决定是否继续
    }
    
    // 不再限制最大问题数，完全按照rate limit执行
    log(`开始执行任务，将根据实际rate limit动态执行问题`, "info");
    
    // 创建新的聊天线程
    try {
      chat.createThread();
      log("已创建新的聊天线程", "success");
    } catch (error) {
      log(`创建聊天线程失败: ${error.message}`, "error");
      // 尝试再次创建
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        chat.createThread();
        log("重试创建聊天线程成功", "success");
      } catch (retryError) {
        log(`重试创建聊天线程仍然失败: ${retryError.message}`, "error");
        throw new Error("无法创建聊天线程");
      }
    }
    
    // 获取可用模型
    let selectedModel = "gemini-1.5-pro"; // 默认模型
    try {
      log("正在获取可用模型...", "info");
      const availableModels = await models.getModels();
      if (availableModels && availableModels.length > 0) {
        // 处理模型可能是对象的情况
        if (typeof availableModels[0] === 'object') {
          // 如果是对象，尝试提取model_id或name属性
          if (availableModels[0].model_id) {
            selectedModel = availableModels[0].model_id;
          } else if (availableModels[0].name) {
            selectedModel = availableModels[0].name;
          } else {
            // 如果没有这些属性，使用JSON字符串化输出对象内容供调试
            log(`模型对象结构: ${JSON.stringify(availableModels[0])}`, "info");
            // 使用默认模型
          }
        } else {
          selectedModel = availableModels[0];
        }
        log(`成功获取模型列表，选择: ${selectedModel}`, "success");
      } else {
        log(`未找到可用模型，使用默认模型: ${selectedModel}`, "warning");
      }
    } catch (error) {
      log(`获取模型列表失败: ${error.message}`, "warning");
      log(`使用默认模型: ${selectedModel}`, "info");
    }
    
    try {
      // 确保传递给API的是正确的模型标识符
      chat.setSelectedModel(selectedModel);
      log(`已选择模型: ${selectedModel}`, "info");
    } catch (error) {
      log(`设置模型失败: ${error.message}`, "error");
      // 尝试使用默认模型
      try {
        chat.setSelectedModel("gemini-1.5-pro");
        log(`回退到默认模型: gemini-1.5-pro`, "info");
      } catch (fallbackError) {
        log(`设置默认模型也失败: ${fallbackError.message}`, "error");
      }
    }
    
    // 执行聊天 - 使用动态while循环，完全按照rate limit执行
    let completedQuestions = 0;
    let pointsEarned = 0;
    let lastPoints = beforePoints;
    let questionIndex = 0;

    let consecutiveErrors = 0;
    const MAX_CONSECUTIVE_ERRORS = 5; // 恶劣环境下增加容错次数
    let networkSlowCount = 0; // 网络缓慢计数
    const MAX_NETWORK_SLOW = 3; // 最大网络缓慢次数

    while (true) {
      // 检查连续错误次数
      if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
        log(`连续错误次数达到${MAX_CONSECUTIVE_ERRORS}次，停止执行`, "error");
        break;
      }

      // 每次都检查剩余问题数
      updateActivity(); // 更新活跃度
      try {
        const rateData = await retryNetworkOperation(
          () => rateLimit.getRateLimit(),
          "获取剩余问题数",
          3
        );
        const currentRemaining = rateData.remaining || 0;
        log(`当前剩余问题数: ${currentRemaining}`, "info");

        if (currentRemaining <= 0) {
          log("已达到问题数限制，停止提问", "warning");
          break;
        }

        // 重置连续错误计数
        consecutiveErrors = 0;
      } catch (error) {
        consecutiveErrors++;
        log(`检查剩余问题数失败 (${consecutiveErrors}/${MAX_CONSECUTIVE_ERRORS}): ${error.message}`, "warning");

        if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
          log("连续获取rate limit失败，停止执行", "error");
          break;
        }

        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 5000));
        continue;
      }

      // 动态获取问题
      const question = questionReader.getRandomQuestion();
      if (!question) {
        log("无法获取问题，停止执行", "error");
        break;
      }

      questionIndex++;
      log(`[${questionIndex}] 发送问题: ${question.substring(0, 30)}...`, "info");
      
      try {
        // 重置流式响应状态
        streamDetector.resetStreamState(contextKey);
        
        log(`准备发送问题: ${question.substring(0, 50)}...`, "info");
        
        // 发送问题 - 使用重试机制
        updateActivity(); // 更新活跃度
        let response;
        try {
          response = await retryNetworkOperation(
            () => chat.sendChatMessage(question),
            "发送聊天消息",
            2 // 聊天消息只重试2次，避免重复发送
          );
          log(`成功发送问题，正在等待响应...`, "info");

          // 不再立即标记流式响应完成，而是等待真正的流式响应结束
        } catch (chatError) {
          log(`发送问题遇到错误: ${chatError.message}`, "error");
          // 如果有部分响应，使用它
          response = chatError.partialResponse || "无响应";
          // 错误情况下才标记流式响应完成
          streamDetector.markStreamComplete(contextKey);
        }
        
        // 等待流式响应完成，恶劣环境下增加超时时间
        updateActivity(); // 更新活跃度
        const streamStartTime = Date.now();
        const streamCompleted = await streamDetector.waitForStreamCompletion(contextKey, 90000); // 90秒超时
        const streamDuration = Date.now() - streamStartTime;
        updateActivity(); // 流式响应完成后更新活跃度

        if (!streamCompleted) {
          log(`流式响应等待超时，这是正常情况，继续执行后续逻辑`, "info");
          // 流式响应超时是正常情况，但可以适当延长下次执行间隔
          networkSlowCount++;

          if (networkSlowCount >= MAX_NETWORK_SLOW) {
            log(`流式响应连续超时${networkSlowCount}次，可能网络较慢`, "warning");
            // 适当增加等待时间，但不要太长
            const extraWait = Math.min(networkSlowCount * 3000, 15000); // 最多15秒
            log(`网络较慢，额外等待${extraWait/1000}秒...`, "info");
            await new Promise(resolve => setTimeout(resolve, extraWait));
          }
        } else {
          // 重置网络缓慢计数
          if (streamDuration < 30000) { // 如果响应时间正常（小于30秒）
            networkSlowCount = Math.max(0, networkSlowCount - 1);
          }

          if (streamDuration > 60000) { // 如果响应时间超过60秒
            log(`流式响应耗时较长: ${Math.round(streamDuration/1000)}秒，这是正常情况`, "info");
            // 流式响应耗时长是正常的，轻微增加计数
            networkSlowCount = Math.min(networkSlowCount + 0.5, MAX_NETWORK_SLOW);
          }
        }

        // 额外等待2-3秒，确保网页处理完毕
        const extraWaitTime = 2000 + Math.floor(Math.random() * 1000);
        log(`额外等待 ${extraWaitTime/1000} 秒确保处理完毕...`, "info");
        await new Promise(resolve => setTimeout(resolve, extraWaitTime));
        
        log(`收到回复: ${response.substring(0, 30)}...`, "success");
        completedQuestions++;
        consecutiveErrors = 0; // 重置连续错误计数

        // 检查积分是否增加
        try {
          const pointData = await retryNetworkOperation(
            () => points.getUserPoints(),
            "检查积分",
            3
          );
          const currentPoints = pointData.total_points || 0;

          if (currentPoints > lastPoints) {
            pointsEarned += (currentPoints - lastPoints);
            log(`积分增加: +${currentPoints - lastPoints}，当前总积分: ${currentPoints}`, "success");
            lastPoints = currentPoints;
          } else {
            log(`积分未增加，当前总积分: ${currentPoints}`, "warning");
          }
        } catch (error) {
          log(`检查积分失败: ${error.message}`, "warning");
        }

        // 问题之间添加5-8秒的随机等待时间
        const waitTime = 5000 + Math.floor(Math.random() * 3000);
        log(`等待 ${waitTime/1000} 秒后发送下一个问题...`, "info");
        await new Promise(resolve => setTimeout(resolve, waitTime));

      } catch (error) {
        consecutiveErrors++;
        log(`问题 ${questionIndex} 执行失败 (${consecutiveErrors}/${MAX_CONSECUTIVE_ERRORS}): ${error.message}`, "error");
        logToFile("问题执行失败", { questionIndex, error: error.message, consecutiveErrors });

        // 如果连续失败次数过多，停止执行
        if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
          log("连续问题失败次数过多，停止执行", "error");
          break;
        }

        // 等待一段时间后继续
        const errorWaitTime = 5000 + (consecutiveErrors * 2000); // 错误次数越多等待越久
        log(`等待 ${errorWaitTime/1000} 秒后重试...`, "info");
        await new Promise(resolve => setTimeout(resolve, errorWaitTime));
      }
    }
    
    // 任务完成，获取最终积分
    let finalPoints = lastPoints;
    try {
      const pointData = await points.getUserPoints();
      finalPoints = pointData.total_points || 0;
      pointsEarned = finalPoints - beforePoints;
    } catch (error) {
      log(`获取最终积分失败: ${error.message}`, "warning");
    }
    
    // 记录任务结果
    const result = {
      walletAddress,
      completedQuestions,
      pointsEarned,
      beforePoints,
      afterPoints: finalPoints,
      success: completedQuestions > 0,
      executionTime: new Date().toISOString(),
      totalQuestions: completedQuestions,
      averagePointsPerQuestion: completedQuestions > 0 ? Math.round(pointsEarned / completedQuestions) : 0
    };
    
    log(`任务完成: 成功回答 ${completedQuestions} 个问题，获得 ${pointsEarned} 积分`, "success");
    logToFile("钱包任务完成", result);
    
    // 向主进程发送完成消息
    process.send({
      type: 'completed',
      walletAddress,
      completedTasks: 1,
      pointsEarned,
      result
    });
    
    return result;
  } catch (error) {
    log(`执行任务失败: ${error.message}`, "error");
    logToFile("执行任务失败", { walletAddress, error: error.message, stack: error.stack });
    
    // 检查是否是token相关错误
    const isTokenError = error.message.includes('token') || 
                        error.message.includes('unauthorized') || 
                        error.message.includes('authentication') ||
                        error.message.includes('授权') ||
                        error.message.includes('认证');
    
    // 向主进程发送错误消息
    if (isTokenError) {
      process.send({
        type: 'token_invalid',
        walletAddress,
        error: error.message
      });
    } else {
      process.send({
        type: 'error',
        walletAddress,
        error: error.message
      });
    }
    
    throw error;
  }
}

// 设置进程错误处理
process.on('uncaughtException', (error) => {
  log(`未捕获的异常: ${error.message}`, "error");
  logToFile("未捕获的异常", { walletAddress, error: error.message, stack: error.stack });

  try {
    process.send({
      type: 'error',
      walletAddress,
      error: `未捕获异常: ${error.message}`
    });
  } catch (sendError) {
    // 忽略发送错误
  }

  // 强制退出，避免进程卡死
  setTimeout(() => process.exit(1), 1000);
});

// 处理未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  log(`未处理的Promise拒绝: ${reason}`, "error");
  logToFile("未处理的Promise拒绝", { walletAddress, reason: reason.toString() });

  try {
    process.send({
      type: 'error',
      walletAddress,
      error: `未处理的Promise拒绝: ${reason}`
    });
  } catch (sendError) {
    // 忽略发送错误
  }

  // 强制退出，避免进程卡死
  setTimeout(() => process.exit(1), 1000);
});

// 添加进程活跃度检测
let lastActivityTime = Date.now();
const ACTIVITY_CHECK_INTERVAL = 30000; // 30秒检查一次
const MAX_INACTIVE_TIME = 300000; // 5分钟无活动则认为卡死

function updateActivity() {
  lastActivityTime = Date.now();
}

// 定期检查进程活跃度
const activityChecker = setInterval(() => {
  const now = Date.now();
  const inactiveTime = now - lastActivityTime;

  if (inactiveTime > MAX_INACTIVE_TIME) {
    log(`进程无活动超过${Math.round(inactiveTime/1000)}秒，可能卡死，强制退出`, "error");

    try {
      process.send({
        type: 'error',
        walletAddress,
        error: `进程无活动超过${Math.round(inactiveTime/1000)}秒，疑似卡死`
      });
    } catch (sendError) {
      // 忽略发送错误
    }

    process.exit(1);
  }
}, ACTIVITY_CHECK_INTERVAL);

// 添加消息处理函数
process.on('message', (message) => {
  if (message.type === 'ping') {
    // 响应心跳检测
    process.send({
      type: 'pong',
      walletAddress,
      timestamp: new Date().toISOString()
    });
  }
});

// 启动任务
if (walletAddress && token) {
  log(`启动钱包 ${walletAddress.substring(0, 8)}... 的工作进程`, "info");
  log(`进程 ID: ${process.pid}`, "info");

  // 设置进程超时机制 - 恶劣环境下30分钟后强制退出
  const PROCESS_TIMEOUT = 30 * 60 * 1000; // 30分钟
  const timeoutId = setTimeout(() => {
    log(`钱包 ${walletAddress.substring(0, 8)}... 进程超时，强制退出`, "error");

    try {
      process.send({
        type: 'error',
        walletAddress,
        error: '进程执行超时，强制退出'
      });
    } catch (sendError) {
      log(`无法发送超时消息到主进程: ${sendError.message}`, "error");
    }

    process.exit(1);
  }, PROCESS_TIMEOUT);

  // 尝试执行聊天任务
  executeChatTask()
    .then((result) => {
      clearTimeout(timeoutId); // 清除超时定时器
      clearInterval(activityChecker); // 清除活跃度检测器
      log(`钱包 ${walletAddress.substring(0, 8)}... 任务成功完成`, "success");
      process.exit(0);
    })
    .catch((error) => {
      clearTimeout(timeoutId); // 清除超时定时器
      clearInterval(activityChecker); // 清除活跃度检测器
      log(`钱包 ${walletAddress.substring(0, 8)}... 任务失败: ${error.message}`, "error");
      // 尝试发送错误消息给主进程
      try {
        // 检查是否是token相关错误
        const isTokenError = error.message.includes('token') ||
                            error.message.includes('unauthorized') ||
                            error.message.includes('authentication') ||
                            error.message.includes('授权') ||
                            error.message.includes('认证');

        if (isTokenError) {
          process.send({
            type: 'token_invalid',
            walletAddress,
            error: error.message
          });
        } else {
          process.send({
            type: 'error',
            walletAddress,
            error: error.message
          });
        }
      } catch (sendError) {
        log(`无法发送错误消息到主进程: ${sendError.message}`, "error");
      }
      process.exit(1);
    });
} else {
  log("缺少必要的参数: 钱包地址或令牌", "error");
  process.exit(1);
}
