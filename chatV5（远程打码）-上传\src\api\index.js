const auth = require("./auth");
const chat = require("./chat");
const models = require("./models");
const points = require("./points");
const RateLimiter = require("./rate-limit");

// 创建RateLimiter实例
// 初始化时没有token，在设置token时会更新
const rateLimiterInstance = new RateLimiter("");

// 包装一个兼容对象，提供与walker-worker.js中调用方式兼容的API
const rateLimit = {
  getRateLimit: async () => {
    try {
      // 确保使用当前token
      rateLimiterInstance.token = auth.getCurrentSessionToken();
      return await rateLimiterInstance.getRateLimit();
    } catch (error) {
      console.error(`获取速率限制失败: ${error.message}`);
      // 返回默认值
      return {
        limit: 10,
        remaining: 5,
        resetTime: 3600,
        currentUsage: 0
      };
    }
  }
};

module.exports = {
  auth: {
    ...auth,
    readAllSessionTokensFromFile: auth.readAllSessionTokensFromFile,
  },
  chat,
  models,
  points,
  rateLimit,
};
