{"name": "blessed-contrib", "version": "4.11.0", "description": "", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "npm run lint", "lint": "eslint lib", "lint:fix": "eslint lib --fix"}, "repository": {"type": "git", "url": "https://github.com/yaronn/blessed-contrib.git"}, "author": "<PERSON><PERSON> (<EMAIL>)", "license": "MIT", "devDependencies": {"@types/blessed": "^0.1.6", "blessed": "0.1.54", "colors": "^1.1.2", "eslint": "^5.11.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0"}, "dependencies": {"ansi-term": ">=0.0.2", "chalk": "^1.1.0", "drawille-canvas-blessed-contrib": ">=0.1.3", "lodash": "~>=4.17.21", "map-canvas": ">=0.1.5", "marked": "^4.0.12", "marked-terminal": "^5.1.1", "memory-streams": "^0.1.0", "memorystream": "^0.3.1", "picture-tuber": "^1.0.1", "sparkline": "^0.1.1", "strip-ansi": "^3.0.0", "term-canvas": "0.0.5", "x256": ">=0.0.1"}}