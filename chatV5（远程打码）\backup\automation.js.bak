const pLimit = require("p-limit");
const path = require("path");
const fs = require("fs");
const { auth, chat, models, points, rateLimit } = require("./api");
const RateLimiter = require("./api/rate-limit");
// 移除对groq的依赖，因为我们只使用question.txt获取问题
// const { groq, apiManager } = require("./services");
const { log, logToFile, randomDelay, checkLogSize, updateProgress } = require("./utils/simple-logger");
const config = require("../config");
const walletProxyManager = require("./services/walletProxyManager");
const questionReader = require("./services/questionReader");

// 直接使用配置文件中的线程数
const THREADS = config.THREADS || 10;

let isRunning = false;
let isPaused = false;
let currentTokenIndex = 0;
let allTokens = [];
let userInfoCache = {};
let lastShuffleDate = null;
let dailyProgress = {
  completed: 0,
  total: 0,
  startTime: null
};

async function initAutomation() {
  try {
    log("正在初始化服务...", "info");
    logToFile("初始化自动化服务");

    // 移除对Groq API的初始化，因为我们只使用question.txt获取问题
    // await groq.initGroqClient();

    // 读取代理列表
    const proxies = readProxiesFromFile();
    log(`已加载 ${proxies.length} 个代理`, "info");

    // 初始化钱包队列
    await initWalletQueue();

    log("已准备好开始自动化", "success");
    return true;
  } catch (error) {
    log(`初始化错误: ${error.message}`, "error");
    logToFile("初始化错误", { error: error.message, stack: error.stack });
    return false;
  }
}

function readProxiesFromFile() {
  try {
    const proxyPath = path.join(process.cwd(), "proxies.txt");
    if (!fs.existsSync(proxyPath)) {
      log("未找到代理文件 proxies.txt", "warning");
      return [];
    }

    const content = fs.readFileSync(proxyPath, "utf8");
    const proxies = content
      .split("\n")
      .map(line => line.trim())
      .filter(line => line.length > 0);

    return proxies;
  } catch (error) {
    log(`读取代理文件失败: ${error.message}`, "error");
    return [];
  }
}

async function initWalletQueue() {
  try {
    // 读取所有会话令牌
    allTokens = auth.readAllSessionTokensFromFile();
    if (allTokens.length === 0) {
      log("未发现令牌。无法启动自动化。", "error");
      return false;
    }

    log(`已加载 ${allTokens.length} 个钱包会话`, "info");

    // 检查是否需要随机打乱钱包队列
    if (config.SHUFFLE_WALLETS_DAILY) {
      checkAndShuffleWallets();
    }

    // 初始化每日进度
    resetDailyProgress();

    // 如果启用了钱包代理绑定，自动为未绑定的钱包分配代理
    if (config.ENABLE_WALLET_PROXY_BINDING) {
      const walletAddresses = [];

      // 获取所有钱包地址
      for (const token of allTokens) {
        try {
          auth.setCurrentToken(token);
          const userInfo = await auth.getUserInfo();
          if (userInfo && userInfo.wallet_address) {
            walletAddresses.push(userInfo.wallet_address);
            userInfoCache[token] = userInfo;
          }
        } catch (error) {
          log(`获取钱包地址失败: ${error.message}`, "error");
        }
      }

      // 读取代理列表
      const proxies = readProxiesFromFile();

      // 自动绑定钱包和代理
      if (walletAddresses.length > 0 && proxies.length > 0) {
        walletProxyManager.autoBindWallets(walletAddresses, proxies);
      }
    }

    return true;
  } catch (error) {
    log(`初始化钱包队列失败: ${error.message}`, "error");
    logToFile("初始化钱包队列失败", { error: error.message });
    return false;
  }
}

function checkAndShuffleWallets() {
  const today = new Date().toISOString().split('T')[0]; // 获取当前日期 YYYY-MM-DD

  if (!lastShuffleDate || lastShuffleDate !== today) {
    // 新的一天，需要重新打乱钱包队列
    shuffleWalletQueue();
    lastShuffleDate = today;
    resetDailyProgress();
  }
}

function shuffleWalletQueue() {
  if (allTokens.length <= 1) return;

  log("随机打乱钱包队列...", "info");
  logToFile("随机打乱钱包队列");

  // Fisher-Yates 洗牌算法
  for (let i = allTokens.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [allTokens[i], allTokens[j]] = [allTokens[j], allTokens[i]];
  }

  log(`钱包队列已随机打乱，共 ${allTokens.length} 个钱包`, "success");
}

function resetDailyProgress() {
  dailyProgress = {
    completed: 0,
    total: allTokens.length,
    startTime: new Date()
  };
  updateProgressDisplay();
}

function updateProgressDisplay() {
  const percentage = dailyProgress.total > 0
    ? Math.round((dailyProgress.completed / dailyProgress.total) * 100)
    : 0;

  const progressText = `进度: ${dailyProgress.completed}/${dailyProgress.total} (${percentage}%)`;
  updateProgress(dailyProgress.completed, dailyProgress.total);
}

async function runSingleAutomation(token, idx) {
  let consecutiveErrors = 0;
  const MAX_CONSECUTIVE_ERRORS = 3;

  function localLog(message, level = "info") {
    try {
      // 确保消息是字符串类型
      const safeMessage = String(message);
      // 修复token和idx显示格式以避免编码问题
      const tokenPreview = Buffer.from(token.slice(0, 8), 'utf8').toString('utf8');
      log(`[${tokenPreview}] [${idx}] ${safeMessage}`, level);
    } catch (err) {
      // 降级处理：如果出现编码问题，使用简化的日志格式
      log(`[令牌${idx}] ${String(message)}`, level);
    }
  }
  function localLogToFile(message, data = null, verbose = true) {
    try {
      const tokenPreview = Buffer.from(token.slice(0, 8), 'utf8').toString('utf8');
      logToFile(`[${tokenPreview}] [${idx}] ${message}`, data, verbose);
    } catch (err) {
      // 降级处理
      logToFile(`[令牌${idx}] ${message}`, data, verbose);
    }
  }

  try {
    auth.setCurrentToken(token);

    await auth.login(false);

    const limiter = new RateLimiter(token);

    const pts = await points.getUserPoints();
    localLog(`令牌 ${token.slice(0, 8)} => 积分: ${pts.total_points}`, "info");

    const modelList = await models.getModels();
    localLog(`令牌 ${token.slice(0, 8)} => 模型数量: ${modelList.length}`, "info");
    await models.selectDefaultModel();

    chat.createThread();

    localLog(`令牌 ${token.slice(0, 8)} 的自动化已启动`, "info");
    logToFile("令牌的自动化已启动", { tokenPreview: token.slice(0, 8) });

    while (isRunning) {
      checkLogSize();

      // const available = await rateLimit.checkRateLimitAvailability();
      // if (!available) {
      //   const info = await rateLimit.getRateLimit();
      //   if (info.remaining === 0) {
      //     log(`Token ${token.slice(0,8)} => exhausted daily limit => finishing`, "warning");
      //   } else {
      //     log(`Token ${token.slice(0,8)} => partial cooldown => finishing anyway`, "warning");
      //   }
      //   break;
      // } else {
      //   const info = await rateLimit.getRateLimit();
      //   if (info.remaining === 0) {
      //     log(`Token ${token.slice(0,8)} => remain=0 => finishing`, "warning");
      //     break;
      //   }
      // }

      try {
        const newRL = await limiter.getRateLimit();
        if (newRL.remaining === 0) {
          localLog(`令牌 ${token.slice(0, 8)} => 剩余次数=0 => 结束。切换到下一个令牌。`, "warning");
          await auth.switchToNextToken();
          break;
        }
      } catch (rlErr) {
        logToFile(`令牌 ${token.slice(0, 8)} 的速率限制错误`, { error: rlErr.message }, false);
      }

      // 保留selectedApi变量以便后续代码兼容
      const selectedApi = "gemini";
      localLog(`令牌 ${token.slice(0, 8)} => 准备从问题库获取问题`, "info");

      // 生成随机用户消息
      let userMessage;
      try {
        // 从问题库获取随机问题
        userMessage = questionReader.getRandomQuestion();
        localLog(`令牌 ${token.slice(0, 8)} => 从问题库获取问题: "${userMessage.substring(0, 50)}${userMessage.length > 50 ? '...' : ''}"`, "info");
      } catch (msgErr) {
        localLog(`令牌 ${token.slice(0, 8)} => 从问题库获取问题失败: ${msgErr.message}`, "error");

        // 使用备用问题列表
        const fallbackQuestions = [
          "你认为人工智能将如何改变我们的未来生活？",
          "如果可以和历史上任何一位人物共进晚餐，你会选择谁，为什么？",
          "你能分享一个鲜为人知但非常有趣的科学事实吗？",
          "你最喜欢的书籍或电影是什么，它如何影响了你？",
          "你认为人类最伟大的发明是什么？为什么？",
          "如果你有超能力，你会选择什么能力，你会如何使用它？",
          "未来十年，你认为科技会有哪些重大突破？",
          "地球上最神秘的地方是哪里？为什么它令人着迷？",
          "你能解释一个复杂的科学概念，让小学生也能理解吗？",
          "人类与其他动物最大的区别是什么？",
          "如何平衡科技使用和保持真实的人际关系？",
          "你能分享一个改变了你世界观的经历或概念吗？",
          "你认为宇宙中存在其他智慧生命吗？为什么？",
          "如何在日常生活中培养创造力？",
          "你能分享一个鲜为人知的历史事件吗？",
          "如果你可以发明一种新技术，它会是什么样的？",
          "你认为人类最大的潜力和局限性是什么？",
          "什么是你最感兴趣的科学领域，为什么？",
          "如何在信息爆炸的时代保持专注和高效？",
          "你对未来50年的预测是什么？",
          "生活中哪些小习惯对你影响最大？",
          "你认为人类进化的下一步是什么？",
          "如何培养批判性思维？",
          "你认为最被低估的技能是什么？",
          "人类应该移民到其他星球吗？为什么？"
        ];
        userMessage = fallbackQuestions[Math.floor(Math.random() * fallbackQuestions.length)];
        localLog(`令牌 ${token.slice(0, 8)} => 使用备用问题: "${userMessage}"`, "info");
      }

      try {
        await chat.sendChatMessage(userMessage);
        consecutiveErrors = 0;

        // 记录API使用统计
        const stats = apiManager.getStats();
        logToFile(`${apiName} API 使用统计`, {
          api: selectedApi,
          calls: stats[selectedApi].calls,
          errors: stats[selectedApi].errors,
          remaining: stats[selectedApi].remaining
        }, false);

      } catch (chatError) {
        consecutiveErrors++;
        logToFile(
          `令牌 ${token.slice(0, 8)} 的聊天错误 (连续错误: ${consecutiveErrors}/${MAX_CONSECUTIVE_ERRORS})`,
          { error: chatError.message },
          false
        );

        if (
          chatError.response &&
          (chatError.response.status === 401 || chatError.response.status === 403)
        ) {
          localLog(`令牌 ${token.slice(0, 8)} => 无效 => 停止`, "warning");
          break;
        }

        if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
          localLog(`令牌 ${token.slice(0, 8)} 的错误过多 => 暂停 1 分钟`, "error");
          await new Promise((r) => setTimeout(r, 60000));
          consecutiveErrors = 0;
        } else {
          await new Promise((r) => setTimeout(r, 5000));
        }
        continue;
      }

      try {
        const updatedPoints = await points.getUserPoints();
        localLog(`令牌 ${token.slice(0, 8)} => 更新后的积分: ${updatedPoints.total_points}`, "info");
      } catch (ptErr) {
        logToFile(`令牌 ${token.slice(0, 8)} 的积分更新错误`, { error: ptErr.message }, false);
      }

      // 使用配置文件中的聊天延迟设置
      const delayRange = config.MAX_CHAT_DELAY - config.MIN_CHAT_DELAY;
      const delay = Math.floor(Math.random() * delayRange) + config.MIN_CHAT_DELAY;
      const delaySeconds = (delay / 1000).toFixed(1);
      localLog(`令牌 ${token.slice(0, 8)} 休眠 ${delaySeconds} 秒...`, "info");
      await new Promise((r) => setTimeout(r, delay));
    }

    localLog(`令牌 ${token.slice(0, 8)} 的自动化已结束`, "info");
  } catch (error) {
    localLog(`令牌 ${token.slice(0, 8)} 的致命错误 => ${error.message}`, "error");
    logToFile("令牌的致命错误", { token: token.slice(0, 8), error: error.message }, false);
  }
}

async function startAutomation() {
  if (isRunning) {
    log("自动化已在运行中", "warning");
    return;
  }
  isRunning = true;
  log("正在启动多令牌并发自动化（无冷却等待）...", "info");
  logToFile("正在启动多令牌并发自动化（无冷却等待）");

  const tokens = auth.readAllSessionTokensFromFile();
  if (!tokens || tokens.length === 0) {
    log("未发现令牌。无法启动自动化。", "error");
    isRunning = false;
    return;
  }

  const limit = pLimit(THREADS);
  tokens.forEach((tk, i) => {
    limit(() => runSingleAutomation(tk, i + 1));
  });

  log(`已排队 ${tokens.length} 个令牌，并发数=${THREADS}`, "info");
}

function pauseAutomation() {
  if (!isRunning) {
    log("自动化未运行", "warning");
    return;
  }
  isRunning = false;
  log("自动化已暂停", "warning");
  logToFile("自动化已暂停");
}

function resumeAutomation() {
  if (isRunning) {
    log("自动化已在运行中", "warning");
    return;
  }
  log("正在恢复自动化...", "info");
  logToFile("恢复自动化");
  startAutomation();
}

function getRunningState() {
  return isRunning;
}

async function manualSwitchAccount() {
  log("在并发模式下不支持手动切换账户", "warning");
  return false;
}

async function runAutomation() {
  if (!isRunning || isPaused) {
    return;
  }

  try {
    // 检查是否需要随机打乱钱包队列
    if (config.SHUFFLE_WALLETS_DAILY) {
      checkAndShuffleWallets();
    }

    // 获取当前令牌
    const currentToken = allTokens[currentTokenIndex];
    if (!currentToken) {
      log("无效的令牌索引", "error");
      return;
    }

    // 设置当前令牌
    auth.setCurrentToken(currentToken);

    // 获取用户信息
    let userInfo = userInfoCache[currentToken];
    if (!userInfo) {
      userInfo = await auth.getUserInfo();
      if (userInfo) {
        userInfoCache[currentToken] = userInfo;
      }
    }

    // 如果启用了钱包代理绑定，设置当前钱包地址
    if (config.ENABLE_WALLET_PROXY_BINDING && userInfo && userInfo.wallet_address) {
      auth.setCurrentWalletAddress(userInfo.wallet_address);
    }

    // 获取钱包地址
    const walletAddress = userInfo ? userInfo.wallet_address : "未知钱包";

    // 更新状态
    log(`账户 ${currentTokenIndex + 1}/${allTokens.length} - ${walletAddress.substring(0, 8)}...`, "info");

    // 创建聊天线程
    chat.createThread();

    // 设置模型
    const modelName = models.getAvailableModels()[0] || "gemini-1.5-pro";
    chat.setSelectedModel(modelName);

    // 获取问题（从question.txt而不是Gemini API）
    const question = questionReader.getRandomQuestion();

    // 发送聊天消息
    log(`发送消息: ${question.substring(0, 50)}${question.length > 50 ? "..." : ""}`, "info");
    const response = await chat.sendChatMessage(question);

    // 记录响应
    log(`收到回复: ${response.substring(0, 50)}${response.length > 50 ? "..." : ""}`, "success");

    // 更新进度
    dailyProgress.completed++;
    updateProgressDisplay();

    // 切换到下一个账户
    switchToNextAccount();

    // 随机延迟后继续
    const delay = Math.floor(Math.random() * (config.MAX_CHAT_DELAY - config.MIN_CHAT_DELAY + 1)) + config.MIN_CHAT_DELAY;
    log(`等待 ${delay / 1000} 秒后继续...`, "info");

    setTimeout(runAutomation, delay);
  } catch (error) {
    log(`自动化过程中出错: ${error.message}`, "error");
    logToFile("自动化过程中出错", { error: error.message });

    // 切换到下一个账户
    switchToNextAccount();

    // 延迟后继续
    setTimeout(runAutomation, config.MIN_CHAT_DELAY);
  }
}

function switchToNextAccount() {
  currentTokenIndex = (currentTokenIndex + 1) % allTokens.length;
  log(`切换到账户 ${currentTokenIndex + 1}/${allTokens.length}`, "info");
}

async function manualSwitchAccount() {
  if (allTokens.length <= 1) {
    log("只有一个账户，无法切换", "warning");
    return false;
  }

  switchToNextAccount();
  log(`已手动切换到账户 ${currentTokenIndex + 1}/${allTokens.length}`, "success");

  // 更新状态
  try {
    const currentToken = allTokens[currentTokenIndex];
    auth.setCurrentToken(currentToken);

    const userInfo = await auth.getUserInfo();
    if (userInfo) {
      const walletAddress = userInfo.wallet_address || "未知钱包";
      log(`账户 ${currentTokenIndex + 1}/${allTokens.length} - ${walletAddress.substring(0, 8)}...`, "info");

      // 如果启用了钱包代理绑定，设置当前钱包地址
      if (config.ENABLE_WALLET_PROXY_BINDING && userInfo.wallet_address) {
        auth.setCurrentWalletAddress(userInfo.wallet_address);
      }
    }
  } catch (error) {
    log(`获取用户信息失败: ${error.message}`, "error");
  }

  return true;
}

module.exports = {
  initAutomation,
  startAutomation,
  pauseAutomation,
  resumeAutomation,
  getRunningState,
  manualSwitchAccount,
};
