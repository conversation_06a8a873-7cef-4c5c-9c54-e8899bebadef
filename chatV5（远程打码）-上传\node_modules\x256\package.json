{"name": "x256", "description": "find the nearest xterm 256 color index for an rgb", "version": "0.0.2", "repository": {"type": "git", "url": "git://github.com/substack/node-x256.git"}, "main": "index.js", "keywords": ["256", "terminal", "colors", "xterm", "ansi"], "directories": {"test": "test"}, "scripts": {"test": "tape test/*.js"}, "devDependencies": {"tape": "^2.13.3"}, "engines": {"node": ">=0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/15", "firefox/latest", "firefox/nightly", "chrome/15", "chrome/latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}}