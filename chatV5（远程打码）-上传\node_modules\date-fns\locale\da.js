import { formatDistance } from "./da/_lib/formatDistance.js";
import { formatLong } from "./da/_lib/formatLong.js";
import { formatRelative } from "./da/_lib/formatRelative.js";
import { localize } from "./da/_lib/localize.js";
import { match } from "./da/_lib/match.js";

/**
 * @category Locales
 * @summary Danish locale.
 * @language Danish
 * @iso-639-2 dan
 * <AUTHOR> [@MathiasKandelborg](https://github.com/MathiasKandelborg)
 * <AUTHOR> [@Andersbiha](https://github.com/Andersbiha)
 * <AUTHOR>
 * <AUTHOR>
 */
export const da = {
  code: "da",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default da;
