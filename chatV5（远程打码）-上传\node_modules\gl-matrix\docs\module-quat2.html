<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>JSDoc: Module: quat2</title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">
</head>

<body>

<div id="main">

    <h1 class="page-title">Module: quat2</h1>

    




<section>

<header>
    
        
            
        
    
</header>

<article>
    <div class="container-overview">
    
        
            <div class="description">Dual Quaternion<br>
Format: [real, dual]<br>
Quaternion format: XYZW<br>
Make sure to have normalized dual quaternions, otherwise the functions may not work as intended.<br></div>
        

        
            















<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line5">line 5</a>
    </li></ul></dd>
    

    

    

    
</dl>


















        
    
    </div>

    

    

    

    

    

    

    
        <h3 class="subsection-title">Members</h3>

        
            
<h4 class="name" id=".getReal"><span class="type-signature">(static, constant) </span>getReal<span class="type-signature"></span></h4>




<div class="description">
    Gets the real part of a dual quat
</div>







<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line268">line 268</a>
    </li></ul></dd>
    

    

    

    
</dl>






        
    

    
        <h3 class="subsection-title">Methods</h3>

        
            

    

    
    <h4 class="name" id=".add"><span class="type-signature">(static) </span>add<span class="signature">(out, a, b)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Adds two dual quat's
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the receiving dual quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line580">line 580</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".clone"><span class="type-signature">(static) </span>clone<span class="signature">(a)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Creates a new quat initialized with values from an existing quaternion
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">dual quaternion to clone</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line41">line 41</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    new dual quaternion
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".conjugate"><span class="type-signature">(static) </span>conjugate<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Calculates the conjugate of a dual quat
If the dual quaternion is normalized, this function is faster than quat2.inverse and produces the same result.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the receiving quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">quat to calculate conjugate of</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line719">line 719</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".copy"><span class="type-signature">(static) </span>copy<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Copy the values from one dual quat to another
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the receiving dual quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the source dual quaternion</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line204">line 204</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".create"><span class="type-signature">(static) </span>create<span class="signature">()</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Creates a new identity dual quat
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line19">line 19</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    a new dual quaternion [real -> rotation, dual -> translation]
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".dot"><span class="type-signature">(static) </span>dot<span class="signature">(a, b)</span><span class="type-signature"> &rarr; {Number}</span></h4>
    

    



<div class="description">
    Calculates the dot product of two dual quat's (The dot product of the real parts)
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line663">line 663</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    dot product of a and b
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".equals"><span class="type-signature">(static) </span>equals<span class="signature">(a, b)</span><span class="type-signature"> &rarr; {Boolean}</span></h4>
    

    



<div class="description">
    Returns whether or not the dual quaternions have approximately the same elements in the same position.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the first dual quat.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the second dual quat.</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line829">line 829</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    true if the dual quats are equal, false otherwise.
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".exactEquals"><span class="type-signature">(static) </span>exactEquals<span class="signature">(a, b)</span><span class="type-signature"> &rarr; {Boolean}</span></h4>
    

    



<div class="description">
    Returns whether or not the dual quaternions have exactly the same elements in the same position (when compared with ===)
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the first dual quaternion.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the second dual quaternion.</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line817">line 817</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    true if the dual quaternions are equal, false otherwise.
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromMat4"><span class="type-signature">(static) </span>fromMat4<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Creates a new dual quat from a matrix (4x4)
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the dual quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">mat4</span>


            
            </td>

            

            

            <td class="description last">the matrix</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line186">line 186</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    dual quat receiving operation result
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromRotation"><span class="type-signature">(static) </span>fromRotation<span class="signature">(dual, q)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Creates a dual quat from a quaternion
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>dual</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">quaternion receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>q</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">the quaternion</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line166">line 166</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    dual quaternion receiving operation result
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromRotationTranslation"><span class="type-signature">(static) </span>fromRotationTranslation<span class="signature">(dual, q, t)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Creates a dual quat from a quaternion and a translation
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>dual</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">quaternion receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>q</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>t</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">tranlation vector</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line119">line 119</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    dual quaternion receiving operation result
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromRotationTranslationValues"><span class="type-signature">(static) </span>fromRotationTranslationValues<span class="signature">(x1, y1, z1, w1, x2, y2, z2)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Creates a new dual quat from the given values (quat and translation)
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>x1</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">X component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>y1</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Y component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>z1</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Z component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>w1</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">W component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>x2</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">X component (translation)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>y2</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Y component (translation)</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>z2</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Z component (translation)</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line94">line 94</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    new dual quaternion
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromTranslation"><span class="type-signature">(static) </span>fromTranslation<span class="signature">(dual, t)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Creates a dual quat from a translation
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>dual</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">quaternion receiving operation result</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>t</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">translation vector</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line146">line 146</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    dual quaternion receiving operation result
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".fromValues"><span class="type-signature">(static) </span>fromValues<span class="signature">(x1, y1, z1, w1, x2, y2, z2, w2)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Creates a new dual quat initialized with the given values
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>x1</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">X component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>y1</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Y component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>z1</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Z component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>w1</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">W component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>x2</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">X component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>y2</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Y component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>z2</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Z component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>w2</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">W component</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line68">line 68</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    new dual quaternion
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".getDual"><span class="type-signature">(static) </span>getDual<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {quat}</span></h4>
    

    



<div class="description">
    Gets the dual part of a dual quat
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">dual part</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">Dual Quaternion</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line276">line 276</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    dual part
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".getTranslation"><span class="type-signature">(static) </span>getTranslation<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {vec3}</span></h4>
    

    



<div class="description">
    Gets the translation of a normalized dual quat
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">translation</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">Dual Quaternion to be decomposed</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line316">line 316</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    translation
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">vec3</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".identity"><span class="type-signature">(static) </span>identity<span class="signature">(out)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Set a dual quat to the identity dual quaternion
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the receiving quaternion</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line222">line 222</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".invert"><span class="type-signature">(static) </span>invert<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Calculates the inverse of a dual quat. If they are normalized, conjugate is cheaper
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the receiving dual quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">dual quat to calculate inverse of</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line698">line 698</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".len"><span class="type-signature">(static) </span>len<span class="signature">()</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    Alias for quat2.length
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line744">line 744</a>
    </li></ul></dd>
    

    

    

    
</dl>


















        
            

    

    
    <h4 class="name" id=".length"><span class="type-signature">(static) </span>length<span class="signature">(a)</span><span class="type-signature"> &rarr; {Number}</span></h4>
    

    



<div class="description">
    Calculates the length of a dual quat
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">dual quat to calculate length of</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line738">line 738</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    length of a
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".lerp"><span class="type-signature">(static) </span>lerp<span class="signature">(out, a, b, t)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Performs a linear interpolation between two dual quats's
NOTE: The resulting dual quaternions won't always be normalized (The error is most noticeable when t = 0.5)
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the receiving dual quat</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>t</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">interpolation amount, in the range [0-1], between the two inputs</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line675">line 675</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".mul"><span class="type-signature">(static) </span>mul<span class="signature">()</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    Alias for quat2.multiply
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line632">line 632</a>
    </li></ul></dd>
    

    

    

    
</dl>


















        
            

    

    
    <h4 class="name" id=".multiply"><span class="type-signature">(static) </span>multiply<span class="signature">(out, a, b)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Multiplies two dual quat's
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the receiving dual quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the first operand</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the second operand</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line600">line 600</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".normalize"><span class="type-signature">(static) </span>normalize<span class="signature">(out, a)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Normalize a dual quat
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the receiving dual quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">dual quaternion to normalize</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line769">line 769</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".rotateAroundAxis"><span class="type-signature">(static) </span>rotateAroundAxis<span class="signature">(out, a, axis, rad)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Rotates a dual quat around a given axis. Does the normalisation automatically
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the receiving dual quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the dual quaternion to rotate</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>axis</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">the axis to rotate around</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rad</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">how far the rotation should be</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line536">line 536</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".rotateByQuatAppend"><span class="type-signature">(static) </span>rotateByQuatAppend<span class="signature">(out, a, q)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Rotates a dual quat by a given quaternion (a * q)
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the receiving dual quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the dual quaternion to rotate</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>q</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">quaternion to rotate by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line469">line 469</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".rotateByQuatPrepend"><span class="type-signature">(static) </span>rotateByQuatPrepend<span class="signature">(out, q, a)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Rotates a dual quat by a given quaternion (q * a)
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the receiving dual quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>q</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">quaternion to rotate by</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the dual quaternion to rotate</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line502">line 502</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".rotateX"><span class="type-signature">(static) </span>rotateX<span class="signature">(out, a, rad)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Rotates a dual quat around the X axis
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the receiving dual quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the dual quaternion to rotate</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rad</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last">how far should the rotation be</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line370">line 370</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".rotateY"><span class="type-signature">(static) </span>rotateY<span class="signature">(out, a, rad)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Rotates a dual quat around the Y axis
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the receiving dual quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the dual quaternion to rotate</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rad</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last">how far should the rotation be</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line403">line 403</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".rotateZ"><span class="type-signature">(static) </span>rotateZ<span class="signature">(out, a, rad)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Rotates a dual quat around the Z axis
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the receiving dual quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the dual quaternion to rotate</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rad</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last">how far should the rotation be</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line436">line 436</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".scale"><span class="type-signature">(static) </span>scale<span class="signature">(out, a, b)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Scales a dual quat by a scalar number
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the receiving dual quat</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the dual quat to scale</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">amount to scale the dual quat by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line643">line 643</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".set"><span class="type-signature">(static) </span>set<span class="signature">(out, x1, y1, z1, w1, x2, y2, z2, w2)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Set the components of a dual quat to the given values
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the receiving quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>x1</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">X component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>y1</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Y component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>z1</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Z component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>w1</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">W component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>x2</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">X component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>y2</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Y component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>z2</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">Z component</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>w2</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last">W component</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line249">line 249</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".setDual"><span class="type-signature">(static) </span>setDual<span class="signature">(out, q)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Set the dual component of a dual quat to the given quaternion
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the receiving quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>q</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">a quaternion representing the dual part</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line302">line 302</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".setReal"><span class="type-signature">(static) </span>setReal<span class="signature">(out, q)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Set the real component of a dual quat to the given quaternion
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the receiving quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>q</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat</span>


            
            </td>

            

            

            <td class="description last">a quaternion representing the real part</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line292">line 292</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".sqrLen"><span class="type-signature">(static) </span>sqrLen<span class="signature">()</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    Alias for quat2.squaredLength
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line759">line 759</a>
    </li></ul></dd>
    

    

    

    
</dl>


















        
            

    

    
    <h4 class="name" id=".squaredLength"><span class="type-signature">(static) </span>squaredLength<span class="signature">(a)</span><span class="type-signature"> &rarr; {Number}</span></h4>
    

    



<div class="description">
    Calculates the squared length of a dual quat
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">dual quat to calculate squared length of</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line753">line 753</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    squared length of a
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".str"><span class="type-signature">(static) </span>str<span class="signature">(a)</span><span class="type-signature"> &rarr; {String}</span></h4>
    

    



<div class="description">
    Returns a string representation of a dual quatenion
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">dual quaternion to represent as a string</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line805">line 805</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    string representation of the dual quat
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>


    </dd>
</dl>

    





        
            

    

    
    <h4 class="name" id=".translate"><span class="type-signature">(static) </span>translate<span class="signature">(out, a, v)</span><span class="type-signature"> &rarr; {quat2}</span></h4>
    

    



<div class="description">
    Translates a dual quat by the given vector
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>out</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the receiving dual quaternion</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">quat2</span>


            
            </td>

            

            

            <td class="description last">the dual quaternion to translate</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>v</code></td>
            

            <td class="type">
            
                
<span class="param-type">vec3</span>


            
            </td>

            

            

            <td class="description last">vector to translate by</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="quat2.js.html">quat2.js</a>, <a href="quat2.js.html#line339">line 339</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    out
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">quat2</span>


    </dd>
</dl>

    





        
    

    

    
</article>

</section>




</div>

<nav>
    <h2><a href="index.html">Home</a></h2><h3>Modules</h3><ul><li><a href="module-glMatrix.html">glMatrix</a></li><li><a href="module-mat2.html">mat2</a></li><li><a href="module-mat2d.html">mat2d</a></li><li><a href="module-mat3.html">mat3</a></li><li><a href="module-mat4.html">mat4</a></li><li><a href="module-quat.html">quat</a></li><li><a href="module-quat2.html">quat2</a></li><li><a href="module-vec2.html">vec2</a></li><li><a href="module-vec3.html">vec3</a></li><li><a href="module-vec4.html">vec4</a></li></ul>
</nav>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.5.5</a> on Fri Jul 13 2018 11:51:34 GMT+0200 (W. Europe Daylight Time)
</footer>

<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>
</body>
</html>