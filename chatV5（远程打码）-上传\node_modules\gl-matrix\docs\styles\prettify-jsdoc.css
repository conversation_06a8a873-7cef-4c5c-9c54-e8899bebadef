/* JSDoc prettify.js theme */

/* plain text */
.pln {
  color: #000000;
  font-weight: normal;
  font-style: normal;
}

/* string content */
.str {
  color: #006400;
  font-weight: normal;
  font-style: normal;
}

/* a keyword */
.kwd {
  color: #000000;
  font-weight: bold;
  font-style: normal;
}

/* a comment */
.com {
  font-weight: normal;
  font-style: italic;
}

/* a type name */
.typ {
  color: #000000;
  font-weight: normal;
  font-style: normal;
}

/* a literal value */
.lit {
  color: #006400;
  font-weight: normal;
  font-style: normal;
}

/* punctuation */
.pun {
  color: #000000;
  font-weight: bold;
  font-style: normal;
}

/* lisp open bracket */
.opn {
  color: #000000;
  font-weight: bold;
  font-style: normal;
}

/* lisp close bracket */
.clo {
  color: #000000;
  font-weight: bold;
  font-style: normal;
}

/* a markup tag name */
.tag {
  color: #006400;
  font-weight: normal;
  font-style: normal;
}

/* a markup attribute name */
.atn {
  color: #006400;
  font-weight: normal;
  font-style: normal;
}

/* a markup attribute value */
.atv {
  color: #006400;
  font-weight: normal;
  font-style: normal;
}

/* a declaration */
.dec {
  color: #000000;
  font-weight: bold;
  font-style: normal;
}

/* a variable name */
.var {
  color: #000000;
  font-weight: normal;
  font-style: normal;
}

/* a function name */
.fun {
  color: #000000;
  font-weight: bold;
  font-style: normal;
}

/* Specify class=linenums on a pre to get line numbering */
ol.linenums {
  margin-top: 0;
  margin-bottom: 0;
}
