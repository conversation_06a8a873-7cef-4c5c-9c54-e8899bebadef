{"version": 3, "file": "contract.d.ts", "sourceRoot": "", "sources": ["../../src.ts/contract/contract.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAS,MAAM,iBAAiB,CAAC;AAInD,OAAO,EAAe,GAAG,EAAE,mBAAmB,EAAE,MAAM,0BAA0B,CAAC;AAMjF,OAAO,EAEH,2BAA2B,EAC3B,QAAQ,EACX,MAAM,eAAe,CAAC;AAEvB,OAAO,KAAK,EAAE,aAAa,EAAE,gBAAgB,EAAE,YAAY,EAAE,SAAS,EAAU,MAAM,iBAAiB,CAAC;AACxG,OAAO,KAAK,EAAE,WAAW,EAAgB,MAAM,qBAAqB,CAAC;AACrE,OAAO,KAAK,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AACpE,OAAO,KAAK,EACR,QAAQ,EAAE,cAAc,EAC3B,MAAM,uBAAuB,CAAC;AAE/B,OAAO,KAAK,EAER,iBAAiB,EACjB,iBAAiB,EAEjB,cAAc,EAEd,aAAa,EACb,mBAAmB,EAEnB,eAAe,EAClB,MAAM,YAAY,CAAC;AAwGpB;;GAEG;AACH,wBAAsB,aAAa,CAAC,CAAC,SAAS,MAAM,GAAG,MAAM,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC,CAkB9I;AAED;;GAEG;AACH,wBAAsB,WAAW,CAAC,OAAO,EAAE,IAAI,GAAG,cAAc,EAAE,MAAM,EAAE,aAAa,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAWzI;AAuQD,QAAA,MAAM,QAAQ,eAAyC,CAAC;AA0MxD,qBAAa,YAAa,YAAW,WAAW,EAAE,gBAAgB,CAAC,iBAAiB,CAAC;IACjF;;;;;;OAMG;IACH,QAAQ,CAAC,MAAM,EAAG,MAAM,GAAG,WAAW,CAAC;IAEvC;;OAEG;IACH,QAAQ,CAAC,SAAS,EAAG,SAAS,CAAC;IAE/B;;;;;;OAMG;IACH,QAAQ,CAAC,MAAM,EAAG,IAAI,GAAG,cAAc,CAAC;IAExC;;OAEG;IACH,QAAQ,CAAC,OAAO,EAAG,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IAEjD;;OAEG;IACH,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC;IAEzB;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAG,IAAI,GAAG,eAAe,CAAC;IAE3C;;;;OAIG;gBACS,MAAM,EAAE,MAAM,GAAG,WAAW,EAAE,GAAG,EAAE,SAAS,GAAG,YAAY,EAAE,MAAM,CAAC,EAAE,IAAI,GAAG,cAAc,EAAE,SAAS,CAAC,EAAE,IAAI,GAAG,mBAAmB;IAwH/I;;;OAGG;IACH,OAAO,CAAC,MAAM,EAAE,IAAI,GAAG,cAAc,GAAG,YAAY;IAIpD;;;OAGG;IACH,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,WAAW,GAAG,YAAY;IAIlD;;OAEG;IACG,UAAU,IAAI,OAAO,CAAC,MAAM,CAAC;IAEnC;;OAEG;IACG,eAAe,IAAI,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;IAU/C;;;OAGG;IACG,iBAAiB,IAAI,OAAO,CAAC,IAAI,CAAC;IA+BxC;;;;;OAKG;IACH,qBAAqB,IAAI,IAAI,GAAG,2BAA2B;IAI3D;;;;OAIG;IACH,WAAW,CAAC,CAAC,SAAS,cAAc,GAAG,cAAc,EAAE,GAAG,EAAE,MAAM,GAAG,gBAAgB,GAAG,CAAC;IAMzF;;;;OAIG;IACH,QAAQ,CAAC,GAAG,EAAE,MAAM,GAAG,aAAa,GAAG,aAAa;IAKpD;;OAEG;IACG,gBAAgB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAoB9D;;;;OAIG;IACG,WAAW,CAAC,KAAK,EAAE,iBAAiB,EAAE,SAAS,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,CAAC;IAgCrH;;OAEG;IACG,EAAE,CAAC,KAAK,EAAE,iBAAiB,EAAE,QAAQ,EAAE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAOrE;;;OAGG;IACG,IAAI,CAAC,KAAK,EAAE,iBAAiB,EAAE,QAAQ,EAAE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAOvE;;;;OAIG;IACG,IAAI,CAAC,KAAK,EAAE,iBAAiB,EAAE,GAAG,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;IAI3E;;;OAGG;IACG,aAAa,CAAC,KAAK,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC;IAgB/D;;;OAGG;IACG,SAAS,CAAC,KAAK,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAgBpE;;;OAGG;IACG,GAAG,CAAC,KAAK,EAAE,iBAAiB,EAAE,QAAQ,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAiBvE;;;OAGG;IACG,kBAAkB,CAAC,KAAK,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;IAiBlE;;OAEG;IACG,WAAW,CAAC,KAAK,EAAE,iBAAiB,EAAE,QAAQ,EAAE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAI9E;;OAEG;IACG,cAAc,CAAC,KAAK,EAAE,iBAAiB,EAAE,QAAQ,EAAE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAIjF;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,iBAAiB,EAAE,GAAG,EAAE,SAAS,GAAG,YAAY,GAAG,KAAK,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,IAAI,GAAG,cAAc,KAAK,YAAY,GAAG,IAAI,CAAC,CAAC,EAAE,MAAM,YAAY,CAAC;IAS3K;;OAEG;IACH,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,iBAAiB,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,SAAS,GAAG,YAAY,EAAE,MAAM,CAAC,EAAE,IAAI,GAAG,cAAc,GAAG,YAAY,GAAG,IAAI,CAAC,CAAC,EAAE,MAAM,YAAY,CAAC;CAKhK;;AAMD;;GAEG;AACH,qBAAa,QAAS,SAAQ,aAAe;CAAI"}