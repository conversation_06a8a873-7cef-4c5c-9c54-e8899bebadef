<!DOCTYPE html>

<!--originally from:

http://geeksretreat.wordpress.com/2012/04/26/html5s-canvas-lets-draw-the-world/
-->

<html lang="en">
   <head>
      <meta charset="utf-8" />
      <title>Canvas Map Plotting</title>
      <script src='map.js'></script>
      <script>
         function showMap() {
            /*
            var options = { excludeAntartica: true
                          , disableBackground: true
                          , disableMapBackground: true
                          , disableGraticule: true
                          , disableFill: true }
            */
            var options = {}
            var map = new Map(options, document.getElementById("map"));            
            map.draw()
            
            map.addMarker( {"lon" : "-122", "lat" : "37" })
            
         }
      </script>
   </head>
   <body onload='showMap()'>
      <div>
         <canvas id="map" width="1580" height="790"></canvas>
      </div>
   </body>
</html>