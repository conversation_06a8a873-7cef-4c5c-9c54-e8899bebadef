{"version": 3, "file": "wallet.js", "sourceRoot": "", "sources": ["../../src.ts/wallet/wallet.ts"], "names": [], "mappings": ";;;AAAA,iDAAgD;AAChD,gDAAmD;AAEnD,qDAA8C;AAC9C,+CAA6C;AAC7C,2DAA6E;AAC7E,yDAI4B;AAC5B,+CAAyC;AASzC,SAAS,KAAK,CAAC,QAAgB;IAC3B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrF,CAAC;AAED;;;;;;;;;GASG;AACH,MAAa,MAAO,SAAQ,2BAAU;IAElC;;;OAGG;IACH,YAAY,GAAwB,EAAE,QAA0B;QAC5D,IAAI,OAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YACnD,GAAG,GAAG,IAAI,GAAG,GAAG,CAAC;SACpB;QAED,IAAI,UAAU,GAAG,CAAC,OAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,qBAAU,CAAC,GAAG,CAAC,CAAA,CAAC,CAAC,GAAG,CAAC;QACvE,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IAChC,CAAC;IAED,OAAO,CAAC,QAAyB;QAC7B,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACjD,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,OAAO,CAAC,QAA6B,EAAE,gBAAmC;QAC5E,MAAM,OAAO,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC;QACvE,OAAO,MAAM,IAAA,sCAAmB,EAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC9E,CAAC;IAED;;;;;;;;;OASG;IACH,WAAW,CAAC,QAA6B;QACrC,MAAM,OAAO,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC;QACvE,OAAO,IAAA,0CAAuB,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IACtD,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,OAAkD;QAClE,IAAA,yBAAc,EAAC,OAAO,EAAE,qBAAqB,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;QAEvE,IAAI,UAAU,IAAI,OAAO,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,KAAK,IAAI,EAAE;YAC/E,MAAM,QAAQ,GAAG,sBAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAChE,MAAM,MAAM,GAAG,0BAAY,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC1E,IAAI,MAAM,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,IAAI,MAAM,CAAC,UAAU,KAAK,OAAO,CAAC,UAAU,EAAE;gBAChF,OAAO,MAAM,CAAC;aACjB;YACD,OAAO,CAAC,GAAG,CAAC,kFAAkF,CAAC,CAAC;SACnG;QAED,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAE9C,IAAA,yBAAc,EAAC,MAAM,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,EAC7C,6BAA6B,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;QAE3D,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAY,EAAE,QAA6B,EAAE,QAA2B;QACnG,IAAI,OAAO,GAA8C,IAAI,CAAC;QAC9D,IAAI,IAAA,iCAAc,EAAC,IAAI,CAAC,EAAE;YACtB,OAAO,GAAG,MAAM,IAAA,sCAAmB,EAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;SAEjE;aAAM,IAAI,IAAA,mCAAe,EAAC,IAAI,CAAC,EAAE;YAC9B,IAAI,QAAQ,EAAE;gBAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;aAAE;YAC9C,OAAO,GAAG,IAAA,wCAAoB,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC/C,IAAI,QAAQ,EAAE;gBAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;aAAE;SAEjD;QAED,OAAO,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,qBAAqB,CAAC,IAAY,EAAE,QAA6B;QACpE,IAAI,OAAO,GAA8C,IAAI,CAAC;QAC9D,IAAI,IAAA,iCAAc,EAAC,IAAI,CAAC,EAAE;YACtB,OAAO,GAAG,IAAA,0CAAuB,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SACrD;aAAM,IAAI,IAAA,mCAAe,EAAC,IAAI,CAAC,EAAE;YAC9B,OAAO,GAAG,IAAA,wCAAoB,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SAClD;aAAM;YACH,IAAA,yBAAc,EAAC,KAAK,EAAE,qBAAqB,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;SACxE;QAED,OAAO,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,YAAY,CAAC,QAA0B;QAC1C,MAAM,MAAM,GAAG,0BAAY,CAAC,YAAY,EAAE,CAAC;QAC3C,IAAI,QAAQ,EAAE;YAAE,OAAO,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAAE;QAClD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,MAAc,EAAE,QAAmB;QACjD,MAAM,MAAM,GAAG,0BAAY,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC/C,IAAI,QAAQ,EAAE;YAAE,OAAO,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAAE;QAClD,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ;AAhID,wBAgIC"}