const axios = require('axios');
const config = require('../../config');
const { log, logToFile } = require('../utils');

/**
 * 2Captcha服务 - 用于解决Cloudflare Turnstile验证
 * 文档: https://2captcha.com/api-docs/cloudflare-turnstile
 */
class TwoCaptchaService {
  constructor() {
    this.apiKey = config.TWOCAPTCHA_API_KEY || '';
    this.websiteURL = config.TWOCAPTCHA_WEBSITE_URL || 'https://klokapp.ai/';
    this.websiteKey = config.TWOCAPTCHA_WEBSITE_KEY || '';
    this.baseUrl = 'https://2captcha.com';
  }

  /**
   * 创建Cloudflare Turnstile验证任务并获取任务ID
   * @returns {Promise<string>} 任务ID
   */
  async createTask() {
    try {
      log(`[2Captcha] 创建Cloudflare Turnstile任务...`, 'info');
      logToFile(`创建2Captcha任务`, {
        websiteURL: this.websiteURL,
        websiteKey: this.websiteKey
      });

      // 构建请求参数
      const params = {
        key: this.apiKey,
        method: 'turnstile',
        sitekey: this.websiteKey,
        pageurl: this.websiteURL,
        json: 1
      };

      // 发送请求 - 使用正确的端点 in.php
      const response = await axios.get(`${this.baseUrl}/in.php`, {
        params: params
      });
      
      // 记录请求信息到日志
      logToFile('2Captcha请求详情', {
        apiKey: this.apiKey.substring(0, 10) + '...',
        method: 'turnstile',
        url: `${this.baseUrl}/in.php`
      });

      if (response.data && response.data.status === 1) {
        log(`[2Captcha] 任务创建成功，ID: ${response.data.request}`, 'success');
        return response.data.request;
      } else {
        throw new Error(`创建任务失败: ${JSON.stringify(response.data)}`);
      }
    } catch (error) {
      log(`[2Captcha] 创建任务失败: ${error.message}`, 'error');
      logToFile(`2Captcha创建任务失败`, { error: error.message });
      throw error;
    }
  }

  /**
   * 获取任务结果
   * @param {string} taskId 任务ID
   * @returns {Promise<string>} 验证码token
   */
  async getTaskResult(taskId) {
    try {
      log(`[2Captcha] 获取任务结果: ${taskId}`, 'info');
      
      // 最多尝试30次，每次间隔5秒
      let attempts = 0;
      const maxAttempts = 30;
      const interval = 5000; // 增加间隔时间，给2Captcha更多处理时间

      while (attempts < maxAttempts) {
        attempts++;
        
        // 构建请求参数
        const params = {
          key: this.apiKey,
          action: 'get',
          id: taskId,
          json: 1
        };

        const response = await axios.get(`${this.baseUrl}/res.php`, {
          params: params
        });

        if (response.data && response.data.status === 1) {
          const token = response.data.request;
          log(`[2Captcha] 成功获取token (尝试${attempts}次)`, 'success');
          logToFile(`2Captcha获取token成功`, { 
            attempts,
            tokenLength: token.length,
            tokenPreview: token.substring(0, 20) + '...'
          });
          return token;
        } else if (response.data && response.data.request === 'CAPCHA_NOT_READY') {
          log(`[2Captcha] 任务处理中，等待中... (${attempts}/${maxAttempts})`, 'info');
          await new Promise(resolve => setTimeout(resolve, interval));
        } else {
          throw new Error(`获取结果失败: ${JSON.stringify(response.data)}`);
        }
      }

      throw new Error(`获取任务结果超时，尝试次数: ${maxAttempts}`);
    } catch (error) {
      log(`[2Captcha] 获取任务结果失败: ${error.message}`, 'error');
      logToFile(`2Captcha获取任务结果失败`, { error: error.message });
      throw error;
    }
  }

  /**
   * 获取Cloudflare Turnstile Token
   * @returns {Promise<string>} 验证码token
   */
  async getTurnstileToken() {
    try {
      if (!this.apiKey) {
        throw new Error('缺少2Captcha API密钥，请在config.js中配置TWOCAPTCHA_API_KEY');
      }
      
      if (!this.websiteKey) {
        throw new Error('缺少Cloudflare Turnstile sitekey，请在config.js中配置TWOCAPTCHA_WEBSITE_KEY');
      }

      log(`[2Captcha] 开始获取Turnstile Token，使用sitekey: ${this.websiteKey}`, 'info');
      const taskId = await this.createTask();
      const token = await this.getTaskResult(taskId);
      return token;
    } catch (error) {
      log(`[2Captcha] 获取Turnstile Token失败: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * 获取账户余额
   * @returns {Promise<number>} 账户余额
   */
  async getBalance() {
    try {
      log(`[2Captcha] 获取账户余额...`, 'info');
      const params = {
        key: this.apiKey,
        action: 'getbalance',
        json: 1
      };

      const response = await axios.get(`${this.baseUrl}/res.php`, {
        params: params
      });

      if (response.data && response.data.status === 1) {
        log(`[2Captcha] 当前账户余额: ${response.data.request}`, 'success');
        return response.data.request;
      } else {
        throw new Error(`获取余额失败: ${JSON.stringify(response.data)}`);
      }
    } catch (error) {
      log(`[2Captcha] 获取账户余额失败: ${error.message}`, 'error');
      return -1;
    }
  }
}

// 导出单例
const twoCaptchaService = new TwoCaptchaService();
module.exports = twoCaptchaService;
