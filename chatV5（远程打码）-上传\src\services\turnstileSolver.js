/**
 * 本地Turnstile-Solver服务
 * 负责与本地部署的Turnstile-Solver服务通信，获取Cloudflare Turnstile验证码token
 */
const axios = require('axios');
const config = require('../../config');
const { log, logToFile } = require('../utils/simple-logger');

/**
 * 本地Turnstile-Solver服务类
 */
class TurnstileSolverService {
  constructor() {
    this.apiUrl = config.LOCAL_TURNSTILE_SOLVER_URL || 'http://localhost:5000';
    this.websiteURL = config.LOCAL_TURNSTILE_WEBSITE_URL || 'https://klokapp.ai/';
    this.websiteKey = config.LOCAL_TURNSTILE_WEBSITE_KEY || '0x4AAAAAABdQypM3HkDQTuaO';
  }

  /**
   * 创建Cloudflare Turnstile验证任务并获取任务ID
   * @returns {Promise<string>} 任务ID
   */
  async createTask() {
    try {
      log(`[本地Turnstile-Solver] 创建Cloudflare Turnstile任务...`, 'info');
      logToFile(`创建本地Turnstile-Solver任务`, {
        websiteURL: this.websiteURL,
        websiteKey: this.websiteKey
      });

      // 构建请求URL
      const requestUrl = `${this.apiUrl}/turnstile?url=${encodeURIComponent(this.websiteURL)}&sitekey=${encodeURIComponent(this.websiteKey)}&action=login`;
      
      log(`[本地Turnstile-Solver] 请求URL: ${requestUrl}`, 'info');

      // 发送请求
      const response = await axios.get(requestUrl, {
        timeout: 30000
      });
      
      // 记录请求信息到日志
      logToFile('本地Turnstile-Solver请求详情', {
        url: requestUrl,
        response: response.data
      });

      if (response.data && response.data.task_id) {
        log(`[本地Turnstile-Solver] 任务创建成功，ID: ${response.data.task_id}`, 'success');
        return response.data.task_id;
      } else {
        throw new Error(`创建任务失败: ${JSON.stringify(response.data)}`);
      }
    } catch (error) {
      const errorMessage = error.response ? 
        `${error.message}: ${JSON.stringify(error.response.data || {})}` : 
        error.message;
      
      log(`[本地Turnstile-Solver] 创建任务失败: ${errorMessage}`, 'error');
      logToFile(`本地Turnstile-Solver创建任务失败`, { 
        error: errorMessage,
        url: this.apiUrl,
        sitekey: this.websiteKey,
        pageurl: this.websiteURL
      });
      throw new Error(errorMessage);
    }
  }

  /**
   * 获取任务结果
   * @param {string} taskId 任务ID
   * @returns {Promise<string>} 验证码token
   */
  async getTaskResult(taskId) {
    try {
      log(`[本地Turnstile-Solver] 获取任务结果: ${taskId}`, 'info');
      
      // 最多尝试30次，每次间隔3秒
      let attempts = 0;
      const maxAttempts = 30;
      const interval = 3000;

      while (attempts < maxAttempts) {
        attempts++;
        
        // 构建请求URL
        const requestUrl = `${this.apiUrl}/result?id=${encodeURIComponent(taskId)}`;

        log(`[本地Turnstile-Solver] 请求结果URL: ${requestUrl} (尝试${attempts}/${maxAttempts})`, 'info');

        try {
          const response = await axios.get(requestUrl, {
            timeout: 10000
          });

          // 如果有value字段，说明验证码已解决
          if (response.data && response.data.value) {
            const token = response.data.value;
            const elapsedTime = response.data.elapsed_time || 0;
            
            log(`[本地Turnstile-Solver] 成功获取token (尝试${attempts}次, 耗时${elapsedTime}秒)`, 'success');
            logToFile(`本地Turnstile-Solver获取token成功`, { 
              attempts,
              elapsedTime,
              tokenLength: token.length,
              tokenPreview: token.substring(0, 20) + '...'
            });
            return token;
          } else if (response.data && response.data.status === "error") {
            // 如果返回错误状态
            throw new Error(`任务失败: ${JSON.stringify(response.data)}`);
          } else {
            // 任务还在处理中
            log(`[本地Turnstile-Solver] 任务处理中，等待中... (${attempts}/${maxAttempts})`, 'info');
            await new Promise(resolve => setTimeout(resolve, interval));
          }
        } catch (requestError) {
          // 请求错误，等待后重试
          log(`[本地Turnstile-Solver] 请求失败，将重试: ${requestError.message}`, 'warning');
          await new Promise(resolve => setTimeout(resolve, interval));
        }
      }

      throw new Error(`获取任务结果超时，尝试次数: ${maxAttempts}`);
    } catch (error) {
      const errorMessage = error.response ? 
        `${error.message}: ${JSON.stringify(error.response.data || {})}` : 
        error.message;
      
      log(`[本地Turnstile-Solver] 获取任务结果失败: ${errorMessage}`, 'error');
      logToFile(`本地Turnstile-Solver获取任务结果失败`, { error: errorMessage });
      throw new Error(errorMessage);
    }
  }

  /**
   * 获取Cloudflare Turnstile Token
   * @returns {Promise<string>} 验证码token
   */
  async getTurnstileToken() {
    try {
      if (!this.websiteKey) {
        throw new Error('缺少Cloudflare Turnstile sitekey，请在config.js中配置LOCAL_TURNSTILE_WEBSITE_KEY');
      }

      log(`[本地Turnstile-Solver] 开始获取Turnstile Token，使用sitekey: ${this.websiteKey}`, 'info');
      const taskId = await this.createTask();
      const token = await this.getTaskResult(taskId);
      return token;
    } catch (error) {
      log(`[本地Turnstile-Solver] 获取Turnstile Token失败: ${error.message}`, 'error');
      throw error;
    }
  }
}

// 导出单例
const turnstileSolverService = new TurnstileSolverService();
module.exports = turnstileSolverService;
