import asyncio
from datetime import datetime, timedelta
from enum import Enum
from common.logger import get_logger,emoji
from datetime import datetime
logger = get_logger("task_manager")
task_pool = {}
TASK_TIMEOUT_SECONDS = 60 #任务超时时间
class Status(Enum):
    PENDING = "processing"
    WAITING = "idle"
    SUCCESS = "ready"
    TIMEOUT = "timeout"

async def cleanup_expired_tasks():
    """清理过期任务并释放worker资源"""
    while True:
        now = datetime.utcnow()
        expired_tasks = []

        for task_id, task in task_pool.items():
            if task['status'] != Status.SUCCESS:
                created_time = task['createdAt'] if isinstance(task['createdAt'], datetime) else datetime.fromisoformat(task['createdAt'])
                if (now - created_time).total_seconds() > TASK_TIMEOUT_SECONDS:
                    expired_tasks.append((task_id, task))

        # 处理过期任务
        for task_id, task in expired_tasks:
            logger.warning(emoji("TIMEOUT", f"任务 {task_id} 超时，正在清理"))

            # 释放worker资源
            assigned_worker_id = task.get('assignedTo')
            if assigned_worker_id:
                await release_worker_task(assigned_worker_id, task_id)

            # 标记任务为超时
            task['status'] = Status.TIMEOUT
            task['result'] = {"error": "任务超时"}
            task['errorId'] = -2

            logger.info(emoji("CLEANUP", f"已清理超时任务 {task_id}"))

        await asyncio.sleep(30)

async def release_worker_task(worker_id, task_id):
    """释放worker的任务计数"""
    try:
        from core.worker_manager import worker_pool
        if worker_id in worker_pool:
            worker = worker_pool[worker_id]
            if worker['current_tasks'] > 0:
                worker['current_tasks'] -= 1
                logger.info(emoji("RELEASE", f"释放worker {worker_id} 的任务计数，当前任务数: {worker['current_tasks']}"))
    except Exception as e:
        logger.error(emoji("ERROR", f"释放worker任务计数失败: {e}"))

def get_task_by_id(task_id):
    """根据任务ID获取任务"""
    return task_pool.get(task_id)

def update_task_status(task_id, status, result=None, error_id=0):
    """更新任务状态"""
    if task_id in task_pool:
        task_pool[task_id]['status'] = status
        if result is not None:
            task_pool[task_id]['result'] = result
        task_pool[task_id]['errorId'] = error_id
        return True
    return False

def get_pending_tasks_for_worker(worker_id):
    """获取分配给指定worker的待处理任务"""
    return [
        task for task in task_pool.values()
        if task.get('assignedTo') == worker_id and task['status'] == Status.PENDING
    ]
