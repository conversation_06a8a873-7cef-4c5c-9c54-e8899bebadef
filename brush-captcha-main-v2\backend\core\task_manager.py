import asyncio
from datetime import datetime, timezone, timedelta
from enum import Enum
from common.logger import get_logger, emoji
logger = get_logger("task_manager")
task_pool = {}
TASK_TIMEOUT_SECONDS = 60 #任务超时时间
class Status(Enum):
    PENDING = "processing"
    WAITING = "idle"
    SUCCESS = "ready"
    TIMEOUT = "timeout"
    FAILED = "failed"

async def cleanup_expired_tasks():
    """清理过期任务并释放worker资源"""
    while True:
        now = datetime.now(timezone.utc)
        expired_tasks = []
        completed_tasks_to_remove = []

        for task_id, task in task_pool.items():
            # 处理创建时间
            created_at = task.get('createdAt')
            if isinstance(created_at, datetime):
                # 确保时间是UTC时区
                if created_at.tzinfo is None:
                    created_time = created_at.replace(tzinfo=timezone.utc)
                else:
                    created_time = created_at.astimezone(timezone.utc)
            elif isinstance(created_at, str):
                try:
                    # 尝试ISO格式
                    created_time = datetime.fromisoformat(created_at)
                    if created_time.tzinfo is None:
                        created_time = created_time.replace(tzinfo=timezone.utc)
                except ValueError:
                    try:
                        # 尝试自定义格式 "YYYY-MM-DD HH:MM:SS"
                        created_time = datetime.strptime(created_at, "%Y-%m-%d %H:%M:%S")
                        # 假设是CST时间，转换为UTC
                        cst = timezone(timedelta(hours=8))
                        created_time = created_time.replace(tzinfo=cst).astimezone(timezone.utc)
                    except ValueError:
                        # 如果都失败，使用当前时间（避免崩溃）
                        logger.warning(f"无法解析任务 {task_id} 的创建时间: {created_at}")
                        created_time = now
            else:
                # 如果没有创建时间，使用当前时间
                created_time = now

            task_age = (now - created_time).total_seconds()

            # 检查超时任务（未完成且超过超时时间）
            if task['status'] in [Status.WAITING, Status.PENDING] and task_age > TASK_TIMEOUT_SECONDS:
                expired_tasks.append((task_id, task))

            # 检查已完成任务是否需要清理（超时任务1分钟删除，其他完成任务5分钟删除）
            elif task['status'] == Status.TIMEOUT and task_age > 60:
                completed_tasks_to_remove.append(task_id)
            elif task['status'] in [Status.SUCCESS, Status.FAILED] and task_age > 300:
                completed_tasks_to_remove.append(task_id)

        # 处理过期任务
        for task_id, task in expired_tasks:
            logger.warning(emoji("TIMEOUT", f"任务 {task_id} 超时，正在清理"))

            # 释放worker资源
            assigned_worker_id = task.get('assignedTo')
            if assigned_worker_id:
                await release_worker_task(assigned_worker_id, task_id)

            # 标记任务为超时
            task['status'] = Status.TIMEOUT
            task['result'] = {"error": "任务超时"}
            task['errorId'] = -2

            logger.info(emoji("CLEANUP", f"已清理超时任务 {task_id}"))

        # 清理已完成的任务（防止内存泄漏）
        for task_id in completed_tasks_to_remove:
            if task_id in task_pool:
                task_status = task_pool[task_id]['status']
                del task_pool[task_id]
                if task_status == Status.TIMEOUT:
                    logger.debug(f"🗑️ 清理超时任务 {task_id}（超时1分钟后删除）")
                else:
                    logger.debug(f"🗑️ 清理已完成任务 {task_id}（完成超过5分钟）")

        # 记录清理统计
        if expired_tasks or completed_tasks_to_remove:
            logger.info(emoji("STATS", f"任务清理完成：超时 {len(expired_tasks)} 个，清理已完成 {len(completed_tasks_to_remove)} 个，当前任务池大小: {len(task_pool)}"))

        await asyncio.sleep(30)

async def release_worker_task(worker_id, task_id=None):
    """释放worker的任务计数"""
    try:
        # 延迟导入避免循环导入
        import core.worker_manager as wm
        if worker_id in wm.worker_pool:
            worker = wm.worker_pool[worker_id]
            if worker['current_tasks'] > 0:
                worker['current_tasks'] -= 1
                logger.info(emoji("RELEASE", f"释放worker {worker_id} 的任务计数，当前任务数: {worker['current_tasks']}"))
    except Exception as e:
        logger.error(emoji("ERROR", f"释放worker任务计数失败: {e}"))

def get_task_by_id(task_id):
    """根据任务ID获取任务"""
    return task_pool.get(task_id)

def update_task_status(task_id, status, result=None, error_id=0):
    """更新任务状态"""
    if task_id in task_pool:
        task_pool[task_id]['status'] = status
        if result is not None:
            task_pool[task_id]['result'] = result
        task_pool[task_id]['errorId'] = error_id
        return True
    return False

def get_pending_tasks_for_worker(worker_id):
    """获取分配给指定worker的待处理任务"""
    return [
        task for task in task_pool.values()
        if task.get('assignedTo') == worker_id and task['status'] == Status.PENDING
    ]

def get_task_pool_stats():
    """获取任务池统计信息"""
    stats = {
        'total': len(task_pool),
        'waiting': 0,
        'pending': 0,
        'success': 0,
        'failed': 0,
        'timeout': 0
    }

    for task in task_pool.values():
        if task['status'] == Status.WAITING:
            stats['waiting'] += 1
        elif task['status'] == Status.PENDING:
            stats['pending'] += 1
        elif task['status'] == Status.SUCCESS:
            stats['success'] += 1
        elif task['status'] == Status.FAILED:
            stats['failed'] += 1
        elif task['status'] == Status.TIMEOUT:
            stats['timeout'] += 1

    return stats

def log_task_pool_stats():
    """记录任务池统计信息"""
    stats = get_task_pool_stats()
    logger.info(emoji("STATS", f"任务池状态 - 总计:{stats['total']}, 等待:{stats['waiting']}, 处理中:{stats['pending']}, 成功:{stats['success']}, 失败:{stats['failed']}, 超时:{stats['timeout']}"))
    return stats
