"use strict";
exports.srLatn = void 0;
var _index = require("./sr-Latn/_lib/formatDistance.cjs");
var _index2 = require("./sr-Latn/_lib/formatLong.cjs");
var _index3 = require("./sr-Latn/_lib/formatRelative.cjs");
var _index4 = require("./sr-Latn/_lib/localize.cjs");
var _index5 = require("./sr-Latn/_lib/match.cjs");

/**
 * @category Locales
 * @summary Serbian latin locale.
 * @language Serbian
 * @iso-639-2 srp
 * <AUTHOR> [@rogyvoje](https://github.com/rogyvoje)
 */
const srLatn = (exports.srLatn = {
  code: "sr-Latn",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
});
