const fs = require("fs");
const path = require("path");
const axios = require("axios");
const crypto = require("crypto");
const config = require("../../config");
const { getAuthHeaders, executeWithRetry, setCurrentWalletAddress } = require("./auth");

// 提取重试函数，用于积分验证
async function retryUntilPointsIncrease(action, maxRetries = 3) {
  let retryCount = 0;
  let lastResult = null;
  let pointsIncreased = false;
  
  // 记录函数调用前的积分
  let beforePoints = 0;
  try {
    const pointData = await getUserPoints();
    beforePoints = pointData.points.inference;
    log(`行动前积分: ${beforePoints}`, "info");
  } catch (pointError) {
    log(`无法获取初始积分: ${pointError.message}`, "error");
  }
  
  while (retryCount <= maxRetries && !pointsIncreased) {
    if (retryCount > 0) {
      log(`开始第 ${retryCount} 次重试...`, "warning");
      // 在重试之间添加随机延时
      const delay = 3000 + Math.floor(Math.random() * 2000);
      log(`等待 ${delay/1000} 秒后重试...`, "info");
      await new Promise(r => setTimeout(r, delay));
    }
    
    try {
      // 执行操作
      lastResult = await action();
      
      // 验证积分是否增加
      try {
        const pointData = await getUserPoints();
        const afterPoints = pointData.points.inference;
        pointsIncreased = afterPoints > beforePoints;
        
        log(`行动前积分: ${beforePoints}, 行动后积分: ${afterPoints}`, "info");
        
        if (pointsIncreased) {
          log(`积分增加成功！当前积分: ${afterPoints}`, "success");
          break;
        } else {
          log(`积分未增加，当前积分: ${afterPoints}`, "warning");
          if (retryCount >= maxRetries) {
            log(`已达到最大重试次数 (${maxRetries})，结束重试`, "warning");
            break;
          }
        }
      } catch (pointVerifyError) {
        log(`验证积分时出错: ${pointVerifyError.message}`, "error");
        if (retryCount >= maxRetries) break;
      }
      
      retryCount++;
    } catch (actionError) {
      log(`操作执行出错: ${actionError.message}`, "error");
      lastResult = { error: actionError };
      
      if (retryCount >= maxRetries) {
        log(`已达到最大重试次数 (${maxRetries})，结束重试`, "error");
        break;
      }
      
      retryCount++;
    }
  }
  
  return { result: lastResult, pointsIncreased, retries: retryCount };
}
const { getUserPoints } = require("./points");
const {
  log,
  logChat,
  logToFile,
  logApiRequest,
  logApiResponse,
  logApiError,
} = require("../utils");
// 根据配置选择验证码服务
function getCaptchaService() {
  const config = require('../../config');
  switch (config.CAPTCHA_SERVICE) {
    case 'local':
      return require('../services/turnstileSolver');
    case '2captcha':
      return require('../services/twoCaptcha');
    case 'remote':
      return require('../services/remoteCaptchaService');
    default:
      console.warn(`未知的验证码服务: ${config.CAPTCHA_SERVICE}，使用本地服务`);
      return require('../services/turnstileSolver');
  }
}

const { HttpsProxyAgent } = require("https-proxy-agent");
const { HttpProxyAgent } = require("http-proxy-agent");
const walletProxyManager = require("../services/walletProxyManager");

const PROXY_FILE = "proxies.txt";

let allProxies = [];
let currentProxyIndex = 0;

function readAllProxiesFromFile() {
  try {
    if (fs.existsSync(PROXY_FILE)) {
      const fileContent = fs.readFileSync(PROXY_FILE, "utf8");
      const proxies = fileContent
        .split("\n")
        .map((line) => line.trim())
        .filter((line) => line.length > 0);
      return proxies;
    }
    return [];
  } catch (err) {
    console.error("Error reading proxies file:", err.message);
    return [];
  }
}

function getCurrentProxy(walletAddress = null) {
  // 如果提供了钱包地址且启用了钱包代理绑定，优先使用钱包绑定的代理
  if (walletAddress && config.ENABLE_WALLET_PROXY_BINDING) {
    const binding = walletProxyManager.getWalletBinding(walletAddress);
    if (binding && binding.proxyUrl) {
      log(`使用钱包 ${walletAddress} 绑定的代理: ${binding.proxyUrl}`, "info");
      return binding.proxyUrl;
    }
  }
  
  // 如果没有钱包绑定的代理，使用普通代理
  if (allProxies.length === 0) {
    allProxies = readAllProxiesFromFile();
    currentProxyIndex = 0;
  }
  if (allProxies.length === 0) {
    log("No proxies found, using machine's default IP.");
    return null;
  }
  if (currentProxyIndex >= allProxies.length) {
    currentProxyIndex = 0;
  }
  const proxyUrl = allProxies[currentProxyIndex];
  return proxyUrl;
}

function switchToNextProxy() {
  if (allProxies.length === 0) {
    allProxies = readAllProxiesFromFile();
  }
  if (allProxies.length === 0) {
    log("No proxies available to switch, using machine's default IP.");
    return null;
  }
  currentProxyIndex = (currentProxyIndex + 1) % allProxies.length;
  const proxyUrl = allProxies[currentProxyIndex];
  try {
    const parsedUrl = new URL(proxyUrl);
    log(`Switched to proxy: ${parsedUrl.hostname}`);
  } catch (err) {
    log(`Switched to proxy: ${proxyUrl}`);
  }
  return proxyUrl;
}

function getProxyAgent(targetUrl, walletAddress = null) {
  const proxyUrl = getCurrentProxy(walletAddress);
  if (!proxyUrl) return null;
  if (targetUrl.startsWith("https")) {
    return new HttpsProxyAgent(proxyUrl);
  } else {
    return new HttpProxyAgent(proxyUrl);
  }
}

let currentThread = null;
let selectedModel = null;

function setSelectedModel(modelName) {
  selectedModel = modelName;
  log(`已选择模型: ${modelName}`, "info");
  logToFile(`Selected model: ${modelName}`);
}

function getSelectedModel() {
  return selectedModel;
}

function createThread() {
  const threadId = crypto.randomUUID();
  currentThread = {
    id: threadId,
    title: "",
    messages: [],
    created_at: new Date().toISOString(),
  };
  log(`已创建新的聊天线程: ${threadId}`, "success");
  logToFile("New chat thread created", {
    threadId: threadId,
    createdAt: currentThread.created_at,
  });
  return currentThread;
}

async function verifyPointIncrease(beforePoints, response) {
  try {
    await new Promise((resolve) => setTimeout(resolve, 3000));
    const pointData = await getUserPoints();
    const afterPoints = pointData.points.inference;
    const pointIncreased = afterPoints > beforePoints;
    
    logToFile(
      `Point verification: ${pointIncreased ? "Points increased" : "No change in points"}`,
      {
        before: beforePoints,
        after: afterPoints,
        difference: afterPoints - beforePoints,
      }
    );
    
    // 检查响应状态码，如果是200，即使积分没有增加也认为成功
    const responseStatus = response && response.status ? response.status : 0;
    const isSuccessStatus = responseStatus >= 200 && responseStatus < 300;
    
    // 如果积分增加或响应状态码为成功，则认为验证通过
    const isVerified = pointIncreased || isSuccessStatus;
    
    if (isVerified && !pointIncreased) {
      log("虽然积分未增加，但响应状态码为成功，验证通过", "warning");
    }
    
    return isVerified;
  } catch (error) {
    logToFile(`Error verifying points: ${error.message}`, { error: error.message }, false);
    // 如果获取积分失败，但响应状态码为成功，仍然认为验证通过
    if (response && response.status && response.status >= 200 && response.status < 300) {
      log("获取积分失败，但响应状态码为成功，验证通过", "warning");
      return true;
    }
    return false;
  }
}

// 提取响应内容的辅助函数
function extractResponseContent(responseData) {
  // 检查是否为普通JSON对象
  if (typeof responseData === "object" && responseData !== null) {
    // 记录响应对象的键，用于调试
    logToFile("尝试提取响应内容", {
      keys: Object.keys(responseData),
      hasContent: !!responseData.content,
      hasResponse: !!responseData.response,
      hasMessage: !!responseData.message,
      hasAssistant: !!responseData.assistant,
      hasChoices: !!responseData.choices
    }, true);
    
    if (responseData.content) {
      return responseData.content;
    }
    if (responseData.text) {
      return responseData.text;
    }
    if (responseData.message) {
      return typeof responseData.message === "string" 
        ? responseData.message 
        : responseData.message.content || responseData.message.text;
    }
    if (responseData.choices && responseData.choices.length > 0) {
      const choice = responseData.choices[0];
      return choice.text || (choice.message && (choice.message.content || choice.message.text)) || "";
    }
    if (responseData.response) {
      return typeof responseData.response === "string" 
        ? responseData.response 
        : responseData.response.content || responseData.response.text;
    }
    if (responseData.assistant) {
      return typeof responseData.assistant === "string" 
        ? responseData.assistant 
        : responseData.assistant.content || responseData.assistant.text;
    }
  }
  
  return null;
}

// 处理响应数据并提取AI回复内容
async function parseResponse(response, model) {
  try {
    const responseData = response.data;
    
    // 记录原始响应数据类型和片段以便调试
    logToFile(`${model} 响应原始数据`, { 
      type: typeof responseData,
      isString: typeof responseData === 'string',
      isObject: typeof responseData === 'object',
      preview: typeof responseData === 'string' 
        ? responseData.substring(0, 200) 
        : JSON.stringify(responseData).substring(0, 200)
    }, true);
    
    // 如果是空响应，直接返回null
    if (!responseData || responseData === "") {
      logToFile(`${model} 响应为空`, {}, true);
      return null;
    }
    
    const responseText = typeof responseData === 'string' ? responseData : JSON.stringify(responseData);
    
    // 1. 首先尝试提取对象属性
    if (typeof responseData === 'object') {
      const extracted = extractResponseContent(responseData);
      if (extracted) {
        logToFile(`${model} 对象属性提取成功`, { extractedType: typeof extracted }, true);
        return extracted;
      }
    } else if (typeof responseData === 'string') {
      // 尝试解析为JSON
      try {
        const jsonData = JSON.parse(responseData);
        logToFile(`${model} 字符串解析为JSON成功`, { keys: Object.keys(jsonData) }, true);
        
        const extracted = extractResponseContent(jsonData);
        if (extracted) return extracted;
        
        if (jsonData.content) return jsonData.content;
        if (jsonData.message) {
          return typeof jsonData.message === 'string' ? jsonData.message : jsonData.message.content;
        }
        if (jsonData.response) {
          return typeof jsonData.response === 'string' ? jsonData.response : jsonData.response.content;
        }
      } catch (e) {
        logToFile(`${model} JSON解析失败`, { error: e.message }, false);
      }
    }
    
    // 2. 尝试使用正则表达式提取
    const patterns = [
      /"text"\s*:\s*"([^"]+)"/,
      /"content"\s*:\s*"([^"]+)"/,
      /"message"\s*:\s*"([^"]+)"/,
      /"assistant"\s*:\s*"([^"]+)"/,
      /data:\s*\{"text":"([^"]+)"/,
      /data:\s*\{"content":"([^"]+)"/
    ];
    
    for (const pattern of patterns) {
      const match = responseText.match(pattern);
      if (match && match[1]) {
        const extracted = match[1].replace(/\\n/g, '\n').replace(/\\"/g, '"');
        logToFile(`${model} 正则表达式提取成功`, { pattern: pattern.toString() }, true);
        return extracted;
      }
    }
    
    // 3. 尝试提取JSON块
    const jsonBlocks = responseText.match(/\{[^{}]*\}/g);
    if (jsonBlocks) {
      for (const block of jsonBlocks) {
        try {
          const blockData = JSON.parse(block);
          if (blockData.text) return blockData.text;
          if (blockData.content) return blockData.content;
        } catch (e) {
          // 忽略解析错误
        }
      }
    }
    
    // 4. 尝试查找特定标记
    if (responseText.includes('data:')) {
      const dataLines = responseText.split('\n');
      for (const line of dataLines) {
        if (line.startsWith('data:') && line.length > 5) {
          try {
            const data = JSON.parse(line.substring(5).trim());
            if (data.text) return data.text;
            if (data.content) return data.content;
          } catch (e) {
            // 忽略解析错误
          }
        }
      }
    }
    
    // 5. 如果以上都失败，但响应是字符串，则直接返回
    if (typeof responseData === 'string' && responseData.length > 0) {
      logToFile(`${model} 直接使用响应字符串`, { length: responseData.length }, true);
      // 尝试清理一些特殊格式
      return responseData
        .replace(/^data:\s*/, '')
        .replace(/\[DONE\]/g, '')
        .trim();
    }
    
    logToFile(`${model} 无法提取响应内容`, {}, false);
    return null;
  } catch (error) {
    logToFile(`${model} 响应解析失败`, { error: error.message }, false);
    return null;
  }
}

/**
 * 发送聊天消息并验证成功与否
 * @param {string} content 要发送的消息内容
 * @returns {Promise<string>} 助手的响应消息
 */
async function sendChatMessage(content) {
  log(`发送聊天消息到${selectedModel || '模型'}，启用积分验证重试机制`, "info");

  // 定义发送聊天消息的实际操作
  const sendChatAction = async () => {
    if (!selectedModel) {
      throw new Error("未选择模型。请先选择一个模型。");
    }
    
    if (!currentThread) {
      createThread();
    }
    
    // 获取发送前的积分
    let beforePoints = 0;
    try {
      const pointData = await getUserPoints();
      beforePoints = pointData.points.inference;
      logToFile(`Points before chat: ${beforePoints}`);
    } catch (pointError) {
      logToFile(`Failed to get points before chat: ${pointError.message}`, { error: pointError.message }, false);
    }
    
    // 记录用户消息
    const userMessage = { role: "user", content };
    currentThread.messages.push(userMessage);
    logChat(content, "user");
    
    // 准备聊天请求负载
    const chatPayload = {
      id: currentThread.id,
      title: currentThread.title || "",
      language: "english",
      messages: currentThread.messages,
      model: selectedModel,
      sources: [],
    };
    
    log(`正在发送聊天消息到 ${selectedModel}...`, "info");
    
    // 准备请求函数
    let streamAborted = false;
    let aiResponse = "";
    
    try {
      // 获取Turnstile token用于聊天验证（带重试机制）
      log(`正在获取聊天验证的Turnstile token...`, "info");
      let turnstileToken = null;
      let tokenAttempts = 0;
      const maxTokenAttempts = 5;

      while (tokenAttempts < maxTokenAttempts && !turnstileToken) {
        tokenAttempts++;
        try {
          if (tokenAttempts > 1) {
            log(`Turnstile token获取重试 ${tokenAttempts}/${maxTokenAttempts}`, "warning");
          }

          // 根据配置使用相应的验证码服务
          const captchaService = getCaptchaService();
          const serviceType = {
            'local': '本地Turnstile-Solver',
            '2captcha': '2Captcha',
            'remote': '远程打码服务'
          }[config.CAPTCHA_SERVICE] || '未知服务';

          if (tokenAttempts === 1) {
            log(`[聊天验证] 使用${serviceType}服务...`, "info");
          }

          turnstileToken = await captchaService.getTurnstileToken();
          log(`成功获取聊天验证的Turnstile token${tokenAttempts > 1 ? ` (第${tokenAttempts}次尝试)` : ''}`, "success");
          break;

        } catch (tokenError) {
          log(`Turnstile token获取失败 (尝试${tokenAttempts}/${maxTokenAttempts}): ${tokenError.message}`, "warning");

          if (tokenAttempts < maxTokenAttempts) {
            // 随机延时3-6秒
            const delay = 3000 + Math.floor(Math.random() * 3000);
            log(`等待 ${delay/1000} 秒后重试获取Turnstile token...`, "info");
            await new Promise(resolve => setTimeout(resolve, delay));
          } else {
            log(`获取聊天验证的Turnstile token失败，已达到最大重试次数 (${maxTokenAttempts})`, "error");
            logToFile(`获取聊天验证的Turnstile token失败`, {
              error: tokenError.message,
              attempts: tokenAttempts,
              maxAttempts: maxTokenAttempts
            });
            throw new Error(`获取聊天验证的Turnstile token失败: ${tokenError.message}`);
          }
        }
      }

      // 准备请求头，包含Turnstile token和完整的浏览器模拟请求头
      const headers = { 
        ...getAuthHeaders(),
        "Content-Type": "application/json",
        "x-turnstile-token": turnstileToken,
        "accept": "*/*",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh,zh-CN;q=0.9",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Origin": "https://klokapp.ai",
        "Referer": "https://klokapp.ai/",
        "sec-ch-ua": '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "Windows",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site"
      };
      
      logApiRequest(
        "POST",
        `${config.BASE_URL}/v1/chat`,
        chatPayload,
        headers,
        true
      );
      // 获取当前钱包地址
      const currentWalletAddress = require('./auth').getCurrentWalletAddress();
      
      // 获取代理，使用当前钱包地址
      const agent = getProxyAgent(config.BASE_URL, currentWalletAddress);
      
      log(`当前钱包地址: ${currentWalletAddress || '未设置'}`, "info");
      
      // 特殊处理，对于这个聊天请求，如果配置了代理但还是失败，可以尝试禁用代理
      const useProxy = true; // 默认使用代理，如果发现问题可以将此设为false
      
      // 配置请求选项
      const axiosConfig = {
        headers: headers,
        responseType: "text",
        timeout: 60000, // 调整超时时间为60秒，避免过长超时
        maxContentLength: Infinity, // 允许任意大小的响应
        maxBodyLength: Infinity, // 允许任意大小的请求体
        proxy: false, // 禁用axios默认代理设置
        validateStatus: function (status) {
          // 允许所有状态码，手动处理错误
          return true;
        }
      };
      
      // 如果有代理且启用代理，添加到请求配置中
      if (agent && useProxy) {
        log(`使用代理发送聊天请求: ${currentWalletAddress}`, "info");
        if (config.BASE_URL.startsWith("https")) {
          axiosConfig.httpsAgent = agent;
        } else {
          axiosConfig.httpAgent = agent;
        }
      } else {
        log(`不使用代理发送聊天请求`, "info");
      }
      
      try {
        // 记录发送的请求详情
        logToFile('发送聊天请求详情', {
          url: `${config.BASE_URL}/v1/chat`,
          method: 'POST',
          tokenLength: turnstileToken ? turnstileToken.length : 0,
          hasProxy: !!agent,
          proxyInfo: agent ? 'Using proxy' : 'No proxy',
          payloadLength: JSON.stringify(chatPayload).length,
          timeout: axiosConfig.timeout
        });
        
        // 发送请求 - 使用正确的API端点路径
        const response = await axios.post(`${config.BASE_URL}/v1/chat`, chatPayload, axiosConfig);
        
        // 记录响应详情
        logToFile('收到聊天响应', {
          status: response.status,
          statusText: response.statusText,
          dataType: typeof response.data,
          dataLength: typeof response.data === 'string' ? response.data.length : 'N/A',
          contentType: response.headers['content-type']
        });
        
        logApiResponse(
          "/chat",
          {
            model: selectedModel,
            threadId: currentThread.id,
            responseStatus: response.status,
            responsePreview:
              typeof response.data === "string"
                ? response.data.substring(0, 200) + (response.data.length > 200 ? "..." : "")
                : JSON.stringify(response.data).substring(0, 200),
          }
        );
        
        // 处理响应
        if (response.status >= 200 && response.status < 300) {
          try {
            // 提取响应内容
            aiResponse = await parseResponse(response, selectedModel);
            
            if (aiResponse) {
              // 记录AI响应
              const assistantMessage = { role: "assistant", content: aiResponse };
              currentThread.messages.push(assistantMessage);
              logChat(aiResponse, "assistant");
              log("收到响应！", "success");
            } else {
              log("收到空响应，可能需要验证积分", "warning");
            }
          } catch (parseError) {
            log(`解析响应失败: ${parseError.message}`, "error");
            logToFile("解析响应失败", { error: parseError.message, data: response.data });
            // 即使解析失败，我们也返回原始响应，让调用者可以决定如何处理
          }
        } else {
          // 处理非成功状态码
          const errorMsg = `聊天请求失败，状态码: ${response.status}`;
          log(errorMsg, "error");
          throw new Error(errorMsg);
        }
        
        // 检查积分是否增加，最多等待5秒
        try {
          const pointVerified = await verifyPointIncrease(beforePoints, response);
          if (pointVerified) {
            log("积分验证成功，消息已成功发送并计费", "success");
          } else {
            log("积分未增加，但请求状态码为成功，可能是免费消息或积分计算延迟", "warning");
          }
        } catch (verifyError) {
          log(`积分验证失败: ${verifyError.message}`, "warning");
        }
        
        return {
          response,
          aiResponse,
          beforePoints
        };
        
      } catch (error) {
        logApiError("/chat", error);
        throw error;
      }
    } catch (error) {
      logToFile("发送聊天消息错误", {
        error: error.message,
        stack: error.stack,
        model: selectedModel
      });
      throw error;
    }
  };
  
  // 使用重试函数发送聊天消息并验证积分增加
  try {
    const result = await retryUntilPointsIncrease(sendChatAction, 3);
    
    if (result.pointsIncreased) {
      log(`聊天消息发送成功，积分已增加！${result.retries > 0 ? ` (经过${result.retries}次重试)` : ''}`, "success");
    } else {
      log(`聊天消息已发送，但积分未增加。`, "warning");
    }
    
    return result.result && result.result.aiResponse ? result.result.aiResponse : "发送消息成功，但无法解析响应";
  } catch (error) {
    log(`发送聊天消息失败: ${error.message}`, "error");
    throw error;
  }
}

module.exports = {
  setSelectedModel,
  getSelectedModel,
  createThread,
  getCurrentThread: () => currentThread,
  sendChatMessage,
  switchToNextProxy,
  getProxyAgent,
};
