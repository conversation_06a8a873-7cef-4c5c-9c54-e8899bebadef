{"name": "has-ansi", "version": "2.0.0", "description": "Check if a string has ANSI escape codes", "license": "MIT", "repository": "sindresorhus/has-ansi", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": ["Sindre Sorhus <<EMAIL>> (sindresorhus.com)", "<PERSON> <<EMAIL>> (jbnicolai.com)"], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern", "has"], "dependencies": {"ansi-regex": "^2.0.0"}, "devDependencies": {"ava": "0.0.4"}}