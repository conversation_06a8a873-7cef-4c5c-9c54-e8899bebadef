{"version": 3, "file": "provider-jsonrpc.d.ts", "sourceRoot": "", "sources": ["../../src.ts/providers/provider-jsonrpc.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;GAUG;AAWH,OAAO,EAGH,YAAY,EACf,MAAM,mBAAmB,CAAC;AAE3B,OAAO,EAAE,gBAAgB,EAAuB,MAAM,wBAAwB,CAAC;AAC/E,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AACtD,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AAIvC,OAAO,KAAK,EAAE,eAAe,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AACxE,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAC;AAE/D,OAAO,KAAK,EAAE,oBAAoB,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AAC7F,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC/C,OAAO,KAAK,EAAE,QAAQ,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,MAAM,eAAe,CAAC;AACvF,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AA6C1C;;GAEG;AACH,MAAM,MAAM,cAAc,GAAG;IACzB;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,MAAM,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAEzC;;OAEG;IACH,OAAO,EAAE,KAAK,CAAC;CAClB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,aAAa,GAAG;IACxB;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,MAAM,EAAE,GAAG,CAAC;CACf,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,YAAY,GAAG;IACvB;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,KAAK,EAAE;QACH,IAAI,EAAE,MAAM,CAAC;QACb,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,IAAI,CAAC,EAAE,GAAG,CAAC;KACd,CAAA;CACJ,CAAC;AAEF;;;GAGG;AACH,MAAM,MAAM,4BAA4B,GAAG;IACvC,MAAM,EAAE,gBAAgB,CAAC;IACzB,OAAO,EAAE,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC,CAAA;CAClD,GAAG;IACA,MAAM,EAAE,kBAAkB,CAAC;IAC3B,MAAM,EAAE,KAAK,CAAC,aAAa,GAAG,YAAY,CAAC,CAAA;CAC9C,GAAG;IACA,MAAM,EAAE,iBAAiB,CAAC;IAC1B,KAAK,EAAE,KAAK,CAAA;CACf,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BG;AACH,MAAM,MAAM,yBAAyB,GAAG;IACpC,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,aAAa,CAAC,EAAE,IAAI,GAAG,OAAO,GAAG,OAAO,CAAC;IACzC,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,aAAa,CAAC,EAAE,MAAM,CAAC;IAEvB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,eAAe,CAAC,EAAE,MAAM,CAAC;CAC5B,CAAC;AAcF;;;GAGG;AACH,MAAM,WAAW,yBAAyB;IACrC;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IAEZ;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd;;;;;OAKG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;OAEG;IACH,oBAAoB,CAAC,EAAE,MAAM,CAAC;IAE9B;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,UAAU,CAAC,EAAE,KAAK,CAAC;QAAE,OAAO,EAAE,MAAM,CAAC;QAAC,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;KAAE,CAAC,CAAC;CACxE;AAID,qBAAa,aAAc,SAAQ,cAAc,CAAC,kBAAkB,CAAC;IACjE,OAAO,EAAG,MAAM,CAAC;gBAEL,QAAQ,EAAE,kBAAkB,EAAE,OAAO,EAAE,MAAM;IAMzD,OAAO,CAAC,QAAQ,EAAE,IAAI,GAAG,QAAQ,GAAG,MAAM;IAMpC,UAAU,IAAI,OAAO,CAAC,MAAM,CAAC;IAK7B,mBAAmB,CAAC,EAAE,EAAE,kBAAkB,GAAG,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAM7E,wBAAwB,CAAC,GAAG,EAAE,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC;IA2ClE,eAAe,CAAC,EAAE,EAAE,kBAAkB,GAAG,OAAO,CAAC,mBAAmB,CAAC;IAgErE,eAAe,CAAC,GAAG,EAAE,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC;IAkBzD,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;IAM3D,aAAa,CAAC,MAAM,EAAE,eAAe,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,cAAc,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IAgBlI,MAAM,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAM1C,kBAAkB,CAAC,QAAQ,EAAE,MAAM,GAAG,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;CAK3E;AAOD;;;;;;;;;GASG;AACH,8BAAsB,kBAAmB,SAAQ,gBAAgB;;gBAmGjD,OAAO,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,EAAE,yBAAyB;IAmCrE;;;;OAIG;IACH,UAAU,CAAC,CAAC,SAAS,MAAM,yBAAyB,EAAE,GAAG,EAAE,CAAC,GAAG,yBAAyB,CAAC,CAAC,CAAC;IAI3F;;;OAGG;IACH,IAAI,QAAQ,IAAI,OAAO,CAGtB;IAED;;;;OAIG;IACH,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,aAAa,GAAG,YAAY,CAAC,CAAC;IAG7G;;;;;OAKG;IACG,QAAQ,CAAC,GAAG,EAAE,oBAAoB,GAAG,OAAO,CAAC,GAAG,CAAC;IA6BvD;;;;;;OAMG;IACG,cAAc,IAAI,OAAO,CAAC,OAAO,CAAC;IA2DxC;;;;;;OAMG;IACH,MAAM,IAAI,IAAI;IAyBd;;;;OAIG;IACG,eAAe,IAAI,OAAO,CAAC,IAAI,CAAC;IAMtC;;;;;OAKG;IACH,cAAc,CAAC,GAAG,EAAE,YAAY,GAAG,UAAU;IAqB7C;;OAEG;IACH,IAAI,KAAK,IAAI,OAAO,CAAmC;IAEvD;;;;OAIG;IACH,iBAAiB,CAAC,EAAE,EAAE,kBAAkB,GAAG,yBAAyB;IAmCpE;;;OAGG;IACH,aAAa,CAAC,GAAG,EAAE,oBAAoB,GAAG,IAAI,GAAG;QAAE,MAAM,EAAE,MAAM,CAAC;QAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAA;KAAE;IAqGrF;;;;;OAKG;IACH,WAAW,CAAC,OAAO,EAAE,cAAc,EAAE,MAAM,EAAE,YAAY,GAAG,KAAK;IA2FjE;;;;;;;;;;;;OAYG;IACH,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC;IAsB5E;;;;;;;;;;;OAWG;IACG,SAAS,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC;IA4B5D,YAAY,IAAI,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;IAKnD,OAAO,IAAI,IAAI;CAmBlB;AAKD;;GAEG;AACH,8BAAsB,yBAA0B,SAAQ,kBAAkB;;gBAE1D,OAAO,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,EAAE,yBAAyB;IASrE,cAAc,CAAC,GAAG,EAAE,YAAY,GAAG,UAAU;IAQ7C;;OAEG;IACH,IAAI,eAAe,IAAI,MAAM,CAAkC;IAC/D,IAAI,eAAe,CAAC,KAAK,EAAE,MAAM,EAQhC;CACJ;AAED;;;;;;;GAOG;AACH,qBAAa,eAAgB,SAAQ,yBAAyB;;gBAG9C,GAAG,CAAC,EAAE,MAAM,GAAG,YAAY,EAAE,OAAO,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,EAAE,yBAAyB;IAWlG,cAAc,IAAI,YAAY;IAIxB,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC;IAS5E,KAAK,CAAC,OAAO,EAAE,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;CAa9F"}