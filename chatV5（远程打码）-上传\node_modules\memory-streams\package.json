{"name": "memory-streams", "description": "Simple implmentation of Stream.Readable and Stream.Writable holding the data in memory.", "version": "0.1.3", "author": "<PERSON> (http://jaaco.uk/)", "repository": {"type": "git", "url": "**************:paulja/memory-streams-js.git"}, "homepage": "https://github.com/paulja/memory-streams-js", "main": "index.js", "typings": "index.d.ts", "directories": {"test": "test"}, "dependencies": {"readable-stream": "~1.0.2"}, "devDependencies": {"should": "~1.2.2"}, "scripts": {"test": "node ./test/test-readablestream.js && node ./test/test-writablestream.js"}, "keywords": ["stream", "string", "memory", "Readable", "Writable"], "license": "MIT", "readmeFilename": "README.md"}