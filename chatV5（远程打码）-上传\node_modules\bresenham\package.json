{"name": "<PERSON><PERSON><PERSON>", "version": "0.0.3", "description": "<PERSON><PERSON><PERSON>'s line algorithm", "main": "index.js", "scripts": {"test": "mocha -R spec -r should test/**/*.js"}, "repository": {"type": "git", "url": "**************:madbence/node-bresenham.git"}, "keywords": ["line", "<PERSON><PERSON><PERSON>", "math"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/madbence/node-brese<PERSON>/issues"}, "homepage": "https://github.com/madbence/node-bresenham", "devDependencies": {"should": "^3.3.1", "mocha": "^1.18.2"}}