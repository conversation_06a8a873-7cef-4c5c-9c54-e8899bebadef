'use strict'

/* eslint-disable no-template-curly-in-string */

var test = require('tape')
var util = require('util')
var redeyed = require('..')

function inspect(obj) {
  return util.inspect(obj, false, 5, true)
}

test('adding custom asserts ... ', function(t) {
  t.constructor.prototype.assertSurrounds = function(code, opts, expected) {
    var optsi = inspect(opts)
    var result = redeyed(code, opts).code

    this.equals(result
                , expected
                , util.format('%s: %s => %s', optsi, inspect(code), inspect(expected))
               )
    return this
  }

  t.end()
})

test('types', function(t) {
  t.test('\n# Boolean', function(t) {
    t.assertSurrounds('return true;', { 'Boolean': { _default: '$:%' } }, 'return $true%;')
    t.assertSurrounds('return true; return false;'
                      , { 'Boolean': { 'false': '#:', _default: '$:%' } }
                      , 'return $true%; return #false%;')
    t.end()
  })

  t.test('\n# Identifier', function(t) {
    t.assertSurrounds('var a = 1;', { 'Identifier': { _default: '$:%' } }, 'var $a% = 1;')
    t.assertSurrounds('var a = 1; const b = 2;'
                      , { 'Identifier': { 'b': '#:', _default: '$:%' } }
                      , 'var $a% = 1; const #b% = 2;')
    t.end()
  })

  t.test('\n# Null', function(t) {
    t.assertSurrounds('return null;', { 'Null': { _default: '$:%' } }, 'return $null%;').end()
  })

  t.test('\n# Numeric', function(t) {
    t.assertSurrounds('return 1;', { 'Numeric': { _default: '$:%' } }, 'return $1%;')
    t.assertSurrounds('return 1; return 2;'
                      , { 'Numeric': { '2': '#:', _default: '$:%' } }
                      , 'return $1%; return #2%;')
    t.end()
  })

  t.test('\n# Punctuator', function(t) {
    var punctuator = { 'Punctuator': { _default: '$:%' } }
    t.assertSurrounds('return 2 * 2;', punctuator, 'return 2 $*% 2$;%')
    t.assertSurrounds('return 2 * 2;'
                      , { 'Punctuator': { '*': '#:', _default: '$:%' } }
                      , 'return 2 #*% 2$;%')
    t.assertSurrounds('var {op, lhs, rhs} = getASTNode()', punctuator, 'var ${%op$,% lhs$,% rhs$}% $=% getASTNode$(%$)%')
    t.assertSurrounds('function f(x, y=12) { return x + y;}', punctuator, 'function f$(%x$,% y$=%12$)% ${% return x $+% y$;%$}%')
    t.assertSurrounds('function f(x, ...y) { return x * y.length;}', punctuator, 'function f$(%x$,% $...%y$)% ${% return x $*% y$.%length$;%$}%')
    t.end()
  })

  t.test('\n# String', function(t) {
    t.assertSurrounds('return "hello";', { 'String': { _default: '$:%' } }, 'return $"hello"%;')
    t.assertSurrounds('return "hello"; return "world";'
                      , { 'String': { '"world"': '#:', _default: '$:%' } }
                      , 'return $"hello"%; return #"world"%;')
    t.end()
  })

  t.end()
})
