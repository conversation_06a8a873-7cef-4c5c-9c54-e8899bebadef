{"version": 3, "file": "curve.js", "sourceRoot": "", "sources": ["../src/abstract/curve.ts"], "names": [], "mappings": ";;;AAAA,sEAAsE;AACtE,0BAA0B;AAC1B,6CAA8D;AAC9D,yCAA4C;AAC5C,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAsBtB,6DAA6D;AAC7D,mGAAmG;AACnG,yDAAyD;AACzD,4DAA4D;AAC5D,+EAA+E;AAC/E,oGAAoG;AACpG,oGAAoG;AACpG,qCAAqC;AACrC,kGAAkG;AAClG,gGAAgG;AAChG,8CAA8C;AAC9C,SAAgB,IAAI,CAAqB,CAAsB,EAAE,IAAY;IAC3E,MAAM,eAAe,GAAG,CAAC,SAAkB,EAAE,IAAO,EAAK,EAAE;QACzD,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAC1B,OAAO,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;IAChC,CAAC,CAAC;IACF,MAAM,IAAI,GAAG,CAAC,CAAS,EAAE,EAAE;QACzB,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,cAAc;QACvD,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,0BAA0B;QAC3D,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACjC,CAAC,CAAC;IACF,OAAO;QACL,eAAe;QACf,uCAAuC;QACvC,YAAY,CAAC,GAAM,EAAE,CAAS;YAC5B,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACf,IAAI,CAAC,GAAM,GAAG,CAAC;YACf,OAAO,CAAC,GAAG,GAAG,EAAE;gBACd,IAAI,CAAC,GAAG,GAAG;oBAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC1B,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;gBACf,CAAC,KAAK,GAAG,CAAC;aACX;YACD,OAAO,CAAC,CAAC;QACX,CAAC;QAED;;;;;;;;;WASG;QACH,gBAAgB,CAAC,GAAM,EAAE,CAAS;YAChC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,MAAM,GAAQ,EAAE,CAAC;YACvB,IAAI,CAAC,GAAM,GAAG,CAAC;YACf,IAAI,IAAI,GAAG,CAAC,CAAC;YACb,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC/C,IAAI,GAAG,CAAC,CAAC;gBACT,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClB,2BAA2B;gBAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;oBACnC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBACnB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACnB;gBACD,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;aACnB;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED;;;;;;WAMG;QACH,IAAI,CAAC,CAAS,EAAE,WAAgB,EAAE,CAAS;YACzC,gGAAgG;YAChG,4EAA4E;YAC5E,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAExC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACf,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAEf,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,+CAA+C;YAChF,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,CAAC;YACzB,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAE1B,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC/C,MAAM,MAAM,GAAG,MAAM,GAAG,UAAU,CAAC;gBACnC,kBAAkB;gBAClB,IAAI,KAAK,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;gBAE7B,0BAA0B;gBAC1B,CAAC,KAAK,OAAO,CAAC;gBAEd,2DAA2D;gBAC3D,mBAAmB;gBACnB,IAAI,KAAK,GAAG,UAAU,EAAE;oBACtB,KAAK,IAAI,SAAS,CAAC;oBACnB,CAAC,IAAI,GAAG,CAAC;iBACV;gBAED,6FAA6F;gBAC7F,qFAAqF;gBACrF,0EAA0E;gBAC1E,+EAA+E;gBAC/E,2EAA2E;gBAE3E,kCAAkC;gBAClC,+CAA+C;gBAC/C,MAAM,OAAO,GAAG,MAAM,CAAC;gBACvB,MAAM,OAAO,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,0BAA0B;gBACxE,MAAM,KAAK,GAAG,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC;gBAC/B,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;gBACxB,IAAI,KAAK,KAAK,CAAC,EAAE;oBACf,sDAAsD;oBACtD,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;iBACzD;qBAAM;oBACL,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;iBACzD;aACF;YACD,wFAAwF;YACxF,yEAAyE;YACzE,mFAAmF;YACnF,wEAAwE;YACxE,4DAA4D;YAC5D,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QAClB,CAAC;QAED,UAAU,CAAC,CAAI,EAAE,cAA2B,EAAE,CAAS,EAAE,SAAoB;YAC3E,aAAa;YACb,MAAM,CAAC,GAAW,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC;YACtC,yDAAyD;YACzD,IAAI,IAAI,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACjC,IAAI,CAAC,IAAI,EAAE;gBACT,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAQ,CAAC;gBAC1C,IAAI,CAAC,KAAK,CAAC,EAAE;oBACX,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;iBACxC;aACF;YACD,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAC/B,CAAC;KACF,CAAC;AACJ,CAAC;AA/HD,oBA+HC;AAgBD,SAAgB,aAAa,CAAQ,KAAyB;IAC5D,IAAA,0BAAa,EAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IACxB,IAAA,yBAAc,EACZ,KAAK,EACL;QACE,CAAC,EAAE,QAAQ;QACX,CAAC,EAAE,QAAQ;QACX,EAAE,EAAE,OAAO;QACX,EAAE,EAAE,OAAO;KACZ,EACD;QACE,UAAU,EAAE,eAAe;QAC3B,WAAW,EAAE,eAAe;KAC7B,CACF,CAAC;IACF,eAAe;IACf,OAAO,MAAM,CAAC,MAAM,CAAC;QACnB,GAAG,IAAA,oBAAO,EAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;QACrC,GAAG,KAAK;QACR,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE;KAChB,CAAC,CAAC;AACd,CAAC;AArBD,sCAqBC"}