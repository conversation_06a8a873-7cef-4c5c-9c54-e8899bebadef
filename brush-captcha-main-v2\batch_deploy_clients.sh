#!/bin/bash

echo "🚀 批量部署客户端脚本"
echo "主服务器IP: *************"
echo ""

# 检查sshpass是否安装
if ! command -v sshpass &> /dev/null; then
    echo "📦 安装sshpass..."
    apt update && apt install -y sshpass
fi

# 读取客户端服务器列表
echo "请输入客户端服务器信息（格式: IP 用户名 密码）"
echo "每行一台服务器，输入完成后按Ctrl+D结束"
echo "示例:"
echo "************* root password123"
echo "************* root password456"
echo ""

# 读取服务器列表
servers=()
while IFS= read -r line; do
    if [ -n "$line" ]; then
        servers+=("$line")
    fi
done

if [ ${#servers[@]} -eq 0 ]; then
    echo "❌ 没有输入任何服务器信息"
    exit 1
fi

echo ""
echo "📋 将要部署的服务器列表:"
for i in "${!servers[@]}"; do
    echo "$((i+1)). ${servers[i]}"
done

echo ""
read -p "确认开始部署？[y/N]: " confirm
if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
    echo "❌ 部署已取消"
    exit 1
fi

# 创建部署脚本
cat > /tmp/client_deploy.sh << 'EOF'
#!/bin/bash
SERVER_IP="*************"
CLIENT_NAME=$1

echo "🔧 安装Docker..."
# 更新系统
apt update && apt upgrade -y

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
systemctl start docker
systemctl enable docker

echo "📥 克隆项目..."
# 克隆项目
if [ -d "brush-captcha" ]; then
    cd brush-captcha && git pull
else
    git clone https://github.com/Brush-Bot/brush-captcha.git
    cd brush-captcha
fi

cd client

echo "📝 创建配置文件..."
# 创建配置文件
cat > config.yaml << EOL
concurrency: null
camoufox:
  solver_type:
    - HcaptchaCracker
    - AntiTurnstileTaskProxyLess
  headless: "true"
worker:
  name: "$CLIENT_NAME"
  wss_url: "ws://$SERVER_IP:8080/ws/worker/"
EOL

echo "🚀 启动客户端..."
# 启动客户端
docker compose up -d

echo "✅ 客户端 $CLIENT_NAME 部署完成"
EOF

# 开始批量部署
echo ""
echo "🚀 开始批量部署..."

success_count=0
failed_count=0

for i in "${!servers[@]}"; do
    server_info=(${servers[i]})
    server_ip=${server_info[0]}
    username=${server_info[1]}
    password=${server_info[2]}
    client_name="client-$(printf "%02d" $((i+1)))"
    
    echo ""
    echo "📡 部署服务器 $((i+1))/${#servers[@]}: $server_ip ($client_name)"
    
    # 复制部署脚本到远程服务器
    if sshpass -p "$password" scp -o StrictHostKeyChecking=no /tmp/client_deploy.sh "$username@$server_ip:/tmp/"; then
        # 执行部署脚本
        if sshpass -p "$password" ssh -o StrictHostKeyChecking=no "$username@$server_ip" "bash /tmp/client_deploy.sh $client_name"; then
            echo "✅ $server_ip ($client_name) 部署成功"
            ((success_count++))
        else
            echo "❌ $server_ip ($client_name) 部署失败"
            ((failed_count++))
        fi
    else
        echo "❌ $server_ip 连接失败"
        ((failed_count++))
    fi
    
    # 等待一下再部署下一台
    sleep 2
done

# 清理临时文件
rm -f /tmp/client_deploy.sh

echo ""
echo "📊 部署完成统计:"
echo "✅ 成功: $success_count 台"
echo "❌ 失败: $failed_count 台"
echo "📱 总计: ${#servers[@]} 台"
echo ""
echo "🌐 管理后台: http://*************:8080"
echo "👤 用户名: ddk"
echo "🔑 密码: Ddk19940316."
echo ""
echo "📋 查看客户端状态:"
echo "在管理后台可以看到所有连接的客户端节点"
