import asyncio
import psutil
import os
import gc
from datetime import datetime
from common.logger import get_logger, emoji

logger = get_logger("resource_monitor")

class ResourceMonitor:
    def __init__(self):
        self.process = psutil.Process()
        self.initial_memory = self.process.memory_info().rss
        self.initial_fds = self.get_open_fds()
        self.start_time = datetime.now()
        
    def get_open_fds(self):
        """获取当前进程打开的文件描述符数量"""
        try:
            return len(os.listdir(f'/proc/{os.getpid()}/fd'))
        except:
            try:
                return self.process.num_fds()
            except:
                return -1
    
    def get_memory_usage(self):
        """获取内存使用情况"""
        memory_info = self.process.memory_info()
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,  # 物理内存
            'vms_mb': memory_info.vms / 1024 / 1024,  # 虚拟内存
            'percent': self.process.memory_percent()
        }
    
    def get_cpu_usage(self):
        """获取CPU使用情况"""
        return {
            'percent': self.process.cpu_percent(),
            'num_threads': self.process.num_threads()
        }
    
    def get_system_stats(self):
        """获取系统整体统计"""
        return {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_percent': psutil.disk_usage('/').percent
        }
    
    def check_resource_leaks(self):
        """检查资源泄漏"""
        current_memory = self.process.memory_info().rss
        current_fds = self.get_open_fds()
        uptime = (datetime.now() - self.start_time).total_seconds()
        
        memory_growth = (current_memory - self.initial_memory) / 1024 / 1024  # MB
        fd_growth = current_fds - self.initial_fds if current_fds > 0 and self.initial_fds > 0 else 0
        
        # 计算每小时增长率
        hours = max(uptime / 3600, 0.1)  # 避免除零
        memory_growth_per_hour = memory_growth / hours
        fd_growth_per_hour = fd_growth / hours
        
        return {
            'uptime_hours': uptime / 3600,
            'memory_growth_mb': memory_growth,
            'memory_growth_per_hour': memory_growth_per_hour,
            'fd_growth': fd_growth,
            'fd_growth_per_hour': fd_growth_per_hour,
            'current_fds': current_fds,
            'is_memory_leak': memory_growth_per_hour > 50,  # 每小时增长超过50MB
            'is_fd_leak': fd_growth_per_hour > 10  # 每小时增长超过10个FD
        }
    
    def log_resource_status(self):
        """记录资源状态"""
        memory = self.get_memory_usage()
        cpu = self.get_cpu_usage()
        system = self.get_system_stats()
        leaks = self.check_resource_leaks()
        
        logger.info(emoji("MONITOR", 
            f"资源监控 - 内存: {memory['rss_mb']:.1f}MB ({memory['percent']:.1f}%), "
            f"CPU: {cpu['percent']:.1f}%, 线程: {cpu['num_threads']}, "
            f"文件描述符: {leaks['current_fds']}"
        ))
        
        if leaks['uptime_hours'] > 1:  # 运行超过1小时才检查泄漏
            if leaks['is_memory_leak']:
                logger.warning(emoji("WARNING", 
                    f"检测到内存泄漏！每小时增长: {leaks['memory_growth_per_hour']:.1f}MB, "
                    f"总增长: {leaks['memory_growth_mb']:.1f}MB"
                ))
            
            if leaks['is_fd_leak']:
                logger.warning(emoji("WARNING", 
                    f"检测到文件描述符泄漏！每小时增长: {leaks['fd_growth_per_hour']:.1f}, "
                    f"总增长: {leaks['fd_growth']}"
                ))
        
        return {
            'memory': memory,
            'cpu': cpu,
            'system': system,
            'leaks': leaks
        }
    
    def force_gc(self):
        """强制垃圾回收"""
        before_memory = self.process.memory_info().rss / 1024 / 1024
        
        # 执行垃圾回收
        collected = gc.collect()
        
        after_memory = self.process.memory_info().rss / 1024 / 1024
        freed_memory = before_memory - after_memory
        
        logger.info(emoji("GC", 
            f"垃圾回收完成 - 回收对象: {collected}, "
            f"释放内存: {freed_memory:.1f}MB"
        ))
        
        return {
            'collected_objects': collected,
            'freed_memory_mb': freed_memory,
            'before_memory_mb': before_memory,
            'after_memory_mb': after_memory
        }

# 全局资源监控器实例
resource_monitor = ResourceMonitor()

async def resource_monitor_loop():
    """资源监控循环"""
    last_gc_time = datetime.now()

    while True:
        try:
            # 记录资源状态
            resource_monitor.log_resource_status()

            # 每30分钟强制执行一次垃圾回收
            now = datetime.now()
            if (now - last_gc_time).total_seconds() >= 1800:  # 30分钟
                resource_monitor.force_gc()
                last_gc_time = now

        except Exception as e:
            logger.error(f"资源监控异常: {e}")

        await asyncio.sleep(600)  # 每10分钟检查一次

def get_resource_stats():
    """获取当前资源统计（供API调用）"""
    return resource_monitor.log_resource_status()
