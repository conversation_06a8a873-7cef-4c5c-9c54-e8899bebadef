{"name": "drawille-canvas-blessed-contrib", "version": "0.1.3", "description": "HTML5 Canvas API for drawille", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/madbence/node-drawille-canvas"}, "keywords": ["drawille", "canvas"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/madbence/node-drawille-canvas/issues"}, "homepage": "https://github.com/madbence/node-drawille-canvas", "dependencies": {"ansi-term": ">=0.0.2", "bresenham": "0.0.3", "drawille-blessed-contrib": ">=0.0.1", "x256": ">=0.0.1", "gl-matrix": "^2.1.0"}}