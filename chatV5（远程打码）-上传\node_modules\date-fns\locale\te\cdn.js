(() => {
var _window$dateFns;function _typeof(o) {"@babel/helpers - typeof";return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) {return typeof o;} : function (o) {return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o;}, _typeof(o);}function ownKeys(e, r) {var t = Object.keys(e);if (Object.getOwnPropertySymbols) {var o = Object.getOwnPropertySymbols(e);r && (o = o.filter(function (r) {return Object.getOwnPropertyDescriptor(e, r).enumerable;})), t.push.apply(t, o);}return t;}function _objectSpread(e) {for (var r = 1; r < arguments.length; r++) {var t = null != arguments[r] ? arguments[r] : {};r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {_defineProperty(e, r, t[r]);}) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));});}return e;}function _defineProperty(obj, key, value) {key = _toPropertyKey(key);if (key in obj) {Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });} else {obj[key] = value;}return obj;}function _toPropertyKey(t) {var i = _toPrimitive(t, "string");return "symbol" == _typeof(i) ? i : String(i);}function _toPrimitive(t, r) {if ("object" != _typeof(t) || !t) return t;var e = t[Symbol.toPrimitive];if (void 0 !== e) {var i = e.call(t, r || "default");if ("object" != _typeof(i)) return i;throw new TypeError("@@toPrimitive must return a primitive value.");}return ("string" === r ? String : Number)(t);}var __defProp = Object.defineProperty;
var __export = function __export(target, all) {
  for (var name in all)
  __defProp(target, name, {
    get: all[name],
    enumerable: true,
    configurable: true,
    set: function set(newValue) {return all[name] = function () {return newValue;};}
  });
};

// lib/locale/te/_lib/formatDistance.js
var formatDistanceLocale = {
  lessThanXSeconds: {
    standalone: {
      one: "\u0C38\u0C46\u0C15\u0C28\u0C41 \u0C15\u0C28\u0C4D\u0C28\u0C3E \u0C24\u0C15\u0C4D\u0C15\u0C41\u0C35",
      other: "{{count}} \u0C38\u0C46\u0C15\u0C28\u0C4D\u0C32 \u0C15\u0C28\u0C4D\u0C28\u0C3E \u0C24\u0C15\u0C4D\u0C15\u0C41\u0C35"
    },
    withPreposition: {
      one: "\u0C38\u0C46\u0C15\u0C28\u0C41",
      other: "{{count}} \u0C38\u0C46\u0C15\u0C28\u0C4D\u0C32"
    }
  },
  xSeconds: {
    standalone: {
      one: "\u0C12\u0C15 \u0C38\u0C46\u0C15\u0C28\u0C41",
      other: "{{count}} \u0C38\u0C46\u0C15\u0C28\u0C4D\u0C32"
    },
    withPreposition: {
      one: "\u0C12\u0C15 \u0C38\u0C46\u0C15\u0C28\u0C41",
      other: "{{count}} \u0C38\u0C46\u0C15\u0C28\u0C4D\u0C32"
    }
  },
  halfAMinute: {
    standalone: "\u0C05\u0C30 \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C02",
    withPreposition: "\u0C05\u0C30 \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C02"
  },
  lessThanXMinutes: {
    standalone: {
      one: "\u0C12\u0C15 \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C02 \u0C15\u0C28\u0C4D\u0C28\u0C3E \u0C24\u0C15\u0C4D\u0C15\u0C41\u0C35",
      other: "{{count}} \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C3E\u0C32 \u0C15\u0C28\u0C4D\u0C28\u0C3E \u0C24\u0C15\u0C4D\u0C15\u0C41\u0C35"
    },
    withPreposition: {
      one: "\u0C12\u0C15 \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C02",
      other: "{{count}} \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C3E\u0C32"
    }
  },
  xMinutes: {
    standalone: {
      one: "\u0C12\u0C15 \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C02",
      other: "{{count}} \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C3E\u0C32\u0C41"
    },
    withPreposition: {
      one: "\u0C12\u0C15 \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C02",
      other: "{{count}} \u0C28\u0C3F\u0C2E\u0C3F\u0C37\u0C3E\u0C32"
    }
  },
  aboutXHours: {
    standalone: {
      one: "\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C17\u0C02\u0C1F",
      other: "\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C17\u0C02\u0C1F\u0C32\u0C41"
    },
    withPreposition: {
      one: "\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C17\u0C02\u0C1F",
      other: "\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C17\u0C02\u0C1F\u0C32"
    }
  },
  xHours: {
    standalone: {
      one: "\u0C12\u0C15 \u0C17\u0C02\u0C1F",
      other: "{{count}} \u0C17\u0C02\u0C1F\u0C32\u0C41"
    },
    withPreposition: {
      one: "\u0C12\u0C15 \u0C17\u0C02\u0C1F",
      other: "{{count}} \u0C17\u0C02\u0C1F\u0C32"
    }
  },
  xDays: {
    standalone: {
      one: "\u0C12\u0C15 \u0C30\u0C4B\u0C1C\u0C41",
      other: "{{count}} \u0C30\u0C4B\u0C1C\u0C41\u0C32\u0C41"
    },
    withPreposition: {
      one: "\u0C12\u0C15 \u0C30\u0C4B\u0C1C\u0C41",
      other: "{{count}} \u0C30\u0C4B\u0C1C\u0C41\u0C32"
    }
  },
  aboutXWeeks: {
    standalone: {
      one: "\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C35\u0C3E\u0C30\u0C02",
      other: "\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C35\u0C3E\u0C30\u0C3E\u0C32\u0C41"
    },
    withPreposition: {
      one: "\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C35\u0C3E\u0C30\u0C02",
      other: "\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C35\u0C3E\u0C30\u0C3E\u0C32\u0C32"
    }
  },
  xWeeks: {
    standalone: {
      one: "\u0C12\u0C15 \u0C35\u0C3E\u0C30\u0C02",
      other: "{{count}} \u0C35\u0C3E\u0C30\u0C3E\u0C32\u0C41"
    },
    withPreposition: {
      one: "\u0C12\u0C15 \u0C35\u0C3E\u0C30\u0C02",
      other: "{{count}} \u0C35\u0C3E\u0C30\u0C3E\u0C32\u0C32"
    }
  },
  aboutXMonths: {
    standalone: {
      one: "\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C28\u0C46\u0C32",
      other: "\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C28\u0C46\u0C32\u0C32\u0C41"
    },
    withPreposition: {
      one: "\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C28\u0C46\u0C32",
      other: "\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C28\u0C46\u0C32\u0C32"
    }
  },
  xMonths: {
    standalone: {
      one: "\u0C12\u0C15 \u0C28\u0C46\u0C32",
      other: "{{count}} \u0C28\u0C46\u0C32\u0C32\u0C41"
    },
    withPreposition: {
      one: "\u0C12\u0C15 \u0C28\u0C46\u0C32",
      other: "{{count}} \u0C28\u0C46\u0C32\u0C32"
    }
  },
  aboutXYears: {
    standalone: {
      one: "\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",
      other: "\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32\u0C41"
    },
    withPreposition: {
      one: "\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 \u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",
      other: "\u0C38\u0C41\u0C2E\u0C3E\u0C30\u0C41 {{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32"
    }
  },
  xYears: {
    standalone: {
      one: "\u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",
      other: "{{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32\u0C41"
    },
    withPreposition: {
      one: "\u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",
      other: "{{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32"
    }
  },
  overXYears: {
    standalone: {
      one: "\u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02 \u0C2A\u0C48\u0C17\u0C3E",
      other: "{{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32\u0C15\u0C41 \u0C2A\u0C48\u0C17\u0C3E"
    },
    withPreposition: {
      one: "\u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",
      other: "{{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32"
    }
  },
  almostXYears: {
    standalone: {
      one: "\u0C26\u0C3E\u0C26\u0C3E\u0C2A\u0C41 \u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",
      other: "\u0C26\u0C3E\u0C26\u0C3E\u0C2A\u0C41 {{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32\u0C41"
    },
    withPreposition: {
      one: "\u0C26\u0C3E\u0C26\u0C3E\u0C2A\u0C41 \u0C12\u0C15 \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C02",
      other: "\u0C26\u0C3E\u0C26\u0C3E\u0C2A\u0C41 {{count}} \u0C38\u0C02\u0C35\u0C24\u0C4D\u0C38\u0C30\u0C3E\u0C32"
    }
  }
};
var formatDistance = function formatDistance(token, count, options) {
  var result;
  var tokenValue = options !== null && options !== void 0 && options.addSuffix ? formatDistanceLocale[token].withPreposition : formatDistanceLocale[token].standalone;
  if (typeof tokenValue === "string") {
    result = tokenValue;
  } else if (count === 1) {
    result = tokenValue.one;
  } else {
    result = tokenValue.other.replace("{{count}}", String(count));
  }
  if (options !== null && options !== void 0 && options.addSuffix) {
    if (options.comparison && options.comparison > 0) {
      return result + "\u0C32\u0C4B";
    } else {
      return result + " \u0C15\u0C4D\u0C30\u0C3F\u0C24\u0C02";
    }
  }
  return result;
};

// lib/locale/_lib/buildFormatLongFn.js
function buildFormatLongFn(args) {
  return function () {var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    var width = options.width ? String(options.width) : args.defaultWidth;
    var format = args.formats[width] || args.formats[args.defaultWidth];
    return format;
  };
}

// lib/locale/te/_lib/formatLong.js
var dateFormats = {
  full: "d, MMMM y, EEEE",
  long: "d MMMM, y",
  medium: "d MMM, y",
  short: "dd-MM-yy"
};
var timeFormats = {
  full: "h:mm:ss a zzzz",
  long: "h:mm:ss a z",
  medium: "h:mm:ss a",
  short: "h:mm a"
};
var dateTimeFormats = {
  full: "{{date}} {{time}}'\u0C15\u0C3F'",
  long: "{{date}} {{time}}'\u0C15\u0C3F'",
  medium: "{{date}} {{time}}",
  short: "{{date}} {{time}}"
};
var formatLong = {
  date: buildFormatLongFn({
    formats: dateFormats,
    defaultWidth: "full"
  }),
  time: buildFormatLongFn({
    formats: timeFormats,
    defaultWidth: "full"
  }),
  dateTime: buildFormatLongFn({
    formats: dateTimeFormats,
    defaultWidth: "full"
  })
};

// lib/locale/te/_lib/formatRelative.js
var formatRelativeLocale = {
  lastWeek: "'\u0C17\u0C24' eeee p",
  yesterday: "'\u0C28\u0C3F\u0C28\u0C4D\u0C28' p",
  today: "'\u0C08 \u0C30\u0C4B\u0C1C\u0C41' p",
  tomorrow: "'\u0C30\u0C47\u0C2A\u0C41' p",
  nextWeek: "'\u0C24\u0C26\u0C41\u0C2A\u0C30\u0C3F' eeee p",
  other: "P"
};
var formatRelative = function formatRelative(token, _date, _baseDate, _options) {return formatRelativeLocale[token];};

// lib/locale/_lib/buildLocalizeFn.js
function buildLocalizeFn(args) {
  return function (value, options) {
    var context = options !== null && options !== void 0 && options.context ? String(options.context) : "standalone";
    var valuesArray;
    if (context === "formatting" && args.formattingValues) {
      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;
      var width = options !== null && options !== void 0 && options.width ? String(options.width) : defaultWidth;
      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];
    } else {
      var _defaultWidth = args.defaultWidth;
      var _width = options !== null && options !== void 0 && options.width ? String(options.width) : args.defaultWidth;
      valuesArray = args.values[_width] || args.values[_defaultWidth];
    }
    var index = args.argumentCallback ? args.argumentCallback(value) : value;
    return valuesArray[index];
  };
}

// lib/locale/te/_lib/localize.js
var eraValues = {
  narrow: ["\u0C15\u0C4D\u0C30\u0C40.\u0C2A\u0C42.", "\u0C15\u0C4D\u0C30\u0C40.\u0C36."],
  abbreviated: ["\u0C15\u0C4D\u0C30\u0C40.\u0C2A\u0C42.", "\u0C15\u0C4D\u0C30\u0C40.\u0C36."],
  wide: ["\u0C15\u0C4D\u0C30\u0C40\u0C38\u0C4D\u0C24\u0C41 \u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C02", "\u0C15\u0C4D\u0C30\u0C40\u0C38\u0C4D\u0C24\u0C41\u0C36\u0C15\u0C02"]
};
var quarterValues = {
  narrow: ["1", "2", "3", "4"],
  abbreviated: ["\u0C24\u0C4D\u0C30\u0C481", "\u0C24\u0C4D\u0C30\u0C482", "\u0C24\u0C4D\u0C30\u0C483", "\u0C24\u0C4D\u0C30\u0C484"],
  wide: ["1\u0C35 \u0C24\u0C4D\u0C30\u0C48\u0C2E\u0C3E\u0C38\u0C3F\u0C15\u0C02", "2\u0C35 \u0C24\u0C4D\u0C30\u0C48\u0C2E\u0C3E\u0C38\u0C3F\u0C15\u0C02", "3\u0C35 \u0C24\u0C4D\u0C30\u0C48\u0C2E\u0C3E\u0C38\u0C3F\u0C15\u0C02", "4\u0C35 \u0C24\u0C4D\u0C30\u0C48\u0C2E\u0C3E\u0C38\u0C3F\u0C15\u0C02"]
};
var monthValues = {
  narrow: ["\u0C1C", "\u0C2B\u0C3F", "\u0C2E\u0C3E", "\u0C0F", "\u0C2E\u0C47", "\u0C1C\u0C42", "\u0C1C\u0C41", "\u0C06", "\u0C38\u0C46", "\u0C05", "\u0C28", "\u0C21\u0C3F"],
  abbreviated: [
  "\u0C1C\u0C28",
  "\u0C2B\u0C3F\u0C2C\u0C4D\u0C30",
  "\u0C2E\u0C3E\u0C30\u0C4D\u0C1A\u0C3F",
  "\u0C0F\u0C2A\u0C4D\u0C30\u0C3F",
  "\u0C2E\u0C47",
  "\u0C1C\u0C42\u0C28\u0C4D",
  "\u0C1C\u0C41\u0C32\u0C48",
  "\u0C06\u0C17",
  "\u0C38\u0C46\u0C2A\u0C4D\u0C1F\u0C46\u0C02",
  "\u0C05\u0C15\u0C4D\u0C1F\u0C4B",
  "\u0C28\u0C35\u0C02",
  "\u0C21\u0C3F\u0C38\u0C46\u0C02"],

  wide: [
  "\u0C1C\u0C28\u0C35\u0C30\u0C3F",
  "\u0C2B\u0C3F\u0C2C\u0C4D\u0C30\u0C35\u0C30\u0C3F",
  "\u0C2E\u0C3E\u0C30\u0C4D\u0C1A\u0C3F",
  "\u0C0F\u0C2A\u0C4D\u0C30\u0C3F\u0C32\u0C4D",
  "\u0C2E\u0C47",
  "\u0C1C\u0C42\u0C28\u0C4D",
  "\u0C1C\u0C41\u0C32\u0C48",
  "\u0C06\u0C17\u0C38\u0C4D\u0C1F\u0C41",
  "\u0C38\u0C46\u0C2A\u0C4D\u0C1F\u0C46\u0C02\u0C2C\u0C30\u0C4D",
  "\u0C05\u0C15\u0C4D\u0C1F\u0C4B\u0C2C\u0C30\u0C4D",
  "\u0C28\u0C35\u0C02\u0C2C\u0C30\u0C4D",
  "\u0C21\u0C3F\u0C38\u0C46\u0C02\u0C2C\u0C30\u0C4D"]

};
var dayValues = {
  narrow: ["\u0C06", "\u0C38\u0C4B", "\u0C2E", "\u0C2C\u0C41", "\u0C17\u0C41", "\u0C36\u0C41", "\u0C36"],
  short: ["\u0C06\u0C26\u0C3F", "\u0C38\u0C4B\u0C2E", "\u0C2E\u0C02\u0C17\u0C33", "\u0C2C\u0C41\u0C27", "\u0C17\u0C41\u0C30\u0C41", "\u0C36\u0C41\u0C15\u0C4D\u0C30", "\u0C36\u0C28\u0C3F"],
  abbreviated: ["\u0C06\u0C26\u0C3F", "\u0C38\u0C4B\u0C2E", "\u0C2E\u0C02\u0C17\u0C33", "\u0C2C\u0C41\u0C27", "\u0C17\u0C41\u0C30\u0C41", "\u0C36\u0C41\u0C15\u0C4D\u0C30", "\u0C36\u0C28\u0C3F"],
  wide: [
  "\u0C06\u0C26\u0C3F\u0C35\u0C3E\u0C30\u0C02",
  "\u0C38\u0C4B\u0C2E\u0C35\u0C3E\u0C30\u0C02",
  "\u0C2E\u0C02\u0C17\u0C33\u0C35\u0C3E\u0C30\u0C02",
  "\u0C2C\u0C41\u0C27\u0C35\u0C3E\u0C30\u0C02",
  "\u0C17\u0C41\u0C30\u0C41\u0C35\u0C3E\u0C30\u0C02",
  "\u0C36\u0C41\u0C15\u0C4D\u0C30\u0C35\u0C3E\u0C30\u0C02",
  "\u0C36\u0C28\u0C3F\u0C35\u0C3E\u0C30\u0C02"]

};
var dayPeriodValues = {
  narrow: {
    am: "\u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    pm: "\u0C05\u0C2A\u0C30\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    midnight: "\u0C05\u0C30\u0C4D\u0C27\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F",
    noon: "\u0C2E\u0C3F\u0C1F\u0C4D\u0C1F\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    morning: "\u0C09\u0C26\u0C2F\u0C02",
    afternoon: "\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    evening: "\u0C38\u0C3E\u0C2F\u0C02\u0C24\u0C4D\u0C30\u0C02",
    night: "\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F"
  },
  abbreviated: {
    am: "\u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    pm: "\u0C05\u0C2A\u0C30\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    midnight: "\u0C05\u0C30\u0C4D\u0C27\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F",
    noon: "\u0C2E\u0C3F\u0C1F\u0C4D\u0C1F\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    morning: "\u0C09\u0C26\u0C2F\u0C02",
    afternoon: "\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    evening: "\u0C38\u0C3E\u0C2F\u0C02\u0C24\u0C4D\u0C30\u0C02",
    night: "\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F"
  },
  wide: {
    am: "\u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    pm: "\u0C05\u0C2A\u0C30\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    midnight: "\u0C05\u0C30\u0C4D\u0C27\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F",
    noon: "\u0C2E\u0C3F\u0C1F\u0C4D\u0C1F\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    morning: "\u0C09\u0C26\u0C2F\u0C02",
    afternoon: "\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    evening: "\u0C38\u0C3E\u0C2F\u0C02\u0C24\u0C4D\u0C30\u0C02",
    night: "\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F"
  }
};
var formattingDayPeriodValues = {
  narrow: {
    am: "\u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    pm: "\u0C05\u0C2A\u0C30\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    midnight: "\u0C05\u0C30\u0C4D\u0C27\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F",
    noon: "\u0C2E\u0C3F\u0C1F\u0C4D\u0C1F\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    morning: "\u0C09\u0C26\u0C2F\u0C02",
    afternoon: "\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    evening: "\u0C38\u0C3E\u0C2F\u0C02\u0C24\u0C4D\u0C30\u0C02",
    night: "\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F"
  },
  abbreviated: {
    am: "\u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    pm: "\u0C05\u0C2A\u0C30\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    midnight: "\u0C05\u0C30\u0C4D\u0C27\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F",
    noon: "\u0C2E\u0C3F\u0C1F\u0C4D\u0C1F\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    morning: "\u0C09\u0C26\u0C2F\u0C02",
    afternoon: "\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    evening: "\u0C38\u0C3E\u0C2F\u0C02\u0C24\u0C4D\u0C30\u0C02",
    night: "\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F"
  },
  wide: {
    am: "\u0C2A\u0C42\u0C30\u0C4D\u0C35\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    pm: "\u0C05\u0C2A\u0C30\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    midnight: "\u0C05\u0C30\u0C4D\u0C27\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F",
    noon: "\u0C2E\u0C3F\u0C1F\u0C4D\u0C1F\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    morning: "\u0C09\u0C26\u0C2F\u0C02",
    afternoon: "\u0C2E\u0C27\u0C4D\u0C2F\u0C3E\u0C39\u0C4D\u0C28\u0C02",
    evening: "\u0C38\u0C3E\u0C2F\u0C02\u0C24\u0C4D\u0C30\u0C02",
    night: "\u0C30\u0C3E\u0C24\u0C4D\u0C30\u0C3F"
  }
};
var ordinalNumber = function ordinalNumber(dirtyNumber, _options) {
  var number = Number(dirtyNumber);
  return number + "\u0C35";
};
var localize = {
  ordinalNumber: ordinalNumber,
  era: buildLocalizeFn({
    values: eraValues,
    defaultWidth: "wide"
  }),
  quarter: buildLocalizeFn({
    values: quarterValues,
    defaultWidth: "wide",
    argumentCallback: function argumentCallback(quarter) {return quarter - 1;}
  }),
  month: buildLocalizeFn({
    values: monthValues,
    defaultWidth: "wide"
  }),
  day: buildLocalizeFn({
    values: dayValues,
    defaultWidth: "wide"
  }),
  dayPeriod: buildLocalizeFn({
    values: dayPeriodValues,
    defaultWidth: "wide",
    formattingValues: formattingDayPeriodValues,
    defaultFormattingWidth: "wide"
  })
};

// lib/locale/_lib/buildMatchFn.js
function buildMatchFn(args) {
  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    var width = options.width;
    var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];
    var matchResult = string.match(matchPattern);
    if (!matchResult) {
      return null;
    }
    var matchedString = matchResult[0];
    var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];
    var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function (pattern) {return pattern.test(matchedString);}) : findKey(parsePatterns, function (pattern) {return pattern.test(matchedString);});
    var value;
    value = args.valueCallback ? args.valueCallback(key) : key;
    value = options.valueCallback ? options.valueCallback(value) : value;
    var rest = string.slice(matchedString.length);
    return { value: value, rest: rest };
  };
}
function findKey(object, predicate) {
  for (var key in object) {
    if (Object.prototype.hasOwnProperty.call(object, key) && predicate(object[key])) {
      return key;
    }
  }
  return;
}
function findIndex(array, predicate) {
  for (var key = 0; key < array.length; key++) {
    if (predicate(array[key])) {
      return key;
    }
  }
  return;
}

// lib/locale/_lib/buildMatchPatternFn.js
function buildMatchPatternFn(args) {
  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    var matchResult = string.match(args.matchPattern);
    if (!matchResult)
    return null;
    var matchedString = matchResult[0];
    var parseResult = string.match(args.parsePattern);
    if (!parseResult)
    return null;
    var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];
    value = options.valueCallback ? options.valueCallback(value) : value;
    var rest = string.slice(matchedString.length);
    return { value: value, rest: rest };
  };
}

// lib/locale/te/_lib/match.js
var matchOrdinalNumberPattern = /^(\d+)(వ)?/i;
var parseOrdinalNumberPattern = /\d+/i;
var matchEraPatterns = {
  narrow: /^(క్రీ\.పూ\.|క్రీ\.శ\.)/i,
  abbreviated: /^(క్రీ\.?\s?పూ\.?|ప్ర\.?\s?శ\.?\s?పూ\.?|క్రీ\.?\s?శ\.?|సా\.?\s?శ\.?)/i,
  wide: /^(క్రీస్తు పూర్వం|ప్రస్తుత శకానికి పూర్వం|క్రీస్తు శకం|ప్రస్తుత శకం)/i
};
var parseEraPatterns = {
  any: [/^(పూ|శ)/i, /^సా/i]
};
var matchQuarterPatterns = {
  narrow: /^[1234]/i,
  abbreviated: /^త్రై[1234]/i,
  wide: /^[1234](వ)? త్రైమాసికం/i
};
var parseQuarterPatterns = {
  any: [/1/i, /2/i, /3/i, /4/i]
};
var matchMonthPatterns = {
  narrow: /^(జూ|జు|జ|ఫి|మా|ఏ|మే|ఆ|సె|అ|న|డి)/i,
  abbreviated: /^(జన|ఫిబ్ర|మార్చి|ఏప్రి|మే|జూన్|జులై|ఆగ|సెప్|అక్టో|నవ|డిసె)/i,
  wide: /^(జనవరి|ఫిబ్రవరి|మార్చి|ఏప్రిల్|మే|జూన్|జులై|ఆగస్టు|సెప్టెంబర్|అక్టోబర్|నవంబర్|డిసెంబర్)/i
};
var parseMonthPatterns = {
  narrow: [
  /^జ/i,
  /^ఫి/i,
  /^మా/i,
  /^ఏ/i,
  /^మే/i,
  /^జూ/i,
  /^జు/i,
  /^ఆ/i,
  /^సె/i,
  /^అ/i,
  /^న/i,
  /^డి/i],

  any: [
  /^జన/i,
  /^ఫి/i,
  /^మా/i,
  /^ఏ/i,
  /^మే/i,
  /^జూన్/i,
  /^జులై/i,
  /^ఆగ/i,
  /^సె/i,
  /^అ/i,
  /^న/i,
  /^డి/i]

};
var matchDayPatterns = {
  narrow: /^(ఆ|సో|మ|బు|గు|శు|శ)/i,
  short: /^(ఆది|సోమ|మం|బుధ|గురు|శుక్ర|శని)/i,
  abbreviated: /^(ఆది|సోమ|మం|బుధ|గురు|శుక్ర|శని)/i,
  wide: /^(ఆదివారం|సోమవారం|మంగళవారం|బుధవారం|గురువారం|శుక్రవారం|శనివారం)/i
};
var parseDayPatterns = {
  narrow: [/^ఆ/i, /^సో/i, /^మ/i, /^బు/i, /^గు/i, /^శు/i, /^శ/i],
  any: [/^ఆది/i, /^సోమ/i, /^మం/i, /^బుధ/i, /^గురు/i, /^శుక్ర/i, /^శని/i]
};
var matchDayPeriodPatterns = {
  narrow: /^(పూర్వాహ్నం|అపరాహ్నం|అర్ధరాత్రి|మిట్టమధ్యాహ్నం|ఉదయం|మధ్యాహ్నం|సాయంత్రం|రాత్రి)/i,
  any: /^(పూర్వాహ్నం|అపరాహ్నం|అర్ధరాత్రి|మిట్టమధ్యాహ్నం|ఉదయం|మధ్యాహ్నం|సాయంత్రం|రాత్రి)/i
};
var parseDayPeriodPatterns = {
  any: {
    am: /^పూర్వాహ్నం/i,
    pm: /^అపరాహ్నం/i,
    midnight: /^అర్ధ/i,
    noon: /^మిట్ట/i,
    morning: /ఉదయం/i,
    afternoon: /మధ్యాహ్నం/i,
    evening: /సాయంత్రం/i,
    night: /రాత్రి/i
  }
};
var match = {
  ordinalNumber: buildMatchPatternFn({
    matchPattern: matchOrdinalNumberPattern,
    parsePattern: parseOrdinalNumberPattern,
    valueCallback: function valueCallback(value) {return parseInt(value, 10);}
  }),
  era: buildMatchFn({
    matchPatterns: matchEraPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseEraPatterns,
    defaultParseWidth: "any"
  }),
  quarter: buildMatchFn({
    matchPatterns: matchQuarterPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseQuarterPatterns,
    defaultParseWidth: "any",
    valueCallback: function valueCallback(index) {return index + 1;}
  }),
  month: buildMatchFn({
    matchPatterns: matchMonthPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseMonthPatterns,
    defaultParseWidth: "any"
  }),
  day: buildMatchFn({
    matchPatterns: matchDayPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseDayPatterns,
    defaultParseWidth: "any"
  }),
  dayPeriod: buildMatchFn({
    matchPatterns: matchDayPeriodPatterns,
    defaultMatchWidth: "any",
    parsePatterns: parseDayPeriodPatterns,
    defaultParseWidth: "any"
  })
};

// lib/locale/te.js
var te = {
  code: "te",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 0,
    firstWeekContainsDate: 1
  }
};

// lib/locale/te/cdn.js
window.dateFns = _objectSpread(_objectSpread({},
window.dateFns), {}, {
  locale: _objectSpread(_objectSpread({}, (_window$dateFns =
  window.dateFns) === null || _window$dateFns === void 0 ? void 0 : _window$dateFns.locale), {}, {
    te: te }) });



//# debugId=46A0E1872EF5A16F64756E2164756E21

//# sourceMappingURL=cdn.js.map
})();