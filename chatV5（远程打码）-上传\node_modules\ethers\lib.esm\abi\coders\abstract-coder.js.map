{"version": 3, "file": "abstract-coder.js", "sourceRoot": "", "sources": ["../../../src.ts/abi/coders/abstract-coder.ts"], "names": [], "mappings": "AACA,OAAO,EACH,gBAAgB,EAAE,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAC1D,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAC7B,MAAM,EAAE,cAAc;AACtB,aAAa;EAChB,MAAM,sBAAsB,CAAC;AAI9B;;GAEG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAW,EAAE,CAAC;AACnC,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;AAEzC,qEAAqE;AACrE,iEAAiE;AACjE,MAAM,cAAc,GAAG,CAAE,MAAM,CAAE,CAAC;AAElC,MAAM,MAAM,GAAG,EAAG,CAAC;AAEnB,MAAM,WAAW,GAAkD,IAAI,OAAO,EAAE,CAAC;AAEjF,SAAS,QAAQ,CAAC,MAAc;IAC5B,OAAO,WAAW,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC;AACpC,CAAC;AACD,SAAS,QAAQ,CAAC,MAAc,EAAE,KAAmC;IACjE,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACnC,CAAC;AAED,SAAS,UAAU,CAAC,IAAY,EAAE,KAAY;IAC1C,MAAM,OAAO,GAAG,IAAI,KAAK,CAAC,0DAA2D,IAAK,EAAE,CAAC,CAAC;IACxF,OAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;IAC7B,MAAM,OAAO,CAAC;AAClB,CAAC;AAED,SAAS,QAAQ,CAAC,KAAmC,EAAE,KAAa,EAAE,IAAc;IAChF,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAC1B,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC7B,IAAI,IAAI,YAAY,MAAM,EAAE;gBACxB,OAAO,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;aAC/C;YACD,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC,CAAC;KACN;IAED,OAAuB,KAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;QACxD,IAAI,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,EAAE;YAClB,IAAI,IAAI,IAAI,IAAI,YAAY,MAAM,EAAE;gBAChC,IAAI,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;aAC/C;YACD,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;SACtB;QACD,OAAO,KAAK,CAAC;IACjB,CAAC,EAAuB,EAAG,CAAC,CAAC;AACjC,CAAC;AAGD;;;;;;GAMG;AACH,MAAM,OAAO,MAAO,SAAQ,KAAU;IAClC,8DAA8D;IAC9D,0DAA0D;IAC1D,gBAAgB;IACP,MAAM,CAA+B;IAI9C;;OAEG;IACH,YAAY,GAAG,IAAgB;QAC3B,oDAAoD;QACpD,uDAAuD;QACvD,wDAAwD;QACxD,uDAAuD;QACvD,kDAAkD;QAElD,2EAA2E;QAC3E,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,KAAK,GAAe,IAAI,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,KAAK,GAAyB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAG,CAAC,CAAC,KAAK,EAAE,CAAC;QAE3D,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,KAAK,KAAK,MAAM,EAAE;YAClB,KAAK,GAAG,IAAI,CAAC;YACb,KAAK,GAAG,EAAG,CAAC;YACZ,IAAI,GAAG,KAAK,CAAC;SAChB;QAED,yDAAyD;QACzD,kCAAkC;QAClC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACpB,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAExD,uBAAuB;QACvB,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;YAC5C,IAAI,OAAM,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE;gBAC3B,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;aAC/C;YACD,OAAO,KAAK,CAAC;QACjB,CAAC,EAAuB,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;QAErC,kCAAkC;QAClC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACnD,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,IAAI,IAAI,IAAI,IAAI,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBAC5C,OAAO,IAAI,CAAC;aACf;YACD,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC;QAEL,0DAA0D;QAC1D,IAAI,CAAC,MAAM,GAAG,EAAG,CAAC;QAClB,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;YAAE,KAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAAE;QAE/C,IAAI,CAAC,IAAI,EAAE;YAAE,OAAO;SAAE;QAEtB,gCAAgC;QAChC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEpB,yDAAyD;QACzD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE;YAC1B,GAAG,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;gBAC5B,IAAI,OAAM,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE;oBAE3B,iBAAiB;oBACjB,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;wBACxB,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;wBACxC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;4BACnC,MAAM,IAAI,UAAU,CAAC,qBAAqB,CAAC,CAAC;yBAC/C;wBAED,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;wBAC3B,IAAI,IAAI,YAAY,KAAK,EAAE;4BACvB,UAAU,CAAC,SAAU,KAAM,EAAE,EAAE,IAAI,CAAC,CAAC;yBACxC;wBACD,OAAO,IAAI,CAAC;qBACf;oBAED,0DAA0D;oBAC1D,IAAI,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;wBACnC,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;qBAC9C;oBAED,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;oBAC3B,IAAI,KAAK,YAAY,QAAQ,EAAE;wBAC3B,kDAAkD;wBAClD,6HAA6H;wBAC7H,OAAO,UAAoB,GAAG,IAAgB;4BAC1C,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;wBACjE,CAAC,CAAC;qBAEL;yBAAM,IAAI,CAAC,CAAC,IAAI,IAAI,MAAM,CAAC,EAAE;wBAC1B,yBAAyB;wBACzB,OAAO,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA,CAAC,CAAC,IAAI,EAAE,CAAE,IAAI,CAAE,CAAC,CAAC;qBAC9E;iBACJ;gBAED,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC/C,CAAC;SACJ,CAAC,CAAC;QACH,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QAChC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;OAMG;IACH,OAAO,CAAC,IAAc;QAClB,MAAM,MAAM,GAAe,EAAG,CAAC;QAC/B,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACzB,IAAI,IAAI,YAAY,KAAK,EAAE;gBAAE,UAAU,CAAC,SAAU,KAAM,EAAE,EAAE,IAAI,CAAC,CAAC;aAAE;YACpE,IAAI,IAAI,IAAI,IAAI,YAAY,MAAM,EAAE;gBAChC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;aAC7B;YACD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;;OAOG;IACH,QAAQ,CAAC,IAAc;QACnB,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC7B,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;YAEvC,MAAM,CAAC,IAAI,IAAI,IAAI,EAAE,kBAAmB,KAAM,UAAU,EAAE,uBAAuB,EAAE;gBAC/E,SAAS,EAAE,YAAY;aAC1B,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACvC,CAAC,EAAuB,EAAE,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAA0B,EAAE,GAAwB;QACtD,IAAI,KAAK,IAAI,IAAI,EAAE;YAAE,KAAK,GAAG,CAAC,CAAC;SAAE;QACjC,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC;YACrB,IAAI,KAAK,GAAG,CAAC,EAAE;gBAAE,KAAK,GAAG,CAAC,CAAC;aAAE;SAChC;QAED,IAAI,GAAG,IAAI,IAAI,EAAE;YAAE,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;SAAE;QACvC,IAAI,GAAG,GAAG,CAAC,EAAE;YACT,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC;YACnB,IAAI,GAAG,GAAG,CAAC,EAAE;gBAAE,GAAG,GAAG,CAAC,CAAC;aAAE;SAC5B;QACD,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE;YAAE,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;SAAE;QAE7C,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QAE9B,MAAM,MAAM,GAAe,EAAG,EAAE,KAAK,GAAyB,EAAG,CAAC;QAClE,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC9B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACzB;QAED,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAA4D,EAAE,OAAa;QAC9E,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QAE9B,MAAM,MAAM,GAAe,EAAG,EAAE,KAAK,GAAyB,EAAG,CAAC;QAClE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACrB,IAAI,IAAI,YAAY,KAAK,EAAE;gBACvB,UAAU,CAAC,SAAU,CAAE,EAAE,EAAE,IAAI,CAAC,CAAC;aACpC;YAED,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE;gBACvC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClB,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;aACzB;SACJ;QAED,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,GAAG,CAAsB,QAAsD,EAAE,OAAa;QAC1F,MAAM,MAAM,GAAa,EAAG,CAAC;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACrB,IAAI,IAAI,YAAY,KAAK,EAAE;gBACvB,UAAU,CAAC,SAAU,CAAE,EAAE,EAAE,IAAI,CAAC,CAAC;aACpC;YAED,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;SACtD;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAGD;;;;;;;OAOG;IACH,QAAQ,CAAC,IAAY;QACjB,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YAAE,OAAO,SAAS,CAAC;SAAE;QAEvC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QAE1B,IAAI,KAAK,YAAY,KAAK,EAAE;YACxB,UAAU,CAAC,YAAa,IAAI,CAAC,SAAS,CAAC,IAAI,CAAE,EAAE,EAAQ,KAAM,CAAC,KAAK,CAAC,CAAC;SACxE;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,SAAS,CAAC,KAAiB,EAAE,IAA2B;QAC3D,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IAC3C,CAAC;CACJ;AAED;;;;;;;;;;;;GAYG;AACH,MAAM,UAAU,iBAAiB,CAAC,MAAc;IAC5C,gCAAgC;IAChC,MAAM,MAAM,GAA0D,EAAG,CAAC;IAE1E,MAAM,WAAW,GAAG,UAAS,IAA4B,EAAE,MAAW;QAClE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAAE,OAAO;SAAE;QACvC,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;YACpB,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAC/B,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEpB,IAAI;gBACC,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;aACxC;YAAC,OAAO,KAAU,EAAE;gBACjB,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;aAClD;SACJ;IACL,CAAC,CAAA;IACD,WAAW,CAAC,EAAG,EAAE,MAAM,CAAC,CAAC;IAEzB,OAAO,MAAM,CAAC;AAElB,CAAC;AAED,SAAS,QAAQ,CAAC,KAAmB;IACjC,IAAI,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;IAE7B,MAAM,CAAE,KAAK,CAAC,MAAM,IAAI,QAAQ,EAAE,qBAAqB,EACnD,gBAAgB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IAEjF,IAAI,KAAK,CAAC,MAAM,KAAK,QAAQ,EAAE;QAC3B,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,CAAE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC,EAAE,KAAK,CAAE,CAAC,CAAC,CAAC;KACnF;IAED,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,MAAM,OAAgB,KAAK;IAEvB,kBAAkB;IAClB,2CAA2C;IAClC,IAAI,CAAU;IAEvB,sDAAsD;IACtD,qEAAqE;IAC5D,IAAI,CAAU;IAEvB,qEAAqE;IACrE,uCAAuC;IAC9B,SAAS,CAAU;IAE5B,gCAAgC;IAChC,+DAA+D;IAC/D,sEAAsE;IAC7D,OAAO,CAAW;IAE3B,YAAY,IAAY,EAAE,IAAY,EAAE,SAAiB,EAAE,OAAgB;QACvE,gBAAgB,CAAQ,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;YAC9D,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS;SAC1E,CAAC,CAAC;IACP,CAAC;IAED,WAAW,CAAC,OAAe,EAAE,KAAU;QACnC,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAC1D,CAAC;CAMJ;AAED;;GAEG;AACH,MAAM,OAAO,MAAM;IACf,yDAAyD;IACzD,KAAK,CAAoB;IACzB,WAAW,CAAS;IAEpB;QACI,IAAI,CAAC,KAAK,GAAG,EAAG,CAAC;QACjB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;IACzB,CAAC;IAED,IAAI,IAAI;QACJ,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IACD,IAAI,MAAM,KAAa,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IAEjD,UAAU,CAAC,IAAgB;QACvB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC;QAChC,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,YAAY,CAAC,MAAc;QACvB,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACtD,CAAC;IAED,wDAAwD;IACxD,UAAU,CAAC,KAAgB;QACvB,IAAI,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;QAChC,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9C,IAAI,aAAa,EAAE;YACf,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,CAAE,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAE,CAAC,CAAC,CAAA;SACxE;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;IAED,8CAA8C;IAC9C,UAAU,CAAC,KAAmB;QAC1B,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED,gEAAgE;IAChE,oCAAoC;IACpC,mBAAmB;QACf,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzB,IAAI,CAAC,WAAW,IAAI,QAAQ,CAAC;QAC7B,OAAO,CAAC,KAAmB,EAAE,EAAE;YAC3B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC,CAAC;IACN,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,MAAM;IACf,iEAAiE;IACjE,kEAAkE;IAClE,4DAA4D;IAC5D,mDAAmD;IAC1C,UAAU,CAAW;IAErB,KAAK,CAAa;IAC3B,OAAO,CAAS;IAEhB,UAAU,CAAS;IACnB,OAAO,CAAgB;IACvB,aAAa,CAAS;IAEtB,YAAY,IAAe,EAAE,UAAoB,EAAE,YAAqB;QACpE,gBAAgB,CAAS,IAAI,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;QAE7D,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,CAAC,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAA,CAAC,CAAC,IAAI,CAAC;QAEjE,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;IACrB,CAAC;IAED,IAAI,IAAI,KAAa,OAAO,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAClD,IAAI,UAAU,KAAa,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IACtD,IAAI,QAAQ,KAAa,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAC/C,IAAI,KAAK,KAAiB,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAE9D,mBAAmB,CAAC,KAAa;QAC7B,IAAI,IAAI,CAAC,OAAO,EAAE;YAAE,OAAO,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SAAE;QAErE,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC;QAEzB,6CAA6C;QAC7C,MAAM,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,EAAE,kDAAmD,IAAI,CAAC,aAAc,+DAA+D,EAAG,gBAAgB,EAAE;YAChP,MAAM,EAAE,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO;YACtD,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE;gBACjB,SAAS,EAAE,IAAI,CAAC,UAAU;gBAC1B,UAAU,EAAE,IAAI,CAAC,UAAU;aAC9B;SACJ,CAAC,CAAC;IACP,CAAC;IAED,UAAU,CAAC,MAAc,EAAE,MAAc,EAAE,KAAe;QACtD,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC;QAC5D,IAAI,IAAI,CAAC,OAAO,GAAG,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YAClD,IAAI,IAAI,CAAC,UAAU,IAAI,KAAK,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBACxE,aAAa,GAAG,MAAM,CAAC;aAC1B;iBAAM;gBACH,MAAM,CAAC,KAAK,EAAE,oBAAoB,EAAE,gBAAgB,EAAE;oBAClD,MAAM,EAAE,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;oBAChC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;oBACzB,MAAM,EAAE,IAAI,CAAC,OAAO,GAAG,aAAa;iBACvC,CAAC,CAAC;aACN;SACJ;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC,CAAA;IACvE,CAAC;IAED,gEAAgE;IAChE,SAAS,CAAC,MAAc;QACpB,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACxG,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,aAAa;IACb,SAAS,CAAC,MAAc,EAAE,KAAe;QACrC,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;QAChD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QACjC,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC;QAC7B,oDAAoD;QACpD,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IAClC,CAAC;IAED,wBAAwB;IACxB,SAAS;QACL,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC9C,CAAC;IAED,SAAS;QACL,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC9C,CAAC;CACJ"}