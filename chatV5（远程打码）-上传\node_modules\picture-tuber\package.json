{"name": "picture-tuber", "description": "render 256 color images on the terminal", "version": "1.0.2", "repository": {"type": "git", "url": "https://github.com/lirantal/picture-tuber"}, "main": "index.js", "bin": {"picture-tube": "bin/tube.js"}, "keywords": ["png", "viewer", "terminal", "ansi"], "dependencies": {"event-stream": "~0.9.8", "buffers": "~0.1.1", "charm": "~0.1.0", "png-js": "~0.1.0", "x256": "~0.0.1", "optimist": "~0.3.4"}, "devDependencies": {}, "engines": {"node": ">=0.4.0"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}}