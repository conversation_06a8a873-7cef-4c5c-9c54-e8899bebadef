client/                                                                                             0000777 0000000 0000000 00000000000 15022030435 007220  5                                                                                                    ustar                                                                                                                                                                                                                                                          client/common/                                                                                      0000777 0000000 0000000 00000000000 15022030435 010510  5                                                                                                    ustar                                                                                                                                                                                                                                                          client/common/logger.py                                                                             0000777 0000000 0000000 00000002363 15000424316 012351  0                                                                                                    ustar                                                                                                                                                                                                                                                          import logging
import os
from logging.handlers import RotatingFileHandler

LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO").upper()
LOG_DIR = os.getenv("LOG_DIR", "./logs")
os.makedirs(LOG_DIR, exist_ok=True)

def get_logger(name: str) -> logging.Logger:
    logger = logging.getLogger(name)
    if logger.hasHandlers():
        return logger

    logger.setLevel(LOG_LEVEL)

    formatter = logging.Formatter("[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s")

    # 控制台输出
    ch = logging.StreamHandler()
    ch.setFormatter(formatter)
    logger.addHandler(ch)

    # 文件输出（按模块名称区分）
    fh = RotatingFileHandler(f"{LOG_DIR}/{name}.log", maxBytes=10 * 1024 * 1024, backupCount=3)
    fh.setFormatter(formatter)
    logger.addHandler(fh)

    return logger
# common/emoji_log.py
def emoji(level: str, message: str) -> str:
    tags = {
        "DEBUG": "🐞",
        "INFO": "ℹ️",
        "SUCCESS": "✅",
        "WARNING": "⚠️",
        "ERROR": "❌",
        "CRITICAL": "🔥",
        "TASK": "📌",
        "GETTASK":"📥",
        "STARTUP": "🚀",
        "SHUTDOWN": "🛑",
        "NETWORK": "🌐",
        "DB": "🗃️",
        "WAIT":"⏳",
    }
    return f"{tags.get(level.upper(), '')} {message}"                                                                                                                                                                                                                                                                             client/config.yaml                                                                                  0000777 0000000 0000000 00000000621 15021561502 011356  0                                                                                                    ustar                                                                                                                                                                                                                                                          # 并发数设置（自动根据系统资源计算）
concurrency: null

# Camoufox 参数配置
camoufox:
  # 支持的打码类型
  solver_type:
    - HcaptchaCracker
    - AntiTurnstileTaskProxyLess
  # 无头模式
  headless: "true"

worker:
  # 客户端名称（每台机器要不同）
  name: "client-20"
  # 主服务器地址
  wss_url: "ws://212.28.185.45:8080/ws/worker/"                                                                                                               client/core/                                                                                        0000777 0000000 0000000 00000000000 15022030435 010150  5                                                                                                    ustar                                                                                                                                                                                                                                                          client/core/system_resources.py                                                                     0000777 0000000 0000000 00000000772 15000424316 014152  0                                                                                                    ustar                                                                                                                                                                                                                                                          import os
import psutil

def auto_concurrency():
    logical_cpu = psutil.cpu_count(logical=True)
    physical_cpu = psutil.cpu_count(logical=False) or 1
    mem_gb = psutil.virtual_memory().available / (1024 ** 3)

    # 每 1 GB 支持一个并发
    mem_based = int(mem_gb)

    # 限制最大并发：不超过物理核心数的 2 倍
    max_concurrency = physical_cpu * 2

    # min(逻辑核, 内存估算, 最大限制)，最少为 1
    return max(1, min(logical_cpu, mem_based, max_concurrency))      client/core/ws_client.py                                                                            0000777 0000000 0000000 00000013673 15021547036 012537  0                                                                                                    ustar                                                                                                                                                                                                                                                          import asyncio
import os
import ssl
import sys
import websockets
import json
import yaml
import importlib
import traceback
from framework.solver_core import get_solver_config
from core.system_resources import auto_concurrency
from common.logger import get_logger, emoji

logger = get_logger("ws_client")

with open("config/config.yaml", "r") as f:
    config = yaml.safe_load(f)

MAX_CONCURRENCY = config.get("concurrency") or auto_concurrency()
task_queue = asyncio.Queue(maxsize=MAX_CONCURRENCY * 2)
semaphore = asyncio.Semaphore(MAX_CONCURRENCY)

logger.info(emoji("TASK", f"最大允许线程数:{MAX_CONCURRENCY}"))

def safe_import_handler(module_name: str, filename: str):
    try:
        return importlib.import_module(f"task_handlers.{module_name}")
    except Exception:
        pass

    try:
        path = os.path.join("task_handlers", filename)
        if not os.path.exists(path):
            logger.error(f"❌ 文件不存在: {path}")
            return None
        spec = importlib.util.spec_from_file_location(module_name, path)
        if not spec:
            logger.error("❌ 创建模块 spec 失败")
            return None
        module = importlib.util.module_from_spec(spec)
        sys.modules[module_name] = module
        spec.loader.exec_module(module)
        return module
    except Exception as e:
        logger.error(f"❌ 模块加载失败: {e}")
        logger.debug(traceback.format_exc())
        return None

async def run_task(task, proxy):
    module_name = task["type"]
    filename = f"{module_name}.py"
    handler = safe_import_handler(module_name, filename)
    if not handler:
        raise RuntimeError(f"无法加载 handler: {module_name}")

    try:
        if asyncio.iscoroutinefunction(handler.run):
            result = await handler.run(task, proxy)
            while asyncio.iscoroutine(result):
                result = await result
            return result
        else:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, handler.run, task)
    finally:
        # 自动清理钩子
        if hasattr(handler, "cleanup") and asyncio.iscoroutinefunction(handler.cleanup):
            try:
                await handler.cleanup()
            except Exception as e:
                logger.warning(f"⚠️ cleanup 执行失败: {e}")

async def task_worker(ws):
    while True:
        task, proxy = await task_queue.get()
        async with semaphore:
            try:
                result = await run_task(task, proxy)
                await ws.send(json.dumps({
                    "type": "task_result",
                    "taskId": task["taskId"],
                    "errorId": 0,
                    "result": result
                }))
            except Exception as e:
                logger.error(f"❌ 任务执行异常: {e}")
                logger.debug(traceback.format_exc())
                await ws.send(json.dumps({
                    "type": "task_result",
                    "taskId": task.get("taskId"),
                    "errorId": -1,
                    "result": {"error": str(e)}
                }))
        task_queue.task_done()

async def heartbeat(ws):
    while True:
        running_tasks = MAX_CONCURRENCY - semaphore._value
        waiting_tasks = task_queue.qsize()
        await ws.send(json.dumps({
            "type": "status_update",
            "current_tasks": running_tasks + waiting_tasks,
            "pending_tasks": running_tasks
        }))
        await asyncio.sleep(10)

async def receiver(ws):
    while True:
        msg = await ws.recv()
        data = json.loads(msg)
        task = data.get("task")
        proxy = data.get("proxy")
        logger.info(emoji("GETTASK", f"接收到任务: {task['type']} - {task['taskId']}"))
        await task_queue.put((task, proxy))

async def worker_main():
    uri = config.get("worker").get("wss_url") + config.get("worker").get("name")

    while True:
        tasks = []

        # 根据URL协议决定是否使用SSL
        if uri.startswith("wss://"):
            ssl_ctx = ssl._create_unverified_context()
        else:
            ssl_ctx = None

        try:
            # 根据是否有SSL上下文决定连接方式
            if ssl_ctx:
                async with websockets.connect(uri, ssl=ssl_ctx) as ws:
                    await ws.send(json.dumps({
                        "type": "register",
                        "task_types": get_solver_config().get("solver_type"),
                        "max_concurrency": MAX_CONCURRENCY
                    }))
                    logger.info(emoji("SUCCESS", f"已注册: {uri}"))

                    tasks.append(asyncio.create_task(heartbeat(ws)))
                    tasks.append(asyncio.create_task(receiver(ws)))
                    for _ in range(MAX_CONCURRENCY):
                        tasks.append(asyncio.create_task(task_worker(ws)))

                    await asyncio.gather(*tasks)
            else:
                async with websockets.connect(uri) as ws:
                    await ws.send(json.dumps({
                        "type": "register",
                        "task_types": get_solver_config().get("solver_type"),
                        "max_concurrency": MAX_CONCURRENCY
                    }))
                    logger.info(emoji("SUCCESS", f"已注册: {uri}"))

                    tasks.append(asyncio.create_task(heartbeat(ws)))
                    tasks.append(asyncio.create_task(receiver(ws)))
                    for _ in range(MAX_CONCURRENCY):
                        tasks.append(asyncio.create_task(task_worker(ws)))

                    await asyncio.gather(*tasks)

        except Exception as e:
            logger.warning(emoji("ERROR", f"连接断开: {e}"))
            logger.debug(traceback.format_exc())
        finally:
            for task in tasks:
                task.cancel()
            await asyncio.gather(*tasks, return_exceptions=True)
            await asyncio.sleep(5)

if __name__ == "__main__":
    asyncio.run(worker_main())                                                                     client/docker-compose.yml                                                                           0000777 0000000 0000000 00000000340 15000424316 012656  0                                                                                                    ustar                                                                                                                                                                                                                                                          services:
  client:
    build:
      context: .
      dockerfile: Dockerfile
    image: capsolver-client:dev
    container_name: capsolver-client
    volumes:
      - ./config.yaml:/app/config/config.yaml
    restart: always                                                                                                                                                                                                                                                                                                client/Dockerfile                                                                                   0000777 0000000 0000000 00000000754 15000424316 011224  0                                                                                                    ustar                                                                                                                                                                                                                                                          FROM mcr.microsoft.com/playwright/python:v1.43.0-jammy

WORKDIR /app
COPY . .

# 安装 Python 依赖和 Camoufox
RUN pip install --no-cache-dir -r requirements.txt \
    && pip install --no-cache-dir camoufox \
    && pip install --no-cache-dir 'camoufox[GeoIP]'

# 触发 GeoIP 下载（模拟一次带 geoip=True 的 Camoufox 启动）
RUN python -c "from camoufox import Camoufox; Camoufox(geoip=True, headless=True).start().close()"
# 启动任务
ENTRYPOINT ["python", "run_client.py"]
                    client/framework/                                                                                   0000777 0000000 0000000 00000000000 15022030435 011215  5                                                                                                    ustar                                                                                                                                                                                                                                                          client/framework/solver_core.py                                                                     0000777 0000000 0000000 00000000655 15000424316 014123  0                                                                                                    ustar                                                                                                                                                                                                                                                          import yaml

with open("config/config.yaml", "r") as f:
    config = yaml.safe_load(f)

def get_proxy_url():
    proxy_cfg = config.get("proxy")
    if proxy_cfg:
        proxy = {
            "server": proxy_cfg["server"],
            "username": proxy_cfg["username"],
            "password": proxy_cfg["password"],
        }
        return proxy
    return None

def get_solver_config():
    return config.get("camoufox", {})
                                                                                   client/requirements.txt                                                                             0000777 0000000 0000000 00000003035 15000424316 012511  0                                                                                                    ustar                                                                                                                                                                                                                                                          aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.16
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.9.0
asyncio==3.4.3
attrs==25.3.0
blinker==1.9.0
browserforge==1.2.3
cachetools==5.5.2
camoufox==0.4.11
certifi==2025.1.31
charset-normalizer==3.4.1
click==8.1.8
contourpy==1.3.2
cycler==0.12.1
Cython==3.0.12
Flask==3.1.0
fonttools==4.57.0
frozenlist==1.5.0
geoip2==5.0.1
google-auth==2.39.0
google-genai==1.10.0
greenlet==3.1.1
h11==0.14.0
h2==4.2.0
hcaptcha-challenger==0.15.3
hpack==4.1.0
httpcore==1.0.8
httpx==0.28.1
Hypercorn==0.17.3
hyperframe==6.1.0
idna==3.10
itsdangerous==2.2.0
Jinja2==3.1.6
kiwisolver==1.4.8
language-tags==1.2.0
loguru==0.7.3
lxml==5.3.2
markdown-it-py==3.0.0
MarkupSafe==3.0.2
matplotlib==3.10.1
maxminddb==2.6.3
mdurl==0.1.2
msgpack==1.1.0
multidict==6.2.0
numpy==2.2.4
opencv-python==4.11.0.86
orjson==3.10.16
packaging==24.2
patchright==1.51.3
pillow==11.2.1
platformdirs==4.3.7
playwright==1.51.0
priority==2.0.0
propcache==0.3.1
psutil==7.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pydantic==2.11.3
pydantic-settings==2.8.1
pydantic_core==2.33.1
pyee==12.1.1
Pygments==2.19.1
pyparsing==3.2.3
PySocks==1.7.1
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
pytz==2025.2
PyYAML==6.0.2
Quart==0.20.0
requests==2.32.3
rich==14.0.0
rsa==4.9.1
screeninfo==0.8.1
shellingham==1.5.4
six==1.17.0
sniffio==1.3.1
tenacity==9.1.2
tqdm==4.67.1
typer==0.15.2
typing-inspection==0.4.0
typing_extensions==4.13.1
ua-parser==1.0.1
ua-parser-builtins==0.18.0.post1
urllib3==2.3.0
websockets==15.0.1
Werkzeug==3.1.3
wsproto==1.2.0
yarl==1.19.0
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   client/run_client.py                                                                                0000777 0000000 0000000 00000000161 15000424316 011736  0                                                                                                    ustar                                                                                                                                                                                                                                                          import asyncio
from core.ws_client import worker_main

if __name__ == "__main__":
    asyncio.run(worker_main())
                                                                                                                                                                                                                                                                                                                                                                                                               client/task_handlers/                                                                               0000777 0000000 0000000 00000000000 15022030435 012042  5                                                                                                    ustar                                                                                                                                                                                                                                                          client/task_handlers/AntiTurnstileTaskProxyLess.py                                                  0000777 0000000 0000000 00000016157 15022031126 017750  0                                                                                                    ustar                                                                                                                                                                                                                                                          import asyncio
import gc
import json
import logging
import os
import resource
import sys
import time
from typing import Optional

import yaml
from camoufox.async_api import AsyncCamoufox
# from patchright.async_api import async_playwright
from common.logger import get_logger,emoji
from dataclasses import dataclass
logger = get_logger("Anti")
with open("config/config.yaml", "r") as f:
    config = yaml.safe_load(f)
@dataclass
class TurnstileResult:
    turnstile_value: Optional[str]
    elapsed_time_seconds: float
    status: str
    reason: Optional[str] = None

class TurnstileSolver:
    HTML_TEMPLATE = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Turnstile Solver</title>
        <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async></script>
        <script>
            async function fetchIP() {
                try {
                    const response = await fetch('https://api64.ipify.org?format=json');
                    const data = await response.json();
                    document.getElementById('ip-display').innerText = `Your IP: ${data.ip}`;
                } catch (error) {
                    console.error('Error fetching IP:', error);
                    document.getElementById('ip-display').innerText = 'Failed to fetch IP';
                }
            }
            window.onload = fetchIP;
        </script>
    </head>
    <body>
        <!-- cf turnstile -->
        <p id="ip-display">Fetching your IP...</p>
    </body>
    </html>
    """

    def __init__(self, debug: bool = False, headless: Optional[bool] = False, useragent: Optional[str] = None, browser_type: str = "chromium"):
        self.debug = debug
        self.browser_type = browser_type
        self.headless = headless
        self.useragent = useragent
        self.browser_args = []
        if useragent:
            self.browser_args.append(f"--user-agent={useragent}")

    async def _setup_page(self, browser, url: str, sitekey: str, action: str = None, cdata: str = None):
        if self.browser_type == "chrome":
            page = browser.pages[0]
        else:
            page = await browser.new_page()

        url_with_slash = url + "/" if not url.endswith("/") else url

        if self.debug:
            logger.debug(f"Navigating to URL: {url_with_slash}")

        turnstile_div = f'<div class="cf-turnstile" style="background: white; width: 70px;" data-sitekey="{sitekey}"' + (f' data-action="{action}"' if action else '') + (f' data-cdata="{cdata}"' if cdata else '') + '></div>'
        page_data = self.HTML_TEMPLATE.replace("<!-- cf turnstile -->", turnstile_div)

        await page.route(url_with_slash, lambda route: route.fulfill(body=page_data, status=200))
        await page.goto(url_with_slash)

        return page, url_with_slash

    async def _get_turnstile_response(self, page, max_attempts: int = 10) -> Optional[str]:
        for attempt in range(max_attempts):
            if self.debug:
                logger.debug(f"Attempt {attempt + 1}: No Turnstile response yet.")

            try:
                turnstile_check = await page.input_value("[name=cf-turnstile-response]")
                if turnstile_check == "":
                    await page.click("//div[@class='cf-turnstile']", timeout=3000)
                    await asyncio.sleep(3)
                else:
                    return turnstile_check
            except Exception as e:
                logger.debug(f"Click error: {str(e)}")
                continue
        return None

    async def solve(self, proxy:json,url: str, sitekey: str, action: str = None, cdata: str = None):
        start_time = time.time()
        elapsed = 0  # 初始化elapsed变量，避免在异常处理中引用未定义的变量
        logger.debug(f"Attempting to solve URL: {url}")
        proxy_config = config.get("proxy") or {}
        # proxy = {
        #     "server": proxy_config.get("server"),
        #     "username": proxy_config.get("username"),
        #     "password": proxy_config.get("password"),
        # }
        logger.debug(f"Proxy: {proxy},type:{type(proxy)}")
        async with AsyncCamoufox(
                headless=self.headless,
                geoip=True,
                proxy=proxy,
        ) as browser:
            try:
                page,url_with_slash = await self._setup_page(browser, url, sitekey, action, cdata)
                token = await self._get_turnstile_response(page)
                elapsed = round(time.time() - start_time, 2)

                if not token:
                    logger.error("Failed to retrieve Turnstile value.")
                    return TurnstileResult(None, elapsed, "failure", "No token obtained")

                logger.info(emoji("SUCCESS", f"Solved Turnstile in {elapsed}s -> {token[:10]}..."))
                return TurnstileResult(token, elapsed, "success")
            except Exception as e:
                # 确保elapsed变量已定义
                elapsed = round(time.time() - start_time, 2)
                logger.error(emoji("ERROR", f"Failed to solve Turnstile: {str(e)}"))
                return TurnstileResult(None, elapsed, "failure", str(e))
            finally:
                await browser.close()
                # 强制垃圾回收
                gc.collect()
                # 打印内存使用情况
                rss_kb = resource.getrusage(resource.RUSAGE_SELF).ru_maxrss
                rss_mb = rss_kb / 1024 if sys.platform != "darwin" else rss_kb / (1024 * 1024)  # macOS单位不同
                logger.debug(f"🧠 内存占用: {rss_mb:.2f} MB")
                logger.debug(f"对象数量追踪: {len(gc.get_objects())}")
                try:
                    open_fds = len(os.listdir(f'/proc/{os.getpid()}/fd'))
                    logger.debug(f"📎 打开文件描述符数: {open_fds}")
                except Exception:
                    pass

async def get_turnstile_token(proxy:json,url: str, sitekey: str, action: str = None, cdata: str = None, debug: bool = False, headless: bool = False, useragent: str = None):
    solver = TurnstileSolver(debug=debug, useragent=useragent, headless=headless)
    logger.debug(f"solver: {solver}")
    result = await solver.solve(proxy=proxy,url=url, sitekey=sitekey, action=action, cdata=cdata)
    return result.__dict__

async def run(task_data,proxy):
    logger.debug(f"task_data: {task_data}")
    url = task_data["websiteURL"]
    sitekey = task_data["websiteKey"]
    action = task_data.get("metadata", {}).get("action")
    logger.debug(f"action: {sitekey}")
    headless_str = config.get("camoufox").get("headless", "true")
    headless = headless_str.lower() == "true"
    logger.debug(f"headless: {headless}")
    res = await get_turnstile_token(
        proxy=proxy,
        url=url,
        sitekey=sitekey,
        action=None,
        cdata=None,
        debug=False,
        headless=headless,
        useragent=None,
    )
    return {
        "token": res["turnstile_value"],
        "elapsed": res["elapsed_time_seconds"],
        "status": "success" if res["turnstile_value"] else "failure",
        "type": "turnstile"
    }                                                                                                                                                                                                                                                                                                                                                                                                                 client/task_handlers/HcaptchaCracker.py                                                             0000777 0000000 0000000 00000010616 15000424316 015432  0                                                                                                    ustar                                                                                                                                                                                                                                                          import asyncio
import gc
import json
import os
import resource
import sys
import time

import yaml
from camoufox.async_api import AsyncCamoufox
from hcaptcha_challenger.agent import AgentV, AgentConfig
from hcaptcha_challenger.models import CaptchaResponse, ChallengeSignal
from hcaptcha_challenger.utils import SiteKey
from common.logger import get_logger,emoji

logger = get_logger("HCaptcha")

with open("config/config.yaml", "r") as f:
    config = yaml.safe_load(f)
# gemini_key = config.get("apikey").get("gemini_api_key")
# models = config.get("models")
headless_str = config.get("camoufox").get("headless", "true")
headless = headless_str.lower() == "true"
# if gemini_key:
#     os.environ["GEMINI_API_KEY"] = gemini_key
# else:
#     raise RuntimeError("config.yaml 缺少 gemini_api_key")

async def run(task_data, proxy):
    url = task_data["websiteURL"]
    sitekey = task_data["websiteKey"]
    print(task_data)
    gemini_key = task_data["clientKey"]
    action = task_data.get("metadata", {}).get("action", "")
    cdata = task_data.get("metadata", {}).get("cdata", "")

    logger.debug(f"🌐 Preparing hCaptcha page at {url}")
    start_time = time.time()
    async with AsyncCamoufox(
        headless=headless,
        proxy=proxy,
        geoip=True,
        args=["--lang=en-US", "--accept-language=en-US,en;q=0.9"]
    ) as browser:
        try:
            page = await browser.new_page()
            await page.goto(SiteKey.as_site_link(sitekey))

            # 初始化 Agent
            agent_config = AgentConfig(
                GEMINI_API_KEY=gemini_key,
                EXECUTION_TIMEOUT = 300,
                RESPONSE_TIMEOUT = 30,
                RETRY_ON_FAILURE = True,
                # CHALLENGE_CLASSIFIER_MODEL=models['CHALLENGE_CLASSIFIER_MODEL'],
                # IMAGE_CLASSIFIER_MODEL=models['IMAGE_CLASSIFIER_MODEL'],
                # SPATIAL_POINT_REASONER_MODEL=models['SPATIAL_POINT_REASONER_MODEL'],
                # SPATIAL_PATH_REASONER_MODEL=models['SPATIAL_PATH_REASONER_MODEL'],
            )
            agent = AgentV(page=page, agent_config=agent_config)

            await agent.robotic_arm.click_checkbox()

            # 执行挑战并等待结果
            await agent.wait_for_challenge()
            elapsed = round(time.time() - start_time, 2)
            if agent.cr_list:
                cr = agent.cr_list[-1]
                cr_data = cr.model_dump()
                logger.debug(cr_data)
                token = cr_data["generated_pass_UUID"] if cr_data.get("is_pass") else None
                logger.info(emoji("SUCCESS", f"Solved Hcaptcha in {elapsed}s -> {token[:10]}..."))
                return {
                    "token": token,
                    "elapsed": cr_data.get("expiration", 0),
                    "status": "success" if cr_data.get("is_pass") else "failure",
                    "type": "hcaptcha"
                }
            else:
                return {
                    "token": None,
                    "elapsed": 0,
                    "status": "failure",
                    "type": "hcaptcha"
                }

        except Exception as e:
            logger.error(emoji("ERROR", f"Failed to solve Hcaptcha: {str(e)}"))
            return {
                "token": None,
                "elapsed": 0,
                "status": "failure",
                "type": "hcaptcha"
            }
        finally:
            await page.close()
            # 强制垃圾回收
            gc.collect()
            # 打印内存使用情况
            rss_kb = resource.getrusage(resource.RUSAGE_SELF).ru_maxrss
            rss_mb = rss_kb / 1024 if sys.platform != "darwin" else rss_kb / (1024 * 1024)  # macOS单位不同
            logger.debug(f"🧠 内存占用: {rss_mb:.2f} MB")
            logger.debug(f"对象数量追踪: {len(gc.get_objects())}")
            try:
                open_fds = len(os.listdir(f'/proc/{os.getpid()}/fd'))
                logger.debug(f"📎 打开文件描述符数: {open_fds}")
            except Exception:
                pass
# if __name__ == "__main__":
#     task_data = {
#         "websiteURL": "https://faucet.n1stake.com/",
#         "websiteKey": "d0ba98cc-0528-41a0-98fe-dc66945e5416"
#     }
#     proxy = {
#         "server": "http://pr-sg.ip2world.com:6001",
#         "username": "capsolver-zone-resi-region-hk",
#         "password": "123456"
#     }
#
#     token = asyncio.run(run(task_data, proxy))
#     print(token)                                                                                                                  client/task_handlers/test.py                                                                        0000777 0000000 0000000 00000004023 15000424316 013376  0                                                                                                    ustar                                                                                                                                                                                                                                                          import asyncio
import json

from playwright.async_api import async_playwright, Page

from hcaptcha_challenger.agent import AgentV, AgentConfig
from hcaptcha_challenger.models import CaptchaResponse
from hcaptcha_challenger.utils import SiteKey


async def challenge(page: Page) -> AgentV:
    """Automates the process of solving an hCaptcha challenge."""
    # Initialize the agent configuration with API key (from parameters or environment)
    agent_config = AgentConfig()

    # Create an agent instance with the page and configuration
    # AgentV appears to be a specialized agent for visual challenges
    agent = AgentV(page=page, agent_config=agent_config)

    # Click the hCaptcha checkbox to initiate the challenge
    # The robotic_arm is an abstraction for performing UI interactions
    await agent.robotic_arm.click_checkbox()

    # Wait for the challenge to appear and be ready for solving
    # This may involve waiting for images to load or instructions to appear
    await agent.wait_for_challenge()

    # Note: The code ends here, suggesting this is part of a larger solution
    # that would continue with challenge solving steps after this point
    return agent


async def main():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context()

        # Create a new page in the provided browser context
        page = await context.new_page()

        # Navigate to the hCaptcha test page using a predefined site key
        # SiteKey.user_easy likely refers to a test/demo hCaptcha with lower difficulty
        # await page.goto(SiteKey.as_site_link(SiteKey.discord))
        await page.goto(SiteKey.as_site_link(SiteKey.user_easy))

        # --- When you encounter hCaptcha in your workflow ---
        agent = await challenge(page)
        if agent.cr_list:
            cr: CaptchaResponse = agent.cr_list[-1]
            print(json.dumps(cr.model_dump(by_alias=True), indent=2, ensure_ascii=False))


if __name__ == "__main__":
    asyncio.run(main())
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             client/test.py                                                                                      0000777 0000000 0000000 00000001011 15000424316 010546  0                                                                                                    ustar                                                                                                                                                                                                                                                          import asyncio
from task_handlers.HcaptchaCracker import run

async def main():
    proxy = {
        "server": "http://capsolver-zone-resi-region-hk:<EMAIL>:6001"

    }
    task = {
        "websiteURL": "https://accounts.hcaptcha.com/demo",
        "websiteKey": "00000000-0000-0000-0000-000000000000",  # 替换为真实 sitekey
        "metadata": {
            "label": "bus"
        }
    }
    result = await run(task, proxy)
    print(result)

if __name__ == "__main__":
    asyncio.run(main())                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       