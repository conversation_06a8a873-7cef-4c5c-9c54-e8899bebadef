/**
 * 自动认证服务
 * 负责在token无效时自动重新获取token
 */
const fs = require('fs');
const path = require('path');
const { ethers } = require('ethers');
const axios = require('axios');
const config = require('../../config');
const { log, logToFile } = require('../utils/simple-logger');
const { HttpsProxyAgent } = require('https-proxy-agent');
const { HttpProxyAgent } = require('http-proxy-agent');
const walletProxyManager = require('./walletProxyManager');
// 验证码服务
const turnstileSolver = require('./turnstileSolver');
const twoCaptchaService = require('./twoCaptcha');
const remoteCaptchaService = require('./remoteCaptchaService');
const crypto = require('crypto');

// 选择使用的验证码服务
function getCaptchaService() {
  switch (config.CAPTCHA_SERVICE) {
    case 'local':
      return turnstileSolver;
    case '2captcha':
      return twoCaptchaService;
    case 'remote':
      return remoteCaptchaService;
    default:
      log(`未知的验证码服务类型: ${config.CAPTCHA_SERVICE}，使用本地服务`, 'warning');
      return turnstileSolver;
  }
}

// 最大重试次数
const MAX_RETRIES = 5;
const RETRY_DELAY = 3000;

// 私钥文件路径
const privateKeyPath = path.join(process.cwd(), 'priv.txt');
const tokenPath = path.join(process.cwd(), 'session-token.key');

/**
 * 从私钥文件中读取所有私钥
 * @returns {Array<string>} 私钥列表
 */
function readPrivateKeys() {
  try {
    if (!fs.existsSync(privateKeyPath)) {
      log(`私钥文件不存在: ${privateKeyPath}`, "error");
      return [];
    }
    
    const content = fs.readFileSync(privateKeyPath, 'utf8');
    return content.split('\n')
      .map(line => line.trim())
      .filter(line => line && !line.startsWith('#'));
  } catch (error) {
    log(`读取私钥文件失败: ${error.message}`, "error");
    return [];
  }
}

/**
 * 根据钱包地址查找对应的私钥
 * @param {string} walletAddress 钱包地址
 * @returns {string|null} 私钥或null
 */
function findPrivateKeyByAddress(walletAddress) {
  const privateKeys = readPrivateKeys();
  
  for (const privateKey of privateKeys) {
    try {
      const wallet = new ethers.Wallet(privateKey);
      if (wallet.address.toLowerCase() === walletAddress.toLowerCase()) {
        return privateKey;
      }
    } catch (error) {
      // 忽略无效的私钥
      continue;
    }
  }
  
  return null;
}

/**
 * 生成随机字符串作为nonce
 * @returns {string} 随机nonce
 */
function generateNonce() {
  return Buffer.from(crypto.randomBytes(48)).toString("hex");
}

/**
 * 使用私钥签名消息
 * @param {Object} wallet 钱包对象
 * @returns {Promise<Object>} 签名结果
 */
async function signMessage(wallet) {
  try {
    const nonce = generateNonce();
    const timestamp = new Date().toISOString();
    const message = `klokapp.ai wants you to sign in with your Ethereum account:
${wallet.address}


URI: https://klokapp.ai/
Version: 1
Chain ID: 1
Nonce: ${nonce}
Issued At: ${timestamp}`;
    
    return {
      signature: await wallet.signMessage(message),
      message: message,
      nonce: nonce,
      timestamp: timestamp
    };
  } catch (error) {
    log(`签名消息失败: ${error.message}`, "error");
    throw error;
  }
}

/**
 * 验证会话令牌是否有效
 * @param {string} walletAddress 钱包地址
 * @param {string} sessionToken 会话令牌
 * @returns {Promise<boolean>} 令牌是否有效
 */
async function validateSessionToken(walletAddress, sessionToken) {
  try {
    log(`验证钱包 ${walletAddress.substring(0, 8)}... 的会话令牌`, "info");
    
    // 获取钱包绑定的代理
    const binding = walletProxyManager.getWalletBinding(walletAddress);
    const proxyUrl = binding ? binding.proxyUrl : null;
    
    // 配置请求选项
    const requestOptions = {
      headers: {
        ...config.DEFAULT_HEADERS,
        'Authorization': `Bearer ${sessionToken}`,
        'accept': '*/*',
        'accept-encoding': 'gzip, deflate, br, zstd',
        'accept-language': 'zh,zh-CN;q=0.9',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Origin': 'https://klokapp.ai',
        'Referer': 'https://klokapp.ai/',
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site'
      },
      timeout: 30000
    };
    
    // 如果有绑定代理，使用代理
    if (proxyUrl) {
      if (config.BASE_URL.startsWith("https")) {
        requestOptions.httpsAgent = new HttpsProxyAgent(proxyUrl);
      } else {
        requestOptions.httpAgent = new HttpProxyAgent(proxyUrl);
      }
    }
    
    // 发送请求验证令牌
    // 使用用户信息端点验证
    const response = await axios.get(`${config.BASE_URL}/v1/me`, requestOptions);
    
    // 如果请求成功，则令牌有效
    if (response.status === 200) {
      log(`钱包 ${walletAddress.substring(0, 8)}... 的会话令牌有效`, "success");
      return true;
    }
    
    return false;
  } catch (error) {
    log(`验证会话令牌失败: ${error.message}`, "warning");
    return false;
  }
}

/**
 * 认证钱包并获取token
 * @param {string} walletAddress 钱包地址
 * @returns {Promise<string|null>} 成功返回token，失败返回null
 */
async function authenticate(walletAddress) {
  // 不再检查现有会话令牌，每次都重新认证
  log(`钱包 ${walletAddress.substring(0, 8)}... 开始新的认证流程`, "info");
  
  // 如果有现有token，记录一下但不使用
  const existingToken = walletProxyManager.getWalletToken(walletAddress);
  if (existingToken) {
    log(`发现钱包 ${walletAddress.substring(0, 8)}... 有现有会话令牌，但将重新获取新token`, "info");
  }
  
  // 查找私钥
  const privateKey = findPrivateKeyByAddress(walletAddress);
  if (!privateKey) {
    log(`未找到钱包 ${walletAddress.substring(0, 8)}... 对应的私钥`, "error");
    return null;
  }
  
  // 创建钱包对象
  const wallet = new ethers.Wallet(privateKey);
  
  let attempt = 0;
  while (attempt < MAX_RETRIES) {
    try {
      log(`钱包 ${walletAddress.substring(0, 8)}... 认证尝试 ${attempt + 1}/${MAX_RETRIES}`, "info");
      
      // 签名消息
      const signResult = await signMessage(wallet);
      log(`钱包 ${walletAddress.substring(0, 8)}... 签名成功`, "success");
      
      // 获取Cloudflare Turnstile token
      log(`获取Cloudflare Turnstile token...`, "info");
      
      // 根据配置使用相应的验证码服务
      const captchaService = getCaptchaService();
      const serviceType = {
        'local': '本地Turnstile-Solver',
        '2captcha': '2Captcha',
        'remote': '远程打码服务'
      }[config.CAPTCHA_SERVICE] || '未知服务';

      log(`[自动认证] 使用${serviceType}服务...`, "info");

      const turnstileToken = await captchaService.getTurnstileToken();
      if (!turnstileToken) {
        throw new Error("获取Cloudflare Turnstile token失败");
      }
      log(`获取Cloudflare Turnstile token成功，长度: ${turnstileToken.length}`, "success");
      log(`Token预览: ${turnstileToken.substring(0, 50)}...`, "info");
      
      // 构建请求体 - 移除验证码token
      const payload = {
        signedMessage: signResult.signature,
        message: signResult.message,
        referral_code: config.REFERRAL_CODE?.referral_code || ""
      };
      
      // 输出完整的payload以便调试
      log(`认证请求payload: ${JSON.stringify(payload)}`, "info");
      
      // 获取钱包绑定的代理
      const binding = walletProxyManager.getWalletBinding(walletAddress);
      const proxyUrl = binding ? binding.proxyUrl : null;
      
      // 配置请求选项
      const requestOptions = {
        headers: {
          ...config.DEFAULT_HEADERS,
          "x-turnstile-token": turnstileToken, // 将Turnstile token放在请求头中
          "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
          "Origin": "https://klokapp.ai",
          "Referer": "https://klokapp.ai/",
          "sec-ch-ua": '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": "Windows",
          "sec-fetch-dest": "empty",
          "sec-fetch-mode": "cors",
          "sec-fetch-site": "cross-site",
          "Accept-Language": "zh-CN,zh;q=0.9"
        },
        timeout: 120000, // 增加超时时间到120秒
        maxContentLength: Infinity, // 允许任意大小的响应
        maxBodyLength: Infinity, // 允许任意大小的请求体
        proxy: false, // 禁用axios默认代理设置，使用我们自己的代理配置
      };
      
      // 是否使用代理
      const USE_PROXY = true; // 设置为true以启用代理使用
      
      // 如果有绑定代理，且需要使用代理
      if (USE_PROXY && proxyUrl) {
        log(`使用钱包 ${walletAddress.substring(0, 8)}... 绑定的代理: ${proxyUrl}`, "info");
        if (config.BASE_URL.startsWith("https")) {
          requestOptions.httpsAgent = new HttpsProxyAgent(proxyUrl);
        } else {
          requestOptions.httpAgent = new HttpProxyAgent(proxyUrl);
        }
      } else {
        log(`不使用代理进行认证请求`, "info");
      }
      
      // 发送认证请求
      log(`发送认证请求...`, "info");
      const response = await axios.post(`${config.BASE_URL}/v1/verify`, payload, requestOptions);
      
      // 获取session token
      const { session_token } = response.data;
      if (!session_token) {
        throw new Error("响应中未包含session_token");
      }
      
      log(`钱包 ${walletAddress.substring(0, 8)}... 认证成功，获取到token`, "success");
      
      // 不再更新钱包-token绑定或保存token到文件
      log(`成功获取新token，但不再保存到绑定关系或文件`, "info");
      
      return session_token;
    } catch (error) {
      attempt++;
      log(`钱包 ${walletAddress.substring(0, 8)}... 认证失败: ${error.message}`, "error");
      
      if (error.response) {
        // 使用安全的方式记录响应数据
        try {
          const responseData = typeof error.response.data === 'object' ? 
            JSON.stringify(error.response.data).substring(0, 200) : 
            String(error.response.data).substring(0, 200);
          log(`状态码: ${error.response.status}, 数据: ${responseData}`, "error");
        } catch (logError) {
          log(`状态码: ${error.response.status}, 无法记录响应数据`, "error");
        }
        
        // 重试所有的HTTP错误，但根据不同错误类型调整重试策略
        if (error.response.status === 400 || error.response.status === 401) {
          // 400/401错误可能是请求格式问题，会重试
          if (attempt < MAX_RETRIES) {
            log(`认证请求返回${error.response.status}错误，将重试(尝试${attempt+1}/${MAX_RETRIES})...`, "warning");
            // 增加额外的等待时间
            await new Promise(resolve => setTimeout(resolve, RETRY_DELAY * 2));
            continue;
          }
        }
        
        // 特殊处理服务器500错误
        if (error.response.status === 500) {
          // 引入调度器
          const scheduler = require('./scheduler');
          
          // 设置1小时后再尝试
          const delayMs = 60 * 60 * 1000; // 1小时
          const nextTime = new Date(new Date().getTime() + delayMs);
          
          // 更新钱包状态
          scheduler.updateWalletState(walletAddress, {
            nextExecutionTime: nextTime.toISOString(),
            lastError: `服务器错误(500): ${error.message}`,
            lastErrorTime: new Date().toISOString()
          });
          
          // 保存钱包状态到文件
          scheduler.saveState();
          
          log(`钱包 ${walletAddress.substring(0, 8)}... 遇到服务器错误，已设置1小时后重试`, "warning");
          
          // 服务器500错误直接返回null，不再重试
          return null;
        }
      }
      
      if (attempt < MAX_RETRIES) {
        // 等待一段时间后重试
        const waitTime = RETRY_DELAY * attempt;
        log(`等待 ${waitTime/1000} 秒后重试...`, "info");
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }
  
  log(`钱包 ${walletAddress.substring(0, 8)}... 认证失败，已达到最大重试次数`, "error");
  
  // 认证失败后强制更新钱包状态
  try {
    const scheduler = require('./scheduler');
    
    // 设置30分钟后再尝试
    const delayMs = 30 * 60 * 1000; // 30分钟
    const nextTime = new Date(new Date().getTime() + delayMs);
    
    // 更新钱包状态
    scheduler.updateWalletState(walletAddress, {
      nextExecutionTime: nextTime.toISOString(),
      lastError: "认证失败，已达到最大重试次数",
      lastErrorTime: new Date().toISOString()
    });
    
    // 保存钱包状态到文件
    scheduler.saveState();
    
    log(`钱包 ${walletAddress.substring(0, 8)}... 已设置30分钟后重试`, "warning");
  } catch (error) {
    log(`更新钱包状态失败: ${error.message}`, "error");
  }
  
  return null;
}

/**
 * 验证token是否有效
 * @param {string} token 要验证的token
 * @param {string} walletAddress 钱包地址
 * @returns {Promise<boolean>} 是否有效
 */
async function verifyToken(token, walletAddress) {
  if (!token) {
    log("无法验证空的token", "error");
    return false;
  }

  try {
    log("验证token有效性...", "info");
    
    // 按照原始代码设置请求头
    const headers = {
      ...config.DEFAULT_HEADERS,
      "X-Session-Token": token
    };
    
    // 获取代理
    const binding = walletProxyManager.getWalletBinding(walletAddress);
    const proxyUrl = binding ? binding.proxyUrl : null;
    
    // 配置请求选项
    const requestOptions = {
      headers,
      timeout: config.REQUEST_TIMEOUT || 30000
    };
    
    if (proxyUrl) {
      log(`验证token使用代理: ${proxyUrl}`, "info");
      if (config.BASE_URL.startsWith("https")) {
        requestOptions.httpsAgent = new HttpsProxyAgent(proxyUrl);
      } else {
        requestOptions.httpAgent = new HttpProxyAgent(proxyUrl);
      }
    }
    
    // 只验证/me端点，与原始代码一致
    let isValid = false;
    
    try {
      // 发送请求
      const response = await axios.get(`${config.BASE_URL}/me`, requestOptions);
      // 只检查状态码是否为200
      isValid = response.status === 200;
      log(`Token验证${isValid ? '成功' : '失败'}`, isValid ? "success" : "warning");
    } catch (error) {
      log(`Token验证失败: ${error.message}`, "warning");
      isValid = false;
    }
    
    // 如果token有效，不再更新绑定关系或保存token
    // 仅返回验证结果
    if (isValid) {
      log(`Token验证成功，可以执行任务`, "success");
    }
    
    return isValid;
  } catch (error) {
    log(`验证token失败: ${error.message}`, "error");
    return false;
  }
}

/**
 * 获取有效的token
 * 如果现有token有效则返回，否则尝试重新认证
 * @param {string} walletAddress 钱包地址
 * @returns {Promise<string|null>} token或null
 */
async function getValidToken(walletAddress) {
  // 从绑定关系中获取token
  const binding = walletProxyManager.getWalletBinding(walletAddress);
  const existingToken = binding ? binding.sessionToken : null;
  
  // 不再验证现有token，直接重新认证
  if (existingToken) {
    log(`钱包 ${walletAddress.substring(0, 8)}... 有现有token，但将重新认证获取新token`, "info");
  } else {
    log(`钱包 ${walletAddress.substring(0, 8)}... 没有token，尝试认证`, "info");
  }
  
  // 直接调用认证函数获取新token
  return await authenticate(walletAddress);
}

module.exports = {
  authenticate,
  verifyToken,
  getValidToken,
  findPrivateKeyByAddress
};
