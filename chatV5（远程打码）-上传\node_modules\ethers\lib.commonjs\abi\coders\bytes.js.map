{"version": 3, "file": "bytes.js", "sourceRoot": "", "sources": ["../../../src.ts/abi/coders/bytes.ts"], "names": [], "mappings": ";;;AAAA,mDAA6D;AAE7D,2DAA4C;AAK5C;;GAEG;AACH,MAAa,iBAAkB,SAAQ,yBAAK;IACxC,YAAY,IAAY,EAAE,SAAiB;QACxC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IACtC,CAAC;IAED,YAAY;QACR,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,MAAc,EAAE,KAAU;QAC7B,KAAK,GAAG,IAAA,uBAAY,EAAC,KAAK,CAAC,CAAC;QAC5B,IAAI,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC7C,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACnC,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,MAAM,CAAC,MAAc;QACjB,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;CACJ;AAnBD,8CAmBC;AAED;;GAEG;AACH,MAAa,UAAW,SAAQ,iBAAiB;IAC7C,YAAY,SAAiB;QACzB,KAAK,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IAC9B,CAAC;IAED,MAAM,CAAC,MAAc;QACjB,OAAO,IAAA,kBAAO,EAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;IACzC,CAAC;CACJ;AARD,gCAQC"}