{"name": "ansicolors", "version": "0.3.2", "description": "Functions that surround a string with ansicolor codes so it prints in color.", "main": "ansicolors.js", "scripts": {"test": "node test/*.js"}, "repository": {"type": "git", "url": "git://github.com/thlorenz/ansicolors.git"}, "keywords": ["ansi", "colors", "highlight", "string"], "author": "<PERSON><PERSON> <<EMAIL>> (thlorenz.com)", "license": "MIT", "readmeFilename": "README.md", "gitHead": "858847ca28e8b360d9b70eee0592700fa2ab087d"}