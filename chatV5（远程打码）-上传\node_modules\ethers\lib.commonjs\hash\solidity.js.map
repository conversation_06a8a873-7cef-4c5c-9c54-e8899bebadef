{"version": 3, "file": "solidity.js", "sourceRoot": "", "sources": ["../../src.ts/hash/solidity.ts"], "names": [], "mappings": ";;;AAAA,kDAAiD;AACjD,iDAE4B;AAC5B,gDAG2B;AAG3B,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,iBAAiB,CAAC,CAAC;AACjD,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,mBAAmB,CAAC,CAAC;AACpD,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAGtD,SAAS,KAAK,CAAC,IAAY,EAAE,KAAU,EAAE,OAAiB;IACtD,QAAO,IAAI,EAAE;QACT,KAAK,SAAS;YACV,IAAI,OAAO,EAAE;gBAAE,OAAO,IAAA,mBAAQ,EAAC,IAAA,uBAAY,EAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;aAAE;YAC1D,OAAO,IAAA,mBAAQ,EAAC,IAAA,qBAAU,EAAC,KAAK,CAAC,CAAC,CAAC;QACvC,KAAK,QAAQ;YACT,OAAO,IAAA,sBAAW,EAAC,KAAK,CAAC,CAAC;QAC9B,KAAK,OAAO;YACR,OAAO,IAAA,mBAAQ,EAAC,KAAK,CAAC,CAAC;QAC3B,KAAK,MAAM;YACP,KAAK,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAA,CAAC,CAAC,MAAM,CAAC,CAAC;YACnC,IAAI,OAAO,EAAE;gBAAE,OAAO,IAAA,mBAAQ,EAAC,IAAA,uBAAY,EAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;aAAE;YAC1D,OAAO,IAAA,mBAAQ,EAAC,KAAK,CAAC,CAAC;KAC9B;IAED,IAAI,KAAK,GAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IACrC,IAAI,KAAK,EAAE;QACP,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC;QAClC,IAAI,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAA;QAEtC,IAAA,yBAAc,EAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,GAAG,EAAE,qBAAqB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAE/I,IAAI,OAAO,EAAE;YAAE,IAAI,GAAG,GAAG,CAAC;SAAE;QAE5B,IAAI,MAAM,EAAE;YAAE,KAAK,GAAG,IAAA,iBAAM,EAAC,KAAK,EAAE,IAAI,CAAC,CAAC;SAAE;QAE5C,OAAO,IAAA,mBAAQ,EAAC,IAAA,uBAAY,EAAC,IAAA,oBAAS,EAAC,KAAK,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;KAC7D;IAED,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAC/B,IAAI,KAAK,EAAE;QACP,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAA,yBAAc,EAAC,MAAM,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE,EAAE,oBAAoB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAC1G,IAAA,yBAAc,EAAC,IAAA,qBAAU,EAAC,KAAK,CAAC,KAAK,IAAI,EAAE,qBAAsB,IAAK,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QAE1F,IAAI,OAAO,EAAE;YAAE,OAAO,IAAA,mBAAQ,EAAC,IAAA,uBAAY,EAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;SAAE;QAC1D,OAAO,KAAK,CAAC;KAChB;IAED,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAC/B,IAAI,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QAC/B,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QACzD,IAAA,yBAAc,EAAC,KAAK,KAAK,KAAK,CAAC,MAAM,EAAE,4BAA6B,IAAK,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QAE7F,MAAM,MAAM,GAAsB,EAAE,CAAC;QACrC,KAAK,CAAC,OAAO,CAAC,UAAS,KAAK;YACxB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QACH,OAAO,IAAA,mBAAQ,EAAC,IAAA,iBAAM,EAAC,MAAM,CAAC,CAAC,CAAC;KACnC;IAED,IAAA,yBAAc,EAAC,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;AACvD,CAAC;AAED,oBAAoB;AAEpB;;;;;;;;GAQG;AACH,SAAgB,cAAc,CAAC,KAA4B,EAAE,MAA0B;IACnF,IAAA,yBAAc,EAAC,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE,oDAAoD,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;IAEvH,MAAM,KAAK,GAAsB,EAAE,CAAC;IACpC,KAAK,CAAC,OAAO,CAAC,UAAS,IAAI,EAAE,KAAK;QAC9B,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;IACH,OAAO,IAAA,kBAAO,EAAC,IAAA,iBAAM,EAAC,KAAK,CAAC,CAAC,CAAC;AAClC,CAAC;AARD,wCAQC;AAED;;;;;;;;GAQG;AACH,SAAgB,uBAAuB,CAAC,KAA4B,EAAE,MAA0B;IAC5F,OAAO,IAAA,oBAAU,EAAC,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AACrD,CAAC;AAFD,0DAEC;AAED;;;;;;;;GAQG;AACH,SAAgB,oBAAoB,CAAC,KAA4B,EAAE,MAA0B;IACzF,OAAO,IAAA,iBAAO,EAAC,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAClD,CAAC;AAFD,oDAEC"}