{"version": 3, "file": "lang-zh.js", "sourceRoot": "", "sources": ["../../src.ts/wordlists/lang-zh.ts"], "names": [], "mappings": ";;;AAAA,+CAAsC;AACtC,gDAAiE;AAEjE,+CAAyC;AAGzC,MAAM,IAAI,GAAG,kgMAAkgM,CAAC;AAChhM,MAAM,SAAS,GAAG,6lDAA6lD,CAAC;AAGhnD,MAAM,SAAS,GAAyC;IACpD,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;CACd,CAAA;AAED,MAAM,MAAM,GAA2B;IACnC,KAAK,EAAE,oEAAoE;IAC3E,KAAK,EAAE,oEAAoE;CAC9E,CAAA;AAED,MAAM,KAAK,GAAG,kEAAkE,CAAC;AACjF,MAAM,KAAK,GAAG,4BAA4B,CAAA;AAE1C,SAAS,SAAS,CAAC,MAAc;IAC7B,IAAI,SAAS,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;QAAE,OAAO,SAAS,CAAC,MAAM,CAAkB,CAAC;KAAE;IAE7E,MAAM,QAAQ,GAAkB,EAAE,CAAC;IAEnC,IAAI,WAAW,GAAG,CAAC,CAAC;IACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;QAC3B,MAAM,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrC,MAAM,KAAK,GAAG;YACV,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;YACd,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YACpC,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;SACvC,CAAC;QAEF,IAAI,MAAM,KAAK,OAAO,EAAE;YACpB,MAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;YACrB,KAAK,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC7B,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA,CAAC,CAAC,GAAG,CAAC,CAAC;aAC9E;SACJ;QAED,QAAQ,CAAC,IAAI,CAAC,IAAA,uBAAY,EAAC,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KACtD;IAED,qDAAqD;IACrD,MAAM,QAAQ,GAAG,IAAA,aAAE,EAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChD,qBAAqB;IACrB,IAAI,QAAQ,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE;QAC7B,MAAM,IAAI,KAAK,CAAC,sBAAuB,MAAO,mBAAmB,CAAC,CAAC;KACtE;IACD,oBAAoB;IAEpB,SAAS,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;IAE7B,OAAO,QAAQ,CAAC;AACpB,CAAC;AAED,MAAM,SAAS,GAA2B,EAAG,CAAC;AAE9C;;;;;GAKG;AACH,MAAa,MAAO,SAAQ,sBAAQ;IAEhC;;;;;;;;;OASG;IACH,YAAY,OAAe,IAAI,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IAExD,OAAO,CAAC,KAAa;QACjB,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrC,IAAA,yBAAc,EAAC,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,EAC7C,uBAAwB,KAAM,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QACtD,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC;IACxB,CAAC;IAED,YAAY,CAAC,IAAY;QACrB,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,MAAc;QAChB,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QAC9C,OAAO,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,QAAQ,CAAC,OAAe;QAC3B,IAAI,SAAS,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE;YAC5B,SAAS,CAAC,OAAO,CAAC,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;SAC5C;QACD,OAAO,SAAS,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;CACJ;AA3CD,wBA2CC"}